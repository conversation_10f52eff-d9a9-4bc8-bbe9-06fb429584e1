import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  <PERSON>alog<PERSON><PERSON>,
  But<PERSON>,
  Stepper,
  Step,
  StepLabel,
  Box,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  IconButton,
  Chip,
  Divider,
  Card,
  CardContent,
  Grid,
  TextField,
  FormControlLabel,
  Switch,
} from '@mui/material';
import {
  Folder as FolderIcon,
  Image as ImageIcon,
  DragIndicator as DragIcon,
  Link as LinkIcon,
  LinkOff as UnlinkIcon,
  AutoAwesome as AutoIcon,
} from '@mui/icons-material';

export interface ImportedFile {
  file: File;
  path: string;
  layerName: string;
  subfolderPath: string;
  suggestedName: string;
}

export interface LayerStructure {
  name: string;
  order: number;
  files: ImportedFile[];
  subfolders: string[];
  relations: string[];
  isEnabled: boolean;
}

export interface TraitRelation {
  masterTraitId: string;
  slaveTraitId: string;
  relationType: 'requires' | 'excludes' | 'enhances';
  confidence: number;
}

interface ImportWizardProps {
  open: boolean;
  onClose: () => void;
  files: File[];
  onImport: (layers: LayerStructure[], relations: TraitRelation[]) => void;
}

const steps = [
  'Analyze Structure',
  'Layer Ordering',
  'Layer Relations',
  'Trait Relations',
  'Confirm Import'
];

export const ImportWizard: React.FC<ImportWizardProps> = ({
  open,
  onClose,
  files,
  onImport,
}) => {
  const [activeStep, setActiveStep] = useState(0);
  const [layers, setLayers] = useState<LayerStructure[]>([]);
  const [traitRelations, setTraitRelations] = useState<TraitRelation[]>([]);
  const [contentAwareEnabled, setContentAwareEnabled] = useState(true);

  // Step 1: Analyze folder structure
  useEffect(() => {
    if (files.length > 0) {
      analyzeStructure();
    }
  }, [files]);

  const analyzeStructure = () => {
    const layerMap: Record<string, ImportedFile[]> = {};

    files.forEach(file => {
      if (!isImageFile(file)) return;

      const pathParts = file.webkitRelativePath?.split('/') || [file.name];
      let layerName: string;
      let subfolderPath = '';

      if (pathParts.length <= 2) {
        layerName = 'Default Layer';
      } else {
        layerName = pathParts[1]; // Skip root folder
        subfolderPath = pathParts.slice(2, -1).join('/');
      }

      const importedFile: ImportedFile = {
        file,
        path: file.webkitRelativePath || file.name,
        layerName,
        subfolderPath,
        suggestedName: cleanFileName(file.name),
      };

      if (!layerMap[layerName]) {
        layerMap[layerName] = [];
      }
      layerMap[layerName].push(importedFile);
    });

    // Convert to LayerStructure array
    const analyzedLayers: LayerStructure[] = Object.entries(layerMap).map(([name, files], index) => {
      const subfolders = [...new Set(files.map(f => f.subfolderPath).filter(Boolean))];
      
      return {
        name: extractLayerName(name),
        order: extractLayerOrder(name, index),
        files,
        subfolders,
        relations: [],
        isEnabled: true,
      };
    });

    // Sort by order
    analyzedLayers.sort((a, b) => a.order - b.order);

    setLayers(analyzedLayers);

    // Auto-detect relations if content-aware is enabled
    if (contentAwareEnabled) {
      detectContentRelations(analyzedLayers);
    }
  };

  const extractLayerName = (folderName: string): string => {
    // Remove order prefix: "1-Background" -> "Background"
    const match = folderName.match(/^\d+-(.+)$/);
    return match ? match[1] : folderName;
  };

  const extractLayerOrder = (folderName: string, defaultOrder: number): number => {
    // Extract order from folder name: "1-Background" -> 1
    const match = folderName.match(/^(\d+)-/);
    return match ? parseInt(match[1], 10) : defaultOrder;
  };

  const detectContentRelations = (layerStructures: LayerStructure[]) => {
    const relations: TraitRelation[] = [];

    // Content-aware analysis
    for (let i = 0; i < layerStructures.length; i++) {
      for (let j = i + 1; j < layerStructures.length; j++) {
        const layer1 = layerStructures[i];
        const layer2 = layerStructures[j];

        // Check for naming patterns
        const layer1Relations = findNamingRelations(layer1, layer2);
        relations.push(...layer1Relations);
      }
    }

    setTraitRelations(relations);
  };

  const findNamingRelations = (layer1: LayerStructure, layer2: LayerStructure): TraitRelation[] => {
    const relations: TraitRelation[] = [];

    // Look for common naming patterns
    layer1.files.forEach(file1 => {
      layer2.files.forEach(file2 => {
        const similarity = calculateNameSimilarity(file1.suggestedName, file2.suggestedName);
        
        if (similarity > 0.7) {
          relations.push({
            masterTraitId: `${layer1.name}-${file1.suggestedName}`,
            slaveTraitId: `${layer2.name}-${file2.suggestedName}`,
            relationType: 'requires',
            confidence: similarity,
          });
        }
      });
    });

    return relations;
  };

  const calculateNameSimilarity = (name1: string, name2: string): number => {
    const words1 = name1.toLowerCase().split(/\s+/);
    const words2 = name2.toLowerCase().split(/\s+/);
    
    let commonWords = 0;
    words1.forEach(word1 => {
      if (words2.some(word2 => word2.includes(word1) || word1.includes(word2))) {
        commonWords++;
      }
    });

    return commonWords / Math.max(words1.length, words2.length);
  };

  const handleNext = () => {
    setActiveStep(prev => prev + 1);
  };

  const handleBack = () => {
    setActiveStep(prev => prev - 1);
  };

  const handleImport = () => {
    onImport(layers, traitRelations);
    onClose();
  };

  const moveLayer = (fromIndex: number, toIndex: number) => {
    const newLayers = [...layers];
    const [movedLayer] = newLayers.splice(fromIndex, 1);
    newLayers.splice(toIndex, 0, movedLayer);
    
    // Update order values
    newLayers.forEach((layer, index) => {
      layer.order = index;
    });
    
    setLayers(newLayers);
  };

  const toggleLayerRelation = (layer1Index: number, layer2Index: number) => {
    const newLayers = [...layers];
    const layer1 = newLayers[layer1Index];
    const layer2Name = newLayers[layer2Index].name;

    if (layer1.relations.includes(layer2Name)) {
      layer1.relations = layer1.relations.filter(name => name !== layer2Name);
    } else {
      layer1.relations.push(layer2Name);
    }

    setLayers(newLayers);
  };

  const renderStepContent = () => {
    switch (activeStep) {
      case 0:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Folder Structure Analysis
            </Typography>
            <FormControlLabel
              control={
                <Switch
                  checked={contentAwareEnabled}
                  onChange={(e) => setContentAwareEnabled(e.target.checked)}
                />
              }
              label="Enable Content-Aware Analysis"
            />
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Detected {layers.length} layers with {files.length} files
            </Typography>
            
            <List>
              {layers.map((layer, index) => (
                <ListItem key={index}>
                  <ListItemIcon>
                    <FolderIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary={layer.name}
                    secondary={`${layer.files.length} files • Order: ${layer.order}`}
                  />
                  <Chip label={`${layer.files.length} traits`} size="small" />
                </ListItem>
              ))}
            </List>
          </Box>
        );

      case 1:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Layer Ordering
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Drag layers to reorder them. Top layers will appear above bottom layers.
            </Typography>
            
            <List>
              {layers.map((layer, index) => (
                <ListItem
                  key={layer.name}
                  sx={{
                    border: 1,
                    borderColor: 'divider',
                    borderRadius: 1,
                    mb: 1,
                    cursor: 'grab',
                  }}
                >
                  <ListItemIcon>
                    <DragIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary={layer.name}
                    secondary={`${layer.files.length} files`}
                  />
                  <Typography variant="body2" color="text.secondary">
                    Order: {index + 1}
                  </Typography>
                </ListItem>
              ))}
            </List>
          </Box>
        );

      case 2:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Layer Relations
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Define which layers are related to each other (e.g., Hair & Hat).
            </Typography>
            
            <Grid container spacing={2}>
              {layers.map((layer, index) => (
                <Grid item xs={12} sm={6} key={layer.name}>
                  <Card>
                    <CardContent>
                      <Typography variant="subtitle1" gutterBottom>
                        {layer.name}
                      </Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                        {layers.map((otherLayer, otherIndex) => {
                          if (index === otherIndex) return null;
                          
                          const isRelated = layer.relations.includes(otherLayer.name);
                          
                          return (
                            <Chip
                              key={otherLayer.name}
                              label={otherLayer.name}
                              variant={isRelated ? 'filled' : 'outlined'}
                              color={isRelated ? 'primary' : 'default'}
                              icon={isRelated ? <LinkIcon /> : <UnlinkIcon />}
                              onClick={() => toggleLayerRelation(index, otherIndex)}
                              size="small"
                            />
                          );
                        })}
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Box>
        );

      case 3:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Trait Relations
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Content-aware analysis found {traitRelations.length} potential trait relationships.
            </Typography>
            
            {traitRelations.length > 0 ? (
              <List>
                {traitRelations.map((relation, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      <AutoIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary={`${relation.masterTraitId} → ${relation.slaveTraitId}`}
                      secondary={`${relation.relationType} • Confidence: ${(relation.confidence * 100).toFixed(0)}%`}
                    />
                    <Chip
                      label={relation.relationType}
                      size="small"
                      color="secondary"
                    />
                  </ListItem>
                ))}
              </List>
            ) : (
              <Typography variant="body2" color="text.secondary">
                No automatic trait relations detected.
              </Typography>
            )}
          </Box>
        );

      case 4:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Confirm Import
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Review your import settings before proceeding.
            </Typography>
            
            <Card sx={{ mb: 2 }}>
              <CardContent>
                <Typography variant="subtitle2" gutterBottom>
                  Summary
                </Typography>
                <Typography variant="body2">
                  • {layers.length} layers will be imported
                </Typography>
                <Typography variant="body2">
                  • {files.length} total files
                </Typography>
                <Typography variant="body2">
                  • {traitRelations.length} trait relations detected
                </Typography>
              </CardContent>
            </Card>
          </Box>
        );

      default:
        return null;
    }
  };

  const isImageFile = (file: File): boolean => {
    const supportedTypes = [
      'image/png',
      'image/jpeg',
      'image/jpg',
      'image/webp',
      'image/gif',
      'image/svg+xml'
    ];
    return supportedTypes.includes(file.type.toLowerCase());
  };

  const cleanFileName = (fileName: string): string => {
    const nameWithoutExt = fileName.replace(/\.[^/.]+$/, '');
    return nameWithoutExt
      .replace(/[-_]/g, ' ')
      .replace(/\b\w/g, l => l.toUpperCase())
      .trim();
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { height: '80vh' }
      }}
    >
      <DialogTitle>
        Import Wizard
      </DialogTitle>
      
      <DialogContent>
        <Stepper activeStep={activeStep} sx={{ mb: 3 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>
        
        {renderStepContent()}
      </DialogContent>
      
      <DialogActions>
        <Button onClick={onClose}>
          Cancel
        </Button>
        <Button
          disabled={activeStep === 0}
          onClick={handleBack}
        >
          Back
        </Button>
        {activeStep === steps.length - 1 ? (
          <Button
            variant="contained"
            onClick={handleImport}
          >
            Import
          </Button>
        ) : (
          <Button
            variant="contained"
            onClick={handleNext}
          >
            Next
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default ImportWizard;
