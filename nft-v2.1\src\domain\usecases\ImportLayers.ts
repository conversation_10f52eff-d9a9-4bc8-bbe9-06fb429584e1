import { Layer, LayerSettings } from '@/domain/entities/Layer';
import { Trait, TraitMetadata, TraitFile, TraitRarity } from '@/domain/entities/Trait';
import { ILayerRepository } from '@/domain/repositories/ILayerRepository';
import { ITraitRepository } from '@/domain/repositories/ITraitRepository';

export interface ImportLayersRequest {
  projectId: string;
  directoryHandle?: FileSystemDirectoryHandle;
  files?: File[];
}

export interface ImportLayersResponse {
  success: boolean;
  layers: Layer[];
  errors: string[];
  warnings: string[];
}

export interface ImportProgress {
  currentLayer: string;
  processedFiles: number;
  totalFiles: number;
  percentage: number;
}

const SUPPORTED_IMAGE_TYPES = [
  'image/png',
  'image/jpeg',
  'image/jpg',
  'image/webp',
  'image/gif',
  'image/svg+xml'
];

export class ImportLayers {
  constructor(
    private layerRepository: ILayerRepository,
    private traitRepository: ITraitRepository
  ) {}

  async execute(
    request: ImportLayersRequest,
    onProgress?: (progress: ImportProgress) => void
  ): Promise<ImportLayersResponse> {
    try {
      const errors: string[] = [];
      const warnings: string[] = [];
      const layers: Layer[] = [];

      // Validate input
      if (!request.directoryHandle && !request.files) {
        return {
          success: false,
          layers: [],
          errors: ['No directory or files provided for import'],
          warnings: [],
        };
      }

      let folderStructure: FolderStructure;

      if (request.directoryHandle) {
        // Use File System Access API
        folderStructure = await this.scanDirectoryHandle(request.directoryHandle);
      } else if (request.files) {
        // Use traditional file input
        folderStructure = await this.scanFileList(request.files);
      } else {
        throw new Error('Invalid import request');
      }

      // Process each layer folder
      let processedFiles = 0;
      const totalFiles = this.countTotalFiles(folderStructure);

      for (const [layerName, layerData] of Object.entries(folderStructure.layers)) {
        try {
          onProgress?.({
            currentLayer: layerName,
            processedFiles,
            totalFiles,
            percentage: (processedFiles / totalFiles) * 100,
          });

          const layer = await this.createLayerFromData(
            request.projectId,
            layerName,
            layerData,
            layers.length
          );

          if (layer.traitIds.length === 0) {
            warnings.push(`Layer "${layerName}" has no valid image files`);
          } else {
            layers.push(layer);
          }

          processedFiles += layerData.files.length;
        } catch (error) {
          errors.push(`Failed to process layer "${layerName}": ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      // Save layers to repository
      const savedLayers: Layer[] = [];
      for (const layer of layers) {
        try {
          const savedLayer = await this.layerRepository.create(layer);
          savedLayers.push(savedLayer);
        } catch (error) {
          errors.push(`Failed to save layer "${layer.settings.name}": ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      return {
        success: savedLayers.length > 0,
        layers: savedLayers,
        errors,
        warnings,
      };

    } catch (error) {
      return {
        success: false,
        layers: [],
        errors: [`Import failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
        warnings: [],
      };
    }
  }

  private async scanDirectoryHandle(directoryHandle: FileSystemDirectoryHandle): Promise<FolderStructure> {
    const structure: FolderStructure = { layers: {} };

    for await (const [name, handle] of directoryHandle.entries()) {
      if (handle.kind === 'directory') {
        // This is a layer folder
        const layerData: LayerData = {
          name,
          files: [],
          subfolders: {},
        };

        await this.scanLayerDirectory(handle, layerData);
        structure.layers[name] = layerData;
      }
    }

    return structure;
  }

  private async scanLayerDirectory(
    directoryHandle: FileSystemDirectoryHandle,
    layerData: LayerData,
    subfolderPath: string = ''
  ): Promise<void> {
    for await (const [name, handle] of directoryHandle.entries()) {
      if (handle.kind === 'file') {
        const file = await handle.getFile();
        if (this.isImageFile(file)) {
          layerData.files.push({
            file,
            name,
            subfolderPath,
          });
        }
      } else if (handle.kind === 'directory') {
        // Recursive scan for subfolders
        const newSubfolderPath = subfolderPath ? `${subfolderPath}/${name}` : name;
        layerData.subfolders[name] = {
          name,
          files: [],
          subfolders: {},
        };
        
        await this.scanLayerDirectory(handle, layerData.subfolders[name], newSubfolderPath);
      }
    }
  }

  private async scanFileList(files: File[]): Promise<FolderStructure> {
    const structure: FolderStructure = { layers: {} };

    for (const file of files) {
      if (!this.isImageFile(file)) continue;

      // Parse file path to extract layer and subfolder structure
      const pathParts = file.webkitRelativePath?.split('/') || [file.name];
      
      if (pathParts.length < 2) {
        // File is in root, skip or create default layer
        continue;
      }

      const layerName = pathParts[0];
      const fileName = pathParts[pathParts.length - 1];
      const subfolderPath = pathParts.length > 2 ? 
        pathParts.slice(1, -1).join('/') : '';

      // Initialize layer if not exists
      if (!structure.layers[layerName]) {
        structure.layers[layerName] = {
          name: layerName,
          files: [],
          subfolders: {},
        };
      }

      structure.layers[layerName].files.push({
        file,
        name: fileName,
        subfolderPath,
      });
    }

    return structure;
  }

  private async createLayerFromData(
    projectId: string,
    layerName: string,
    layerData: LayerData,
    order: number
  ): Promise<Layer> {
    // Extract order from layer name if it follows pattern "1-Background"
    const orderMatch = layerName.match(/^(\d+)-(.+)$/);
    const actualOrder = orderMatch ? parseInt(orderMatch[1], 10) : order;
    const cleanName = orderMatch ? orderMatch[2] : layerName;

    const layerSettings: LayerSettings = {
      name: cleanName,
      description: `Imported from folder: ${layerName}`,
      isVisible: true,
      isLocked: false,
      opacity: 100,
      blendMode: 'normal',
      zIndex: actualOrder,
    };

    const layer = new Layer(projectId, layerSettings);

    // Process all files in the layer
    const allFiles = this.getAllFilesFromLayerData(layerData);
    
    for (const fileData of allFiles) {
      try {
        const trait = await this.createTraitFromFile(
          layer.id,
          projectId,
          fileData
        );

        const savedTrait = await this.traitRepository.create(trait);
        layer.addTrait(savedTrait.id);
      } catch (error) {
        console.error(`Failed to create trait from file ${fileData.name}:`, error);
      }
    }

    // Auto-distribute rarity evenly
    if (layer.traitIds.length > 0) {
      await this.traitRepository.autoDistributeRarity(layer.id);
    }

    return layer;
  }

  private getAllFilesFromLayerData(layerData: LayerData): FileData[] {
    const files: FileData[] = [...layerData.files];

    // Recursively collect files from subfolders
    for (const subfolder of Object.values(layerData.subfolders)) {
      files.push(...this.getAllFilesFromLayerData(subfolder));
    }

    return files;
  }

  private async createTraitFromFile(
    layerId: string,
    projectId: string,
    fileData: FileData
  ): Promise<Trait> {
    const { file, name, subfolderPath } = fileData;

    // Create trait metadata
    const metadata: TraitMetadata = {
      name: this.cleanFileName(name),
      description: `Imported from: ${subfolderPath ? `${subfolderPath}/` : ''}${name}`,
      category: subfolderPath || 'Default',
      tags: subfolderPath ? subfolderPath.split('/') : [],
      attributes: {},
    };

    // Create trait file info
    const traitFile: TraitFile = {
      name,
      path: `${subfolderPath ? `${subfolderPath}/` : ''}${name}`,
      size: file.size,
      type: file.type,
      lastModified: new Date(file.lastModified),
      blob: file,
    };

    return new Trait(layerId, projectId, metadata, traitFile);
  }

  private isImageFile(file: File): boolean {
    return SUPPORTED_IMAGE_TYPES.includes(file.type.toLowerCase());
  }

  private cleanFileName(fileName: string): string {
    // Remove extension and clean up the name
    const nameWithoutExt = fileName.replace(/\.[^/.]+$/, '');
    return nameWithoutExt
      .replace(/[-_]/g, ' ')
      .replace(/\b\w/g, l => l.toUpperCase())
      .trim();
  }

  private countTotalFiles(structure: FolderStructure): number {
    let count = 0;
    for (const layer of Object.values(structure.layers)) {
      count += this.countFilesInLayerData(layer);
    }
    return count;
  }

  private countFilesInLayerData(layerData: LayerData): number {
    let count = layerData.files.length;
    for (const subfolder of Object.values(layerData.subfolders)) {
      count += this.countFilesInLayerData(subfolder);
    }
    return count;
  }
}

// Helper interfaces
interface FolderStructure {
  layers: Record<string, LayerData>;
}

interface LayerData {
  name: string;
  files: FileData[];
  subfolders: Record<string, LayerData>;
}

interface FileData {
  file: File;
  name: string;
  subfolderPath: string;
}

export default ImportLayers;
