{"version": 3, "file": "core.esm.js", "sources": ["../src/components/DndMonitor/context.ts", "../src/components/DndMonitor/useDndMonitor.ts", "../src/components/DndMonitor/useDndMonitorProvider.tsx", "../src/components/Accessibility/defaults.ts", "../src/components/Accessibility/Accessibility.tsx", "../src/store/actions.ts", "../src/utilities/other/noop.ts", "../src/sensors/useSensor.ts", "../src/sensors/useSensors.ts", "../src/utilities/coordinates/constants.ts", "../src/utilities/coordinates/distanceBetweenPoints.ts", "../src/utilities/coordinates/getRelativeTransformOrigin.ts", "../src/utilities/algorithms/helpers.ts", "../src/utilities/algorithms/closestCenter.ts", "../src/utilities/algorithms/closestCorners.ts", "../src/utilities/algorithms/rectIntersection.ts", "../src/utilities/algorithms/pointerWithin.ts", "../src/utilities/rect/adjustScale.ts", "../src/utilities/rect/getRectDelta.ts", "../src/utilities/rect/rectAdjustment.ts", "../src/utilities/transform/parseTransform.ts", "../src/utilities/transform/inverseTransform.ts", "../src/utilities/rect/getRect.ts", "../src/utilities/rect/getWindowClientRect.ts", "../src/utilities/scroll/isFixed.ts", "../src/utilities/scroll/isScrollable.ts", "../src/utilities/scroll/getScrollableAncestors.ts", "../src/utilities/scroll/getScrollableElement.ts", "../src/utilities/scroll/getScrollCoordinates.ts", "../src/types/direction.ts", "../src/utilities/scroll/documentScrollingElement.ts", "../src/utilities/scroll/getScrollPosition.ts", "../src/utilities/scroll/getScrollDirectionAndSpeed.ts", "../src/utilities/scroll/getScrollElementRect.ts", "../src/utilities/scroll/getScrollOffsets.ts", "../src/utilities/scroll/scrollIntoViewIfNeeded.ts", "../src/utilities/rect/Rect.ts", "../src/sensors/utilities/Listeners.ts", "../src/sensors/utilities/getEventListenerTarget.ts", "../src/sensors/utilities/hasExceededDistance.ts", "../src/sensors/events.ts", "../src/sensors/keyboard/types.ts", "../src/sensors/keyboard/defaults.ts", "../src/sensors/keyboard/KeyboardSensor.ts", "../src/sensors/pointer/AbstractPointerSensor.ts", "../src/sensors/pointer/PointerSensor.ts", "../src/sensors/mouse/MouseSensor.ts", "../src/sensors/touch/TouchSensor.ts", "../src/hooks/utilities/useAutoScroller.ts", "../src/hooks/utilities/useCachedNode.ts", "../src/hooks/utilities/useCombineActivators.ts", "../src/hooks/utilities/useDroppableMeasuring.ts", "../src/hooks/utilities/useInitialValue.ts", "../src/hooks/utilities/useInitialRect.ts", "../src/hooks/utilities/useMutationObserver.ts", "../src/hooks/utilities/useResizeObserver.ts", "../src/hooks/utilities/useRect.ts", "../src/hooks/utilities/useRectDelta.ts", "../src/hooks/utilities/useScrollableAncestors.ts", "../src/hooks/utilities/useScrollOffsets.ts", "../src/hooks/utilities/useScrollOffsetsDelta.ts", "../src/hooks/utilities/useSensorSetup.ts", "../src/hooks/utilities/useSyntheticListeners.ts", "../src/hooks/utilities/useWindowRect.ts", "../src/hooks/utilities/useRects.ts", "../src/utilities/nodes/getMeasurableNode.ts", "../src/hooks/utilities/useDragOverlayMeasuring.ts", "../src/components/DndContext/defaults.ts", "../src/store/constructors.ts", "../src/store/context.ts", "../src/store/reducer.ts", "../src/components/Accessibility/components/RestoreFocus.tsx", "../src/modifiers/applyModifiers.ts", "../src/components/DndContext/hooks/useMeasuringConfiguration.ts", "../src/components/DndContext/hooks/useLayoutShiftScrollCompensation.ts", "../src/components/DndContext/DndContext.tsx", "../src/hooks/useDraggable.ts", "../src/hooks/useDndContext.ts", "../src/hooks/useDroppable.ts", "../src/components/DragOverlay/components/AnimationManager/AnimationManager.tsx", "../src/components/DragOverlay/components/NullifiedContextProvider/NullifiedContextProvider.tsx", "../src/components/DragOverlay/components/PositionedOverlay/PositionedOverlay.tsx", "../src/components/DragOverlay/hooks/useDropAnimation.ts", "../src/components/DragOverlay/hooks/useKey.ts", "../src/components/DragOverlay/DragOverlay.tsx"], "sourcesContent": ["import {createContext} from 'react';\n\nimport type {RegisterListener} from './types';\n\nexport const DndMonitorContext = createContext<RegisterListener | null>(null);\n", "import {useContext, useEffect} from 'react';\n\nimport {DndMonitorContext} from './context';\nimport type {DndMonitorListener} from './types';\n\nexport function useDndMonitor(listener: DndMonitorListener) {\n  const registerListener = useContext(DndMonitorContext);\n\n  useEffect(() => {\n    if (!registerListener) {\n      throw new Error(\n        'useDndMonitor must be used within a children of <DndContext>'\n      );\n    }\n\n    const unsubscribe = registerListener(listener);\n\n    return unsubscribe;\n  }, [listener, registerListener]);\n}\n", "import {useCallback, useState} from 'react';\n\nimport type {DndMonitorListener, DndMonitorEvent} from './types';\n\nexport function useDndMonitorProvider() {\n  const [listeners] = useState(() => new Set<DndMonitorListener>());\n\n  const registerListener = useCallback(\n    (listener) => {\n      listeners.add(listener);\n      return () => listeners.delete(listener);\n    },\n    [listeners]\n  );\n\n  const dispatch = useCallback(\n    ({type, event}: DndMonitorEvent) => {\n      listeners.forEach((listener) => listener[type]?.(event as any));\n    },\n    [listeners]\n  );\n\n  return [dispatch, registerListener] as const;\n}\n", "import type {Announcements, ScreenReaderInstructions} from './types';\n\nexport const defaultScreenReaderInstructions: ScreenReaderInstructions = {\n  draggable: `\n    To pick up a draggable item, press the space bar.\n    While dragging, use the arrow keys to move the item.\n    Press space again to drop the item in its new position, or press escape to cancel.\n  `,\n};\n\nexport const defaultAnnouncements: Announcements = {\n  onDragStart({active}) {\n    return `Picked up draggable item ${active.id}.`;\n  },\n  onDragOver({active, over}) {\n    if (over) {\n      return `Draggable item ${active.id} was moved over droppable area ${over.id}.`;\n    }\n\n    return `Draggable item ${active.id} is no longer over a droppable area.`;\n  },\n  onDragEnd({active, over}) {\n    if (over) {\n      return `Draggable item ${active.id} was dropped over droppable area ${over.id}`;\n    }\n\n    return `Draggable item ${active.id} was dropped.`;\n  },\n  onDragCancel({active}) {\n    return `Dragging was cancelled. Draggable item ${active.id} was dropped.`;\n  },\n};\n", "import React, {useEffect, useMemo, useState} from 'react';\nimport {createPortal} from 'react-dom';\nimport {useUniqueId} from '@dnd-kit/utilities';\nimport {HiddenText, LiveRegion, useAnnouncement} from '@dnd-kit/accessibility';\n\nimport {DndMonitorListener, useDndMonitor} from '../DndMonitor';\n\nimport type {Announcements, ScreenReaderInstructions} from './types';\nimport {\n  defaultAnnouncements,\n  defaultScreenReaderInstructions,\n} from './defaults';\n\ninterface Props {\n  announcements?: Announcements;\n  container?: Element;\n  screenReaderInstructions?: ScreenReaderInstructions;\n  hiddenTextDescribedById: string;\n}\n\nexport function Accessibility({\n  announcements = defaultAnnouncements,\n  container,\n  hiddenTextDescribedById,\n  screenReaderInstructions = defaultScreenReaderInstructions,\n}: Props) {\n  const {announce, announcement} = useAnnouncement();\n  const liveRegionId = useUniqueId(`DndLiveRegion`);\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  useDndMonitor(\n    useMemo<DndMonitorListener>(\n      () => ({\n        onDragStart({active}) {\n          announce(announcements.onDragStart({active}));\n        },\n        onDragMove({active, over}) {\n          if (announcements.onDragMove) {\n            announce(announcements.onDragMove({active, over}));\n          }\n        },\n        onDragOver({active, over}) {\n          announce(announcements.onDragOver({active, over}));\n        },\n        onDragEnd({active, over}) {\n          announce(announcements.onDragEnd({active, over}));\n        },\n        onDragCancel({active, over}) {\n          announce(announcements.onDragCancel({active, over}));\n        },\n      }),\n      [announce, announcements]\n    )\n  );\n\n  if (!mounted) {\n    return null;\n  }\n\n  const markup = (\n    <>\n      <HiddenText\n        id={hiddenTextDescribedById}\n        value={screenReaderInstructions.draggable}\n      />\n      <LiveRegion id={liveRegionId} announcement={announcement} />\n    </>\n  );\n\n  return container ? createPortal(markup, container) : markup;\n}\n", "import type {Coordinates, UniqueIdentifier} from '../types';\nimport type {DroppableContainer} from './types';\n\nexport enum Action {\n  DragStart = 'dragStart',\n  DragMove = 'dragMove',\n  DragEnd = 'dragEnd',\n  DragCancel = 'dragCancel',\n  DragOver = 'dragOver',\n  RegisterDroppable = 'registerDroppable',\n  SetDroppableDisabled = 'setDroppableDisabled',\n  UnregisterDroppable = 'unregisterDroppable',\n}\n\nexport type Actions =\n  | {\n      type: Action.DragStart;\n      active: UniqueIdentifier;\n      initialCoordinates: Coordinates;\n    }\n  | {type: Action.DragMove; coordinates: Coordinates}\n  | {type: Action.DragEnd}\n  | {type: Action.DragCancel}\n  | {\n      type: Action.RegisterDroppable;\n      element: DroppableContainer;\n    }\n  | {\n      type: Action.SetDroppableDisabled;\n      id: UniqueIdentifier;\n      key: UniqueIdentifier;\n      disabled: boolean;\n    }\n  | {\n      type: Action.UnregisterDroppable;\n      id: UniqueIdentifier;\n      key: UniqueIdentifier;\n    };\n", "export function noop(..._args: any) {}\n", "import {useMemo} from 'react';\n\nimport type {Sensor, SensorDescriptor, SensorOptions} from './types';\n\nexport function useSensor<T extends SensorOptions>(\n  sensor: Sensor<T>,\n  options?: T\n): SensorDescriptor<T> {\n  return useMemo(\n    () => ({\n      sensor,\n      options: options ?? ({} as T),\n    }),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [sensor, options]\n  );\n}\n", "import {useMemo} from 'react';\n\nimport type {SensorDescriptor, SensorOptions} from './types';\n\nexport function useSensors(\n  ...sensors: (SensorDescriptor<any> | undefined | null)[]\n): SensorDescriptor<SensorOptions>[] {\n  return useMemo(\n    () =>\n      [...sensors].filter(\n        (sensor): sensor is SensorDescriptor<any> => sensor != null\n      ),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [...sensors]\n  );\n}\n", "import type {Coordinates} from '../../types';\n\nexport const defaultCoordinates: Coordinates = Object.freeze({\n  x: 0,\n  y: 0,\n});\n", "import type {Coordinates} from '../../types';\n\n/**\n * Returns the distance between two points\n */\nexport function distanceBetween(p1: Coordinates, p2: Coordinates) {\n  return Math.sqrt(Math.pow(p1.x - p2.x, 2) + Math.pow(p1.y - p2.y, 2));\n}\n", "import {getEventCoordinates} from '@dnd-kit/utilities';\nimport type {ClientRect} from '../../types';\n\nexport function getRelativeTransformOrigin(\n  event: MouseEvent | TouchEvent | KeyboardEvent,\n  rect: ClientRect\n) {\n  const eventCoordinates = getEventCoordinates(event);\n\n  if (!eventCoordinates) {\n    return '0 0';\n  }\n\n  const transformOrigin = {\n    x: ((eventCoordinates.x - rect.left) / rect.width) * 100,\n    y: ((eventCoordinates.y - rect.top) / rect.height) * 100,\n  };\n\n  return `${transformOrigin.x}% ${transformOrigin.y}%`;\n}\n", "/* eslint-disable no-redeclare */\nimport type {ClientRect} from '../../types';\n\nimport type {Collision, CollisionDescriptor} from './types';\n\n/**\n * Sort collisions from smallest to greatest value\n */\nexport function sortCollisionsAsc(\n  {data: {value: a}}: CollisionDescriptor,\n  {data: {value: b}}: CollisionDescriptor\n) {\n  return a - b;\n}\n\n/**\n * Sort collisions from greatest to smallest value\n */\nexport function sortCollisionsDesc(\n  {data: {value: a}}: CollisionDescriptor,\n  {data: {value: b}}: CollisionDescriptor\n) {\n  return b - a;\n}\n\n/**\n * Returns the coordinates of the corners of a given rectangle:\n * [TopLeft {x, y}, TopRight {x, y}, BottomLeft {x, y}, BottomRight {x, y}]\n */\nexport function cornersOfRectangle({left, top, height, width}: ClientRect) {\n  return [\n    {\n      x: left,\n      y: top,\n    },\n    {\n      x: left + width,\n      y: top,\n    },\n    {\n      x: left,\n      y: top + height,\n    },\n    {\n      x: left + width,\n      y: top + height,\n    },\n  ];\n}\n\n/**\n * Returns the first collision, or null if there isn't one.\n * If a property is specified, returns the specified property of the first collision.\n */\nexport function getFirstCollision(\n  collisions: Collision[] | null | undefined\n): Collision | null;\nexport function getFirstCollision<T extends keyof Collision>(\n  collisions: Collision[] | null | undefined,\n  property: T\n): Collision[T] | null;\nexport function getFirstCollision(\n  collisions: Collision[] | null | undefined,\n  property?: keyof Collision\n) {\n  if (!collisions || collisions.length === 0) {\n    return null;\n  }\n\n  const [firstCollision] = collisions;\n\n  return property ? firstCollision[property] : firstCollision;\n}\n", "import {distanceBetween} from '../coordinates';\nimport type {Coordinates, ClientRect} from '../../types';\n\nimport type {CollisionDescriptor, CollisionDetection} from './types';\nimport {sortCollisionsAsc} from './helpers';\n\n/**\n * Returns the coordinates of the center of a given ClientRect\n */\nfunction centerOfRectangle(\n  rect: ClientRect,\n  left = rect.left,\n  top = rect.top\n): Coordinates {\n  return {\n    x: left + rect.width * 0.5,\n    y: top + rect.height * 0.5,\n  };\n}\n\n/**\n * Returns the closest rectangles from an array of rectangles to the center of a given\n * rectangle.\n */\nexport const closestCenter: CollisionDetection = ({\n  collisionRect,\n  droppableRects,\n  droppableContainers,\n}) => {\n  const centerRect = centerOfRectangle(\n    collisionRect,\n    collisionRect.left,\n    collisionRect.top\n  );\n  const collisions: CollisionDescriptor[] = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {id} = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect) {\n      const distBetween = distanceBetween(centerOfRectangle(rect), centerRect);\n\n      collisions.push({id, data: {droppableContainer, value: distBetween}});\n    }\n  }\n\n  return collisions.sort(sortCollisionsAsc);\n};\n", "import {distanceBetween} from '../coordinates';\n\nimport type {CollisionDescriptor, CollisionDetection} from './types';\nimport {cornersOfRectangle, sortCollisionsAsc} from './helpers';\n\n/**\n * Returns the closest rectangles from an array of rectangles to the corners of\n * another rectangle.\n */\nexport const closestCorners: CollisionDetection = ({\n  collisionRect,\n  droppableRects,\n  droppableContainers,\n}) => {\n  const corners = cornersOfRectangle(collisionRect);\n  const collisions: CollisionDescriptor[] = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {id} = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect) {\n      const rectCorners = cornersOfRectangle(rect);\n      const distances = corners.reduce((accumulator, corner, index) => {\n        return accumulator + distanceBetween(rectCorners[index], corner);\n      }, 0);\n      const effectiveDistance = Number((distances / 4).toFixed(4));\n\n      collisions.push({\n        id,\n        data: {droppableContainer, value: effectiveDistance},\n      });\n    }\n  }\n\n  return collisions.sort(sortCollisionsAsc);\n};\n", "import type {ClientRect} from '../../types';\n\nimport type {CollisionDescriptor, CollisionDetection} from './types';\nimport {sortCollisionsDesc} from './helpers';\n\n/**\n * Returns the intersecting rectangle area between two rectangles\n */\nexport function getIntersectionRatio(\n  entry: ClientRect,\n  target: ClientRect\n): number {\n  const top = Math.max(target.top, entry.top);\n  const left = Math.max(target.left, entry.left);\n  const right = Math.min(target.left + target.width, entry.left + entry.width);\n  const bottom = Math.min(target.top + target.height, entry.top + entry.height);\n  const width = right - left;\n  const height = bottom - top;\n\n  if (left < right && top < bottom) {\n    const targetArea = target.width * target.height;\n    const entryArea = entry.width * entry.height;\n    const intersectionArea = width * height;\n    const intersectionRatio =\n      intersectionArea / (targetArea + entryArea - intersectionArea);\n\n    return Number(intersectionRatio.toFixed(4));\n  }\n\n  // Rectangles do not overlap, or overlap has an area of zero (edge/corner overlap)\n  return 0;\n}\n\n/**\n * Returns the rectangles that has the greatest intersection area with a given\n * rectangle in an array of rectangles.\n */\nexport const rectIntersection: CollisionDetection = ({\n  collisionRect,\n  droppableRects,\n  droppableContainers,\n}) => {\n  const collisions: CollisionDescriptor[] = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {id} = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect) {\n      const intersectionRatio = getIntersectionRatio(rect, collisionRect);\n\n      if (intersectionRatio > 0) {\n        collisions.push({\n          id,\n          data: {droppableContainer, value: intersectionRatio},\n        });\n      }\n    }\n  }\n\n  return collisions.sort(sortCollisionsDesc);\n};\n", "import type {Coordinates, ClientRect} from '../../types';\nimport {distanceBetween} from '../coordinates';\n\nimport type {CollisionDescriptor, CollisionDetection} from './types';\nimport {cornersOfRectangle, sortCollisionsAsc} from './helpers';\n\n/**\n * Check if a given point is contained within a bounding rectangle\n */\nfunction isPointWithinRect(point: Coordinates, rect: ClientRect): boolean {\n  const {top, left, bottom, right} = rect;\n\n  return (\n    top <= point.y && point.y <= bottom && left <= point.x && point.x <= right\n  );\n}\n\n/**\n * Returns the rectangles that the pointer is hovering over\n */\nexport const pointerWithin: CollisionDetection = ({\n  droppableContainers,\n  droppableRects,\n  pointerCoordinates,\n}) => {\n  if (!pointerCoordinates) {\n    return [];\n  }\n\n  const collisions: CollisionDescriptor[] = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {id} = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect && isPointWithinRect(pointerCoordinates, rect)) {\n      /* There may be more than a single rectangle intersecting\n       * with the pointer coordinates. In order to sort the\n       * colliding rectangles, we measure the distance between\n       * the pointer and the corners of the intersecting rectangle\n       */\n      const corners = cornersOfRectangle(rect);\n      const distances = corners.reduce((accumulator, corner) => {\n        return accumulator + distanceBetween(pointerCoordinates, corner);\n      }, 0);\n      const effectiveDistance = Number((distances / 4).toFixed(4));\n\n      collisions.push({\n        id,\n        data: {droppableContainer, value: effectiveDistance},\n      });\n    }\n  }\n\n  return collisions.sort(sortCollisionsAsc);\n};\n", "import type {Transform} from '@dnd-kit/utilities';\nimport type {ClientRect} from '../../types';\n\nexport function adjustScale(\n  transform: Transform,\n  rect1: ClientRect | null,\n  rect2: ClientRect | null\n): Transform {\n  return {\n    ...transform,\n    scaleX: rect1 && rect2 ? rect1.width / rect2.width : 1,\n    scaleY: rect1 && rect2 ? rect1.height / rect2.height : 1,\n  };\n}\n", "import type {Coordinates, ClientRect} from '../../types';\nimport {defaultCoordinates} from '../coordinates';\n\nexport function getRectDelta(\n  rect1: ClientRect | null,\n  rect2: ClientRect | null\n): Coordinates {\n  return rect1 && rect2\n    ? {\n        x: rect1.left - rect2.left,\n        y: rect1.top - rect2.top,\n      }\n    : defaultCoordinates;\n}\n", "import type {Coordinates, ClientRect} from '../../types';\n\nexport function createRectAdjustmentFn(modifier: number) {\n  return function adjustClientRect(\n    rect: ClientRect,\n    ...adjustments: Coordinates[]\n  ): ClientRect {\n    return adjustments.reduce<ClientRect>(\n      (acc, adjustment) => ({\n        ...acc,\n        top: acc.top + modifier * adjustment.y,\n        bottom: acc.bottom + modifier * adjustment.y,\n        left: acc.left + modifier * adjustment.x,\n        right: acc.right + modifier * adjustment.x,\n      }),\n      {...rect}\n    );\n  };\n}\n\nexport const getAdjustedRect = createRectAdjustmentFn(1);\n", "import type {Transform} from '@dnd-kit/utilities';\n\nexport function parseTransform(transform: string): Transform | null {\n  if (transform.startsWith('matrix3d(')) {\n    const transformArray = transform.slice(9, -1).split(/, /);\n\n    return {\n      x: +transformArray[12],\n      y: +transformArray[13],\n      scaleX: +transformArray[0],\n      scaleY: +transformArray[5],\n    };\n  } else if (transform.startsWith('matrix(')) {\n    const transformArray = transform.slice(7, -1).split(/, /);\n\n    return {\n      x: +transformArray[4],\n      y: +transformArray[5],\n      scaleX: +transformArray[0],\n      scaleY: +transformArray[3],\n    };\n  }\n\n  return null;\n}\n", "import type {ClientRect} from '../../types';\n\nimport {parseTransform} from './parseTransform';\n\nexport function inverseTransform(\n  rect: ClientRect,\n  transform: string,\n  transformOrigin: string\n): ClientRect {\n  const parsedTransform = parseTransform(transform);\n\n  if (!parsedTransform) {\n    return rect;\n  }\n\n  const {scaleX, scaleY, x: translateX, y: translateY} = parsedTransform;\n\n  const x = rect.left - translateX - (1 - scaleX) * parseFloat(transformOrigin);\n  const y =\n    rect.top -\n    translateY -\n    (1 - scaleY) *\n      parseFloat(transformOrigin.slice(transformOrigin.indexOf(' ') + 1));\n  const w = scaleX ? rect.width / scaleX : rect.width;\n  const h = scaleY ? rect.height / scaleY : rect.height;\n\n  return {\n    width: w,\n    height: h,\n    top: y,\n    right: x + w,\n    bottom: y + h,\n    left: x,\n  };\n}\n", "import {getWindow} from '@dnd-kit/utilities';\n\nimport type {ClientRect} from '../../types';\nimport {inverseTransform} from '../transform';\n\ninterface Options {\n  ignoreTransform?: boolean;\n}\n\nconst defaultOptions: Options = {ignoreTransform: false};\n\n/**\n * Returns the bounding client rect of an element relative to the viewport.\n */\nexport function getClientRect(\n  element: Element,\n  options: Options = defaultOptions\n) {\n  let rect: ClientRect = element.getBoundingClientRect();\n\n  if (options.ignoreTransform) {\n    const {transform, transformOrigin} =\n      getWindow(element).getComputedStyle(element);\n\n    if (transform) {\n      rect = inverseTransform(rect, transform, transformOrigin);\n    }\n  }\n\n  const {top, left, width, height, bottom, right} = rect;\n\n  return {\n    top,\n    left,\n    width,\n    height,\n    bottom,\n    right,\n  };\n}\n\n/**\n * Returns the bounding client rect of an element relative to the viewport.\n *\n * @remarks\n * The ClientRect returned by this method does not take into account transforms\n * applied to the element it measures.\n *\n */\nexport function getTransformAgnosticClientRect(element: Element): ClientRect {\n  return getClientRect(element, {ignoreTransform: true});\n}\n", "import type {ClientRect} from '../../types';\n\nexport function getWindowClientRect(element: typeof window): ClientRect {\n  const width = element.innerWidth;\n  const height = element.innerHeight;\n\n  return {\n    top: 0,\n    left: 0,\n    right: width,\n    bottom: height,\n    width,\n    height,\n  };\n}\n", "import {getWindow} from '@dnd-kit/utilities';\n\nexport function isFixed(\n  node: HTMLElement,\n  computedStyle: CSSStyleDeclaration = getWindow(node).getComputedStyle(node)\n): boolean {\n  return computedStyle.position === 'fixed';\n}\n", "import {getWindow} from '@dnd-kit/utilities';\n\nexport function isScrollable(\n  element: HTMLElement,\n  computedStyle: CSSStyleDeclaration = getWindow(element).getComputedStyle(\n    element\n  )\n): boolean {\n  const overflowRegex = /(auto|scroll|overlay)/;\n  const properties = ['overflow', 'overflowX', 'overflowY'];\n\n  return properties.some((property) => {\n    const value = computedStyle[property as keyof CSSStyleDeclaration];\n\n    return typeof value === 'string' ? overflowRegex.test(value) : false;\n  });\n}\n", "import {\n  getWindow,\n  isDocument,\n  isHTMLElement,\n  isSVGElement,\n} from '@dnd-kit/utilities';\n\nimport {isFixed} from './isFixed';\nimport {isScrollable} from './isScrollable';\n\nexport function getScrollableAncestors(\n  element: Node | null,\n  limit?: number\n): Element[] {\n  const scrollParents: Element[] = [];\n\n  function findScrollableAncestors(node: Node | null): Element[] {\n    if (limit != null && scrollParents.length >= limit) {\n      return scrollParents;\n    }\n\n    if (!node) {\n      return scrollParents;\n    }\n\n    if (\n      isDocument(node) &&\n      node.scrollingElement != null &&\n      !scrollParents.includes(node.scrollingElement)\n    ) {\n      scrollParents.push(node.scrollingElement);\n\n      return scrollParents;\n    }\n\n    if (!isHTMLElement(node) || isSVGElement(node)) {\n      return scrollParents;\n    }\n\n    if (scrollParents.includes(node)) {\n      return scrollParents;\n    }\n\n    const computedStyle = getWindow(element).getComputedStyle(node);\n\n    if (node !== element) {\n      if (isScrollable(node, computedStyle)) {\n        scrollParents.push(node);\n      }\n    }\n\n    if (isFixed(node, computedStyle)) {\n      return scrollParents;\n    }\n\n    return findScrollableAncestors(node.parentNode);\n  }\n\n  if (!element) {\n    return scrollParents;\n  }\n\n  return findScrollableAncestors(element);\n}\n\nexport function getFirstScrollableAncestor(node: Node | null): Element | null {\n  const [firstScrollableAncestor] = getScrollableAncestors(node, 1);\n\n  return firstScrollableAncestor ?? null;\n}\n", "import {\n  canUseDOM,\n  isHTMLElement,\n  isDocument,\n  getOwnerDocument,\n  isNode,\n  isWindow,\n} from '@dnd-kit/utilities';\n\nexport function getScrollableElement(element: EventTarget | null) {\n  if (!canUseDOM || !element) {\n    return null;\n  }\n\n  if (isWindow(element)) {\n    return element;\n  }\n\n  if (!isNode(element)) {\n    return null;\n  }\n\n  if (\n    isDocument(element) ||\n    element === getOwnerDocument(element).scrollingElement\n  ) {\n    return window;\n  }\n\n  if (isHTMLElement(element)) {\n    return element;\n  }\n\n  return null;\n}\n", "import {isWindow} from '@dnd-kit/utilities';\n\nimport type {Coordinates} from '../../types';\n\nexport function getScrollXCoordinate(element: Element | typeof window): number {\n  if (isWindow(element)) {\n    return element.scrollX;\n  }\n\n  return element.scrollLeft;\n}\n\nexport function getScrollYCoordinate(element: Element | typeof window): number {\n  if (isWindow(element)) {\n    return element.scrollY;\n  }\n\n  return element.scrollTop;\n}\n\nexport function getScrollCoordinates(\n  element: Element | typeof window\n): Coordinates {\n  return {\n    x: getScrollXCoordinate(element),\n    y: getScrollYCoordinate(element),\n  };\n}\n", "export enum Direction {\n  Forward = 1,\n  Backward = -1,\n}\n", "import {canUseDOM} from '@dnd-kit/utilities';\n\nexport function isDocumentScrollingElement(element: Element | null) {\n  if (!canUseDOM || !element) {\n    return false;\n  }\n\n  return element === document.scrollingElement;\n}\n", "import {isDocumentScrollingElement} from './documentScrollingElement';\n\nexport function getScrollPosition(scrollingContainer: Element) {\n  const minScroll = {\n    x: 0,\n    y: 0,\n  };\n  const dimensions = isDocumentScrollingElement(scrollingContainer)\n    ? {\n        height: window.innerHeight,\n        width: window.innerWidth,\n      }\n    : {\n        height: scrollingContainer.clientHeight,\n        width: scrollingContainer.clientWidth,\n      };\n  const maxScroll = {\n    x: scrollingContainer.scrollWidth - dimensions.width,\n    y: scrollingContainer.scrollHeight - dimensions.height,\n  };\n\n  const isTop = scrollingContainer.scrollTop <= minScroll.y;\n  const isLeft = scrollingContainer.scrollLeft <= minScroll.x;\n  const isBottom = scrollingContainer.scrollTop >= maxScroll.y;\n  const isRight = scrollingContainer.scrollLeft >= maxScroll.x;\n\n  return {\n    isTop,\n    isLeft,\n    isBottom,\n    isRight,\n    maxScroll,\n    minScroll,\n  };\n}\n", "import {Direction, ClientRect} from '../../types';\nimport {getScrollPosition} from './getScrollPosition';\n\ninterface PositionalCoordinates\n  extends Pick<ClientRect, 'top' | 'left' | 'right' | 'bottom'> {}\n\nconst defaultThreshold = {\n  x: 0.2,\n  y: 0.2,\n};\n\nexport function getScrollDirectionAndSpeed(\n  scrollContainer: Element,\n  scrollContainerRect: ClientRect,\n  {top, left, right, bottom}: PositionalCoordinates,\n  acceleration = 10,\n  thresholdPercentage = defaultThreshold\n) {\n  const {isTop, isBottom, isLeft, isRight} = getScrollPosition(scrollContainer);\n\n  const direction = {\n    x: 0,\n    y: 0,\n  };\n  const speed = {\n    x: 0,\n    y: 0,\n  };\n  const threshold = {\n    height: scrollContainerRect.height * thresholdPercentage.y,\n    width: scrollContainerRect.width * thresholdPercentage.x,\n  };\n\n  if (!isTop && top <= scrollContainerRect.top + threshold.height) {\n    // Scroll Up\n    direction.y = Direction.Backward;\n    speed.y =\n      acceleration *\n      Math.abs(\n        (scrollContainerRect.top + threshold.height - top) / threshold.height\n      );\n  } else if (\n    !isBottom &&\n    bottom >= scrollContainerRect.bottom - threshold.height\n  ) {\n    // Scroll Down\n    direction.y = Direction.Forward;\n    speed.y =\n      acceleration *\n      Math.abs(\n        (scrollContainerRect.bottom - threshold.height - bottom) /\n          threshold.height\n      );\n  }\n\n  if (!isRight && right >= scrollContainerRect.right - threshold.width) {\n    // Scroll Right\n    direction.x = Direction.Forward;\n    speed.x =\n      acceleration *\n      Math.abs(\n        (scrollContainerRect.right - threshold.width - right) / threshold.width\n      );\n  } else if (!isLeft && left <= scrollContainerRect.left + threshold.width) {\n    // Scroll Left\n    direction.x = Direction.Backward;\n    speed.x =\n      acceleration *\n      Math.abs(\n        (scrollContainerRect.left + threshold.width - left) / threshold.width\n      );\n  }\n\n  return {\n    direction,\n    speed,\n  };\n}\n", "export function getScrollElementRect(element: Element) {\n  if (element === document.scrollingElement) {\n    const {innerWidth, innerHeight} = window;\n\n    return {\n      top: 0,\n      left: 0,\n      right: innerWidth,\n      bottom: innerHeight,\n      width: innerWidth,\n      height: innerHeight,\n    };\n  }\n\n  const {top, left, right, bottom} = element.getBoundingClientRect();\n\n  return {\n    top,\n    left,\n    right,\n    bottom,\n    width: element.clientWidth,\n    height: element.clientHeight,\n  };\n}\n", "import {add} from '@dnd-kit/utilities';\n\nimport type {Coordinates} from '../../types';\nimport {\n  getScrollCoordinates,\n  getScrollXCoordinate,\n  getScrollYCoordinate,\n} from './getScrollCoordinates';\nimport {defaultCoordinates} from '../coordinates';\n\nexport function getScrollOffsets(scrollableAncestors: Element[]): Coordinates {\n  return scrollableAncestors.reduce<Coordinates>((acc, node) => {\n    return add(acc, getScrollCoordinates(node));\n  }, defaultCoordinates);\n}\n\nexport function getScrollXOffset(scrollableAncestors: Element[]): number {\n  return scrollableAncestors.reduce<number>((acc, node) => {\n    return acc + getScrollXCoordinate(node);\n  }, 0);\n}\n\nexport function getScrollYOffset(scrollableAncestors: Element[]): number {\n  return scrollableAncestors.reduce<number>((acc, node) => {\n    return acc + getScrollYCoordinate(node);\n  }, 0);\n}\n", "import type {ClientRect} from '../../types';\nimport {getClientRect} from '../rect/getRect';\nimport {getFirstScrollableAncestor} from './getScrollableAncestors';\n\nexport function scrollIntoViewIfNeeded(\n  element: HTMLElement | null | undefined,\n  measure: (node: HTMLElement) => ClientRect = getClientRect\n) {\n  if (!element) {\n    return;\n  }\n\n  const {top, left, bottom, right} = measure(element);\n  const firstScrollableAncestor = getFirstScrollableAncestor(element);\n\n  if (!firstScrollableAncestor) {\n    return;\n  }\n\n  if (\n    bottom <= 0 ||\n    right <= 0 ||\n    top >= window.innerHeight ||\n    left >= window.innerWidth\n  ) {\n    element.scrollIntoView({\n      block: 'center',\n      inline: 'center',\n    });\n  }\n}\n", "import type {ClientRect} from '../../types/rect';\nimport {\n  getScrollableAncestors,\n  getScrollOffsets,\n  getScrollXOffset,\n  getScrollYOffset,\n} from '../scroll';\n\nconst properties = [\n  ['x', ['left', 'right'], getScrollXOffset],\n  ['y', ['top', 'bottom'], getScrollYOffset],\n] as const;\n\nexport class Rect {\n  constructor(rect: ClientRect, element: Element) {\n    const scrollableAncestors = getScrollableAncestors(element);\n    const scrollOffsets = getScrollOffsets(scrollableAncestors);\n\n    this.rect = {...rect};\n    this.width = rect.width;\n    this.height = rect.height;\n\n    for (const [axis, keys, getScrollOffset] of properties) {\n      for (const key of keys) {\n        Object.defineProperty(this, key, {\n          get: () => {\n            const currentOffsets = getScrollOffset(scrollableAncestors);\n            const scrollOffsetsDeltla = scrollOffsets[axis] - currentOffsets;\n\n            return this.rect[key] + scrollOffsetsDeltla;\n          },\n          enumerable: true,\n        });\n      }\n    }\n\n    Object.defineProperty(this, 'rect', {enumerable: false});\n  }\n\n  private rect: ClientRect;\n\n  public width: number;\n\n  public height: number;\n\n  // The below properties are set by the `Object.defineProperty` calls in the constructor\n  // @ts-ignore\n  public top: number;\n  // @ts-ignore\n  public bottom: number;\n  // @ts-ignore\n  public right: number;\n  // @ts-ignore\n  public left: number;\n}\n", "export class Listeners {\n  private listeners: [\n    string,\n    EventListenerOrEventListenerObject,\n    AddEventListenerOptions | boolean | undefined\n  ][] = [];\n\n  constructor(private target: EventTarget | null) {}\n\n  public add<T extends Event>(\n    eventName: string,\n    handler: (event: T) => void,\n    options?: AddEventListenerOptions | boolean\n  ) {\n    this.target?.addEventListener(eventName, handler as EventListener, options);\n    this.listeners.push([eventName, handler as EventListener, options]);\n  }\n\n  public removeAll = () => {\n    this.listeners.forEach((listener) =>\n      this.target?.removeEventListener(...listener)\n    );\n  };\n}\n", "import {getOwnerDocument, getWindow} from '@dnd-kit/utilities';\n\nexport function getEventListenerTarget(\n  target: EventTarget | null\n): EventTarget | Document {\n  // If the `event.target` element is removed from the document events will still be targeted\n  // at it, and hence won't always bubble up to the window or document anymore.\n  // If there is any risk of an element being removed while it is being dragged,\n  // the best practice is to attach the event listeners directly to the target.\n  // https://developer.mozilla.org/en-US/docs/Web/API/EventTarget\n\n  const {EventTarget} = getWindow(target);\n\n  return target instanceof EventTarget ? target : getOwnerDocument(target);\n}\n", "import type {Coordinates, DistanceMeasurement} from '../../types';\n\nexport function hasExceededDistance(\n  delta: Coordinates,\n  measurement: DistanceMeasurement\n): boolean {\n  const dx = Math.abs(delta.x);\n  const dy = Math.abs(delta.y);\n\n  if (typeof measurement === 'number') {\n    return Math.sqrt(dx ** 2 + dy ** 2) > measurement;\n  }\n\n  if ('x' in measurement && 'y' in measurement) {\n    return dx > measurement.x && dy > measurement.y;\n  }\n\n  if ('x' in measurement) {\n    return dx > measurement.x;\n  }\n\n  if ('y' in measurement) {\n    return dy > measurement.y;\n  }\n\n  return false;\n}\n", "export enum EventName {\n  Click = 'click',\n  DragStart = 'dragstart',\n  Keydown = 'keydown',\n  ContextMenu = 'contextmenu',\n  Resize = 'resize',\n  SelectionChange = 'selectionchange',\n  VisibilityChange = 'visibilitychange',\n}\n\nexport function preventDefault(event: Event) {\n  event.preventDefault();\n}\n\nexport function stopPropagation(event: Event) {\n  event.stopPropagation();\n}\n", "import type {Coordinates, UniqueIdentifier} from '../../types';\nimport type {SensorContext} from '../types';\n\nexport enum KeyboardCode {\n  Space = 'Space',\n  Down = 'ArrowDown',\n  Right = 'ArrowRight',\n  Left = 'ArrowLeft',\n  Up = 'ArrowUp',\n  Esc = 'Escape',\n  Enter = 'Enter',\n  Tab = 'Tab',\n}\n\nexport type KeyboardCodes = {\n  start: KeyboardEvent['code'][];\n  cancel: KeyboardEvent['code'][];\n  end: KeyboardEvent['code'][];\n};\n\nexport type KeyboardCoordinateGetter = (\n  event: KeyboardEvent,\n  args: {\n    active: UniqueIdentifier;\n    currentCoordinates: Coordinates;\n    context: SensorContext;\n  }\n) => Coordinates | void;\n", "import {KeyboardCoordinateGetter, KeyboardCode, KeyboardCodes} from './types';\n\nexport const defaultKeyboardCodes: KeyboardCodes = {\n  start: [KeyboardCode.Space, KeyboardCode.Enter],\n  cancel: [KeyboardCode.Esc],\n  end: [KeyboardCode.Space, KeyboardCode.Enter, KeyboardCode.Tab],\n};\n\nexport const defaultKeyboardCoordinateGetter: KeyboardCoordinateGetter = (\n  event,\n  {currentCoordinates}\n) => {\n  switch (event.code) {\n    case KeyboardCode.Right:\n      return {\n        ...currentCoordinates,\n        x: currentCoordinates.x + 25,\n      };\n    case KeyboardCode.Left:\n      return {\n        ...currentCoordinates,\n        x: currentCoordinates.x - 25,\n      };\n    case KeyboardCode.Down:\n      return {\n        ...currentCoordinates,\n        y: currentCoordinates.y + 25,\n      };\n    case KeyboardCode.Up:\n      return {\n        ...currentCoordinates,\n        y: currentCoordinates.y - 25,\n      };\n  }\n\n  return undefined;\n};\n", "import {\n  add as getAdjustedCoordinates,\n  subtract as getCoordinates<PERSON><PERSON><PERSON>,\n  getOwnerDocument,\n  getWindow,\n  isKeyboardEvent,\n} from '@dnd-kit/utilities';\n\nimport type {Coordinates} from '../../types';\nimport {\n  defaultCoordinates,\n  getScrollPosition,\n  getScrollElementRect,\n} from '../../utilities';\nimport {scrollIntoViewIfNeeded} from '../../utilities/scroll';\nimport {EventName} from '../events';\nimport {Listeners} from '../utilities';\nimport type {\n  Activators,\n  SensorInstance,\n  SensorProps,\n  SensorOptions,\n} from '../types';\n\nimport {KeyboardCoordinateGetter, KeyboardCode, KeyboardCodes} from './types';\nimport {\n  defaultKeyboardCodes,\n  defaultKeyboardCoordinateGetter,\n} from './defaults';\n\nexport interface KeyboardSensorOptions extends SensorOptions {\n  keyboardCodes?: KeyboardCodes;\n  coordinateGetter?: KeyboardCoordinateGetter;\n  scrollBehavior?: ScrollBehavior;\n  onActivation?({event}: {event: KeyboardEvent}): void;\n}\n\nexport type KeyboardSensorProps = SensorProps<KeyboardSensorOptions>;\n\nexport class KeyboardSensor implements SensorInstance {\n  public autoScrollEnabled = false;\n  private referenceCoordinates: Coordinates | undefined;\n  private listeners: Listeners;\n  private windowListeners: Listeners;\n\n  constructor(private props: KeyboardSensorProps) {\n    const {\n      event: {target},\n    } = props;\n\n    this.props = props;\n    this.listeners = new Listeners(getOwnerDocument(target));\n    this.windowListeners = new Listeners(getWindow(target));\n    this.handleKeyDown = this.handleKeyDown.bind(this);\n    this.handleCancel = this.handleCancel.bind(this);\n\n    this.attach();\n  }\n\n  private attach() {\n    this.handleStart();\n\n    this.windowListeners.add(EventName.Resize, this.handleCancel);\n    this.windowListeners.add(EventName.VisibilityChange, this.handleCancel);\n\n    setTimeout(() => this.listeners.add(EventName.Keydown, this.handleKeyDown));\n  }\n\n  private handleStart() {\n    const {activeNode, onStart} = this.props;\n    const node = activeNode.node.current;\n\n    if (node) {\n      scrollIntoViewIfNeeded(node);\n    }\n\n    onStart(defaultCoordinates);\n  }\n\n  private handleKeyDown(event: Event) {\n    if (isKeyboardEvent(event)) {\n      const {active, context, options} = this.props;\n      const {\n        keyboardCodes = defaultKeyboardCodes,\n        coordinateGetter = defaultKeyboardCoordinateGetter,\n        scrollBehavior = 'smooth',\n      } = options;\n      const {code} = event;\n\n      if (keyboardCodes.end.includes(code)) {\n        this.handleEnd(event);\n        return;\n      }\n\n      if (keyboardCodes.cancel.includes(code)) {\n        this.handleCancel(event);\n        return;\n      }\n\n      const {collisionRect} = context.current;\n      const currentCoordinates = collisionRect\n        ? {x: collisionRect.left, y: collisionRect.top}\n        : defaultCoordinates;\n\n      if (!this.referenceCoordinates) {\n        this.referenceCoordinates = currentCoordinates;\n      }\n\n      const newCoordinates = coordinateGetter(event, {\n        active,\n        context: context.current,\n        currentCoordinates,\n      });\n\n      if (newCoordinates) {\n        const coordinatesDelta = getCoordinatesDelta(\n          newCoordinates,\n          currentCoordinates\n        );\n        const scrollDelta = {\n          x: 0,\n          y: 0,\n        };\n        const {scrollableAncestors} = context.current;\n\n        for (const scrollContainer of scrollableAncestors) {\n          const direction = event.code;\n          const {isTop, isRight, isLeft, isBottom, maxScroll, minScroll} =\n            getScrollPosition(scrollContainer);\n          const scrollElementRect = getScrollElementRect(scrollContainer);\n\n          const clampedCoordinates = {\n            x: Math.min(\n              direction === KeyboardCode.Right\n                ? scrollElementRect.right - scrollElementRect.width / 2\n                : scrollElementRect.right,\n              Math.max(\n                direction === KeyboardCode.Right\n                  ? scrollElementRect.left\n                  : scrollElementRect.left + scrollElementRect.width / 2,\n                newCoordinates.x\n              )\n            ),\n            y: Math.min(\n              direction === KeyboardCode.Down\n                ? scrollElementRect.bottom - scrollElementRect.height / 2\n                : scrollElementRect.bottom,\n              Math.max(\n                direction === KeyboardCode.Down\n                  ? scrollElementRect.top\n                  : scrollElementRect.top + scrollElementRect.height / 2,\n                newCoordinates.y\n              )\n            ),\n          };\n\n          const canScrollX =\n            (direction === KeyboardCode.Right && !isRight) ||\n            (direction === KeyboardCode.Left && !isLeft);\n          const canScrollY =\n            (direction === KeyboardCode.Down && !isBottom) ||\n            (direction === KeyboardCode.Up && !isTop);\n\n          if (canScrollX && clampedCoordinates.x !== newCoordinates.x) {\n            const newScrollCoordinates =\n              scrollContainer.scrollLeft + coordinatesDelta.x;\n            const canScrollToNewCoordinates =\n              (direction === KeyboardCode.Right &&\n                newScrollCoordinates <= maxScroll.x) ||\n              (direction === KeyboardCode.Left &&\n                newScrollCoordinates >= minScroll.x);\n\n            if (canScrollToNewCoordinates && !coordinatesDelta.y) {\n              // We don't need to update coordinates, the scroll adjustment alone will trigger\n              // logic to auto-detect the new container we are over\n              scrollContainer.scrollTo({\n                left: newScrollCoordinates,\n                behavior: scrollBehavior,\n              });\n              return;\n            }\n\n            if (canScrollToNewCoordinates) {\n              scrollDelta.x = scrollContainer.scrollLeft - newScrollCoordinates;\n            } else {\n              scrollDelta.x =\n                direction === KeyboardCode.Right\n                  ? scrollContainer.scrollLeft - maxScroll.x\n                  : scrollContainer.scrollLeft - minScroll.x;\n            }\n\n            if (scrollDelta.x) {\n              scrollContainer.scrollBy({\n                left: -scrollDelta.x,\n                behavior: scrollBehavior,\n              });\n            }\n            break;\n          } else if (canScrollY && clampedCoordinates.y !== newCoordinates.y) {\n            const newScrollCoordinates =\n              scrollContainer.scrollTop + coordinatesDelta.y;\n            const canScrollToNewCoordinates =\n              (direction === KeyboardCode.Down &&\n                newScrollCoordinates <= maxScroll.y) ||\n              (direction === KeyboardCode.Up &&\n                newScrollCoordinates >= minScroll.y);\n\n            if (canScrollToNewCoordinates && !coordinatesDelta.x) {\n              // We don't need to update coordinates, the scroll adjustment alone will trigger\n              // logic to auto-detect the new container we are over\n              scrollContainer.scrollTo({\n                top: newScrollCoordinates,\n                behavior: scrollBehavior,\n              });\n              return;\n            }\n\n            if (canScrollToNewCoordinates) {\n              scrollDelta.y = scrollContainer.scrollTop - newScrollCoordinates;\n            } else {\n              scrollDelta.y =\n                direction === KeyboardCode.Down\n                  ? scrollContainer.scrollTop - maxScroll.y\n                  : scrollContainer.scrollTop - minScroll.y;\n            }\n\n            if (scrollDelta.y) {\n              scrollContainer.scrollBy({\n                top: -scrollDelta.y,\n                behavior: scrollBehavior,\n              });\n            }\n\n            break;\n          }\n        }\n\n        this.handleMove(\n          event,\n          getAdjustedCoordinates(\n            getCoordinatesDelta(newCoordinates, this.referenceCoordinates),\n            scrollDelta\n          )\n        );\n      }\n    }\n  }\n\n  private handleMove(event: Event, coordinates: Coordinates) {\n    const {onMove} = this.props;\n\n    event.preventDefault();\n    onMove(coordinates);\n  }\n\n  private handleEnd(event: Event) {\n    const {onEnd} = this.props;\n\n    event.preventDefault();\n    this.detach();\n    onEnd();\n  }\n\n  private handleCancel(event: Event) {\n    const {onCancel} = this.props;\n\n    event.preventDefault();\n    this.detach();\n    onCancel();\n  }\n\n  private detach() {\n    this.listeners.removeAll();\n    this.windowListeners.removeAll();\n  }\n\n  static activators: Activators<KeyboardSensorOptions> = [\n    {\n      eventName: 'onKeyDown' as const,\n      handler: (\n        event: React.KeyboardEvent,\n        {keyboardCodes = defaultKeyboardCodes, onActivation},\n        {active}\n      ) => {\n        const {code} = event.nativeEvent;\n\n        if (keyboardCodes.start.includes(code)) {\n          const activator = active.activatorNode.current;\n\n          if (activator && event.target !== activator) {\n            return false;\n          }\n\n          event.preventDefault();\n\n          onActivation?.({event: event.nativeEvent});\n\n          return true;\n        }\n\n        return false;\n      },\n    },\n  ];\n}\n", "import {\n  subtract as getCoordina<PERSON><PERSON><PERSON><PERSON>,\n  getEventCoordinates,\n  getOwnerDocument,\n  getWindow,\n} from '@dnd-kit/utilities';\n\nimport {defaultCoordinates} from '../../utilities';\nimport {\n  getEventListenerTarget,\n  hasExceededDistance,\n  Listeners,\n} from '../utilities';\nimport {EventName, preventDefault, stopPropagation} from '../events';\nimport {KeyboardCode} from '../keyboard';\nimport type {SensorInstance, SensorProps, SensorOptions} from '../types';\nimport type {Coordinates, DistanceMeasurement} from '../../types';\n\ninterface DistanceConstraint {\n  distance: DistanceMeasurement;\n  tolerance?: DistanceMeasurement;\n}\n\ninterface DelayConstraint {\n  delay: number;\n  tolerance: DistanceMeasurement;\n}\n\ninterface EventDescriptor {\n  name: keyof DocumentEventMap;\n  passive?: boolean;\n}\n\nexport interface PointerEventHandlers {\n  cancel?: EventDescriptor;\n  move: EventDescriptor;\n  end: EventDescriptor;\n}\n\nexport type PointerActivationConstraint =\n  | DelayConstraint\n  | DistanceConstraint\n  | (DelayConstraint & DistanceConstraint);\n\nfunction isDistanceConstraint(\n  constraint: PointerActivationConstraint\n): constraint is PointerActivationConstraint & DistanceConstraint {\n  return Boolean(constraint && 'distance' in constraint);\n}\n\nfunction isDelayConstraint(\n  constraint: PointerActivationConstraint\n): constraint is DelayConstraint {\n  return Boolean(constraint && 'delay' in constraint);\n}\n\nexport interface AbstractPointerSensorOptions extends SensorOptions {\n  activationConstraint?: PointerActivationConstraint;\n  bypassActivationConstraint?(\n    props: Pick<AbstractPointerSensorProps, 'activeNode' | 'event' | 'options'>\n  ): boolean;\n  onActivation?({event}: {event: Event}): void;\n}\n\nexport type AbstractPointerSensorProps =\n  SensorProps<AbstractPointerSensorOptions>;\n\nexport class AbstractPointerSensor implements SensorInstance {\n  public autoScrollEnabled = true;\n  private document: Document;\n  private activated: boolean = false;\n  private initialCoordinates: Coordinates;\n  private timeoutId: NodeJS.Timeout | null = null;\n  private listeners: Listeners;\n  private documentListeners: Listeners;\n  private windowListeners: Listeners;\n\n  constructor(\n    private props: AbstractPointerSensorProps,\n    private events: PointerEventHandlers,\n    listenerTarget = getEventListenerTarget(props.event.target)\n  ) {\n    const {event} = props;\n    const {target} = event;\n\n    this.props = props;\n    this.events = events;\n    this.document = getOwnerDocument(target);\n    this.documentListeners = new Listeners(this.document);\n    this.listeners = new Listeners(listenerTarget);\n    this.windowListeners = new Listeners(getWindow(target));\n    this.initialCoordinates = getEventCoordinates(event) ?? defaultCoordinates;\n    this.handleStart = this.handleStart.bind(this);\n    this.handleMove = this.handleMove.bind(this);\n    this.handleEnd = this.handleEnd.bind(this);\n    this.handleCancel = this.handleCancel.bind(this);\n    this.handleKeydown = this.handleKeydown.bind(this);\n    this.removeTextSelection = this.removeTextSelection.bind(this);\n\n    this.attach();\n  }\n\n  private attach() {\n    const {\n      events,\n      props: {\n        options: {activationConstraint, bypassActivationConstraint},\n      },\n    } = this;\n\n    this.listeners.add(events.move.name, this.handleMove, {passive: false});\n    this.listeners.add(events.end.name, this.handleEnd);\n\n    if (events.cancel) {\n      this.listeners.add(events.cancel.name, this.handleCancel);\n    }\n\n    this.windowListeners.add(EventName.Resize, this.handleCancel);\n    this.windowListeners.add(EventName.DragStart, preventDefault);\n    this.windowListeners.add(EventName.VisibilityChange, this.handleCancel);\n    this.windowListeners.add(EventName.ContextMenu, preventDefault);\n    this.documentListeners.add(EventName.Keydown, this.handleKeydown);\n\n    if (activationConstraint) {\n      if (\n        bypassActivationConstraint?.({\n          event: this.props.event,\n          activeNode: this.props.activeNode,\n          options: this.props.options,\n        })\n      ) {\n        return this.handleStart();\n      }\n\n      if (isDelayConstraint(activationConstraint)) {\n        this.timeoutId = setTimeout(\n          this.handleStart,\n          activationConstraint.delay\n        );\n        this.handlePending(activationConstraint);\n        return;\n      }\n\n      if (isDistanceConstraint(activationConstraint)) {\n        this.handlePending(activationConstraint);\n        return;\n      }\n    }\n\n    this.handleStart();\n  }\n\n  private detach() {\n    this.listeners.removeAll();\n    this.windowListeners.removeAll();\n\n    // Wait until the next event loop before removing document listeners\n    // This is necessary because we listen for `click` and `selection` events on the document\n    setTimeout(this.documentListeners.removeAll, 50);\n\n    if (this.timeoutId !== null) {\n      clearTimeout(this.timeoutId);\n      this.timeoutId = null;\n    }\n  }\n\n  private handlePending(\n    constraint: PointerActivationConstraint,\n    offset?: Coordinates | undefined\n  ): void {\n    const {active, onPending} = this.props;\n    onPending(active, constraint, this.initialCoordinates, offset);\n  }\n\n  private handleStart() {\n    const {initialCoordinates} = this;\n    const {onStart} = this.props;\n\n    if (initialCoordinates) {\n      this.activated = true;\n\n      // Stop propagation of click events once activation constraints are met\n      this.documentListeners.add(EventName.Click, stopPropagation, {\n        capture: true,\n      });\n\n      // Remove any text selection from the document\n      this.removeTextSelection();\n\n      // Prevent further text selection while dragging\n      this.documentListeners.add(\n        EventName.SelectionChange,\n        this.removeTextSelection\n      );\n\n      onStart(initialCoordinates);\n    }\n  }\n\n  private handleMove(event: Event) {\n    const {activated, initialCoordinates, props} = this;\n    const {\n      onMove,\n      options: {activationConstraint},\n    } = props;\n\n    if (!initialCoordinates) {\n      return;\n    }\n\n    const coordinates = getEventCoordinates(event) ?? defaultCoordinates;\n    const delta = getCoordinatesDelta(initialCoordinates, coordinates);\n\n    // Constraint validation\n    if (!activated && activationConstraint) {\n      if (isDistanceConstraint(activationConstraint)) {\n        if (\n          activationConstraint.tolerance != null &&\n          hasExceededDistance(delta, activationConstraint.tolerance)\n        ) {\n          return this.handleCancel();\n        }\n\n        if (hasExceededDistance(delta, activationConstraint.distance)) {\n          return this.handleStart();\n        }\n      }\n\n      if (isDelayConstraint(activationConstraint)) {\n        if (hasExceededDistance(delta, activationConstraint.tolerance)) {\n          return this.handleCancel();\n        }\n      }\n\n      this.handlePending(activationConstraint, delta);\n      return;\n    }\n\n    if (event.cancelable) {\n      event.preventDefault();\n    }\n\n    onMove(coordinates);\n  }\n\n  private handleEnd() {\n    const {onAbort, onEnd} = this.props;\n\n    this.detach();\n    if (!this.activated) {\n      onAbort(this.props.active);\n    }\n    onEnd();\n  }\n\n  private handleCancel() {\n    const {onAbort, onCancel} = this.props;\n\n    this.detach();\n    if (!this.activated) {\n      onAbort(this.props.active);\n    }\n    onCancel();\n  }\n\n  private handleKeydown(event: KeyboardEvent) {\n    if (event.code === KeyboardCode.Esc) {\n      this.handleCancel();\n    }\n  }\n\n  private removeTextSelection() {\n    this.document.getSelection()?.removeAllRanges();\n  }\n}\n", "import type {PointerEvent} from 'react';\nimport {getOwnerDocument} from '@dnd-kit/utilities';\n\nimport type {SensorProps} from '../types';\nimport {\n  AbstractPointerSensor,\n  AbstractPointerSensorOptions,\n  PointerEventHandlers,\n} from './AbstractPointerSensor';\n\nconst events: PointerEventHandlers = {\n  cancel: {name: 'pointercancel'},\n  move: {name: 'pointermove'},\n  end: {name: 'pointerup'},\n};\n\nexport interface PointerSensorOptions extends AbstractPointerSensorOptions {}\n\nexport type PointerSensorProps = SensorProps<PointerSensorOptions>;\n\nexport class PointerSensor extends AbstractPointerSensor {\n  constructor(props: PointerSensorProps) {\n    const {event} = props;\n    // Pointer events stop firing if the target is unmounted while dragging\n    // Therefore we attach listeners to the owner document instead\n    const listenerTarget = getOwnerDocument(event.target);\n\n    super(props, events, listenerTarget);\n  }\n\n  static activators = [\n    {\n      eventName: 'onPointerDown' as const,\n      handler: (\n        {nativeEvent: event}: PointerEvent,\n        {onActivation}: PointerSensorOptions\n      ) => {\n        if (!event.isPrimary || event.button !== 0) {\n          return false;\n        }\n\n        onActivation?.({event});\n\n        return true;\n      },\n    },\n  ];\n}\n", "import type {MouseEvent} from 'react';\nimport {getOwnerDocument} from '@dnd-kit/utilities';\n\nimport type {SensorProps} from '../types';\nimport {\n  AbstractPointerSensor,\n  PointerEventHandlers,\n  AbstractPointerSensorOptions,\n} from '../pointer';\n\nconst events: PointerEventHandlers = {\n  move: {name: 'mousemove'},\n  end: {name: 'mouseup'},\n};\n\nenum MouseButton {\n  RightClick = 2,\n}\n\nexport interface MouseSensorOptions extends AbstractPointerSensorOptions {}\n\nexport type MouseSensorProps = SensorProps<MouseSensorOptions>;\n\nexport class MouseSensor extends AbstractPointerSensor {\n  constructor(props: MouseSensorProps) {\n    super(props, events, getOwnerDocument(props.event.target));\n  }\n\n  static activators = [\n    {\n      eventName: 'onMouseDown' as const,\n      handler: (\n        {nativeEvent: event}: MouseEvent,\n        {onActivation}: MouseSensorOptions\n      ) => {\n        if (event.button === MouseButton.RightClick) {\n          return false;\n        }\n\n        onActivation?.({event});\n\n        return true;\n      },\n    },\n  ];\n}\n", "import type {TouchEvent} from 'react';\n\nimport {\n  AbstractPointerSensor,\n  PointerSensorProps,\n  PointerEventHandlers,\n  PointerSensorOptions,\n} from '../pointer';\nimport type {SensorProps} from '../types';\n\nconst events: PointerEventHandlers = {\n  cancel: {name: 'touchcancel'},\n  move: {name: 'touchmove'},\n  end: {name: 'touchend'},\n};\n\nexport interface TouchSensorOptions extends PointerSensorOptions {}\n\nexport type TouchSensorProps = SensorProps<TouchSensorOptions>;\n\nexport class TouchSensor extends AbstractPointerSensor {\n  constructor(props: PointerSensorProps) {\n    super(props, events);\n  }\n\n  static activators = [\n    {\n      eventName: 'onTouchStart' as const,\n      handler: (\n        {nativeEvent: event}: TouchEvent,\n        {onActivation}: TouchSensorOptions\n      ) => {\n        const {touches} = event;\n\n        if (touches.length > 1) {\n          return false;\n        }\n\n        onActivation?.({event});\n\n        return true;\n      },\n    },\n  ];\n\n  static setup() {\n    // Adding a non-capture and non-passive `touchmove` listener in order\n    // to force `event.preventDefault()` calls to work in dynamically added\n    // touchmove event handlers. This is required for iOS Safari.\n    window.addEventListener(events.move.name, noop, {\n      capture: false,\n      passive: false,\n    });\n\n    return function teardown() {\n      window.removeEventListener(events.move.name, noop);\n    };\n\n    // We create a new handler because the teardown function of another sensor\n    // could remove our event listener if we use a referentially equal listener.\n    function noop() {}\n  }\n}\n", "import {useCallback, useEffect, useMemo, useRef} from 'react';\nimport {useInterval, useLazyMemo, usePrevious} from '@dnd-kit/utilities';\n\nimport {getScrollDirectionAndSpeed} from '../../utilities';\nimport {Direction} from '../../types';\nimport type {Coordinates, ClientRect} from '../../types';\n\nexport type ScrollAncestorSortingFn = (ancestors: Element[]) => Element[];\n\nexport enum AutoScrollActivator {\n  Pointer,\n  DraggableRect,\n}\n\nexport interface Options {\n  acceleration?: number;\n  activator?: AutoScrollActivator;\n  canScroll?: CanScroll;\n  enabled?: boolean;\n  interval?: number;\n  layoutShiftCompensation?:\n    | boolean\n    | {\n        x: boolean;\n        y: boolean;\n      };\n  order?: TraversalOrder;\n  threshold?: {\n    x: number;\n    y: number;\n  };\n}\n\ninterface Arguments extends Options {\n  draggingRect: ClientRect | null;\n  enabled: boolean;\n  pointerCoordinates: Coordinates | null;\n  scrollableAncestors: Element[];\n  scrollableAncestorRects: ClientRect[];\n  delta: Coordinates;\n}\n\nexport type CanScroll = (element: Element) => boolean;\n\nexport enum TraversalOrder {\n  TreeOrder,\n  ReversedTreeOrder,\n}\n\ninterface ScrollDirection {\n  x: 0 | Direction;\n  y: 0 | Direction;\n}\n\nexport function useAutoScroller({\n  acceleration,\n  activator = AutoScrollActivator.Pointer,\n  canScroll,\n  draggingRect,\n  enabled,\n  interval = 5,\n  order = TraversalOrder.TreeOrder,\n  pointerCoordinates,\n  scrollableAncestors,\n  scrollableAncestorRects,\n  delta,\n  threshold,\n}: Arguments) {\n  const scrollIntent = useScrollIntent({delta, disabled: !enabled});\n  const [setAutoScrollInterval, clearAutoScrollInterval] = useInterval();\n  const scrollSpeed = useRef<Coordinates>({x: 0, y: 0});\n  const scrollDirection = useRef<ScrollDirection>({x: 0, y: 0});\n  const rect = useMemo(() => {\n    switch (activator) {\n      case AutoScrollActivator.Pointer:\n        return pointerCoordinates\n          ? {\n              top: pointerCoordinates.y,\n              bottom: pointerCoordinates.y,\n              left: pointerCoordinates.x,\n              right: pointerCoordinates.x,\n            }\n          : null;\n      case AutoScrollActivator.DraggableRect:\n        return draggingRect;\n    }\n  }, [activator, draggingRect, pointerCoordinates]);\n  const scrollContainerRef = useRef<Element | null>(null);\n  const autoScroll = useCallback(() => {\n    const scrollContainer = scrollContainerRef.current;\n\n    if (!scrollContainer) {\n      return;\n    }\n\n    const scrollLeft = scrollSpeed.current.x * scrollDirection.current.x;\n    const scrollTop = scrollSpeed.current.y * scrollDirection.current.y;\n\n    scrollContainer.scrollBy(scrollLeft, scrollTop);\n  }, []);\n  const sortedScrollableAncestors = useMemo(\n    () =>\n      order === TraversalOrder.TreeOrder\n        ? [...scrollableAncestors].reverse()\n        : scrollableAncestors,\n    [order, scrollableAncestors]\n  );\n\n  useEffect(\n    () => {\n      if (!enabled || !scrollableAncestors.length || !rect) {\n        clearAutoScrollInterval();\n        return;\n      }\n\n      for (const scrollContainer of sortedScrollableAncestors) {\n        if (canScroll?.(scrollContainer) === false) {\n          continue;\n        }\n\n        const index = scrollableAncestors.indexOf(scrollContainer);\n        const scrollContainerRect = scrollableAncestorRects[index];\n\n        if (!scrollContainerRect) {\n          continue;\n        }\n\n        const {direction, speed} = getScrollDirectionAndSpeed(\n          scrollContainer,\n          scrollContainerRect,\n          rect,\n          acceleration,\n          threshold\n        );\n\n        for (const axis of ['x', 'y'] as const) {\n          if (!scrollIntent[axis][direction[axis] as Direction]) {\n            speed[axis] = 0;\n            direction[axis] = 0;\n          }\n        }\n\n        if (speed.x > 0 || speed.y > 0) {\n          clearAutoScrollInterval();\n\n          scrollContainerRef.current = scrollContainer;\n          setAutoScrollInterval(autoScroll, interval);\n\n          scrollSpeed.current = speed;\n          scrollDirection.current = direction;\n\n          return;\n        }\n      }\n\n      scrollSpeed.current = {x: 0, y: 0};\n      scrollDirection.current = {x: 0, y: 0};\n      clearAutoScrollInterval();\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n      acceleration,\n      autoScroll,\n      canScroll,\n      clearAutoScrollInterval,\n      enabled,\n      interval,\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      JSON.stringify(rect),\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      JSON.stringify(scrollIntent),\n      setAutoScrollInterval,\n      scrollableAncestors,\n      sortedScrollableAncestors,\n      scrollableAncestorRects,\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      JSON.stringify(threshold),\n    ]\n  );\n}\n\ninterface ScrollIntent {\n  x: Record<Direction, boolean>;\n  y: Record<Direction, boolean>;\n}\n\nconst defaultScrollIntent: ScrollIntent = {\n  x: {[Direction.Backward]: false, [Direction.Forward]: false},\n  y: {[Direction.Backward]: false, [Direction.Forward]: false},\n};\n\nfunction useScrollIntent({\n  delta,\n  disabled,\n}: {\n  delta: Coordinates;\n  disabled: boolean;\n}): ScrollIntent {\n  const previousDelta = usePrevious(delta);\n\n  return useLazyMemo<ScrollIntent>(\n    (previousIntent) => {\n      if (disabled || !previousDelta || !previousIntent) {\n        // Reset scroll intent tracking when auto-scrolling is disabled\n        return defaultScrollIntent;\n      }\n\n      const direction = {\n        x: Math.sign(delta.x - previousDelta.x),\n        y: Math.sign(delta.y - previousDelta.y),\n      };\n\n      // Keep track of the user intent to scroll in each direction for both axis\n      return {\n        x: {\n          [Direction.Backward]:\n            previousIntent.x[Direction.Backward] || direction.x === -1,\n          [Direction.Forward]:\n            previousIntent.x[Direction.Forward] || direction.x === 1,\n        },\n        y: {\n          [Direction.Backward]:\n            previousIntent.y[Direction.Backward] || direction.y === -1,\n          [Direction.Forward]:\n            previousIntent.y[Direction.Forward] || direction.y === 1,\n        },\n      };\n    },\n    [disabled, delta, previousDelta]\n  );\n}\n", "import {useLazyMemo} from '@dnd-kit/utilities';\n\nimport type {DraggableNode, DraggableNodes} from '../../store';\nimport type {UniqueIdentifier} from '../../types';\n\nexport function useCachedNode(\n  draggableNodes: DraggableNodes,\n  id: UniqueIdentifier | null\n): DraggableNode['node']['current'] {\n  const draggableNode = id != null ? draggableNodes.get(id) : undefined;\n  const node = draggableNode ? draggableNode.node.current : null;\n\n  return useLazyMemo(\n    (cachedNode) => {\n      if (id == null) {\n        return null;\n      }\n\n      // In some cases, the draggable node can unmount while dragging\n      // This is the case for virtualized lists. In those situations,\n      // we fall back to the last known value for that node.\n      return node ?? cachedNode ?? null;\n    },\n    [node, id]\n  );\n}\n", "import {useMemo} from 'react';\n\nimport type {SensorActivatorFunction, SensorDescriptor} from '../../sensors';\nimport type {\n  SyntheticListener,\n  SyntheticListeners,\n} from './useSyntheticListeners';\n\nexport function useCombineActivators(\n  sensors: SensorDescriptor<any>[],\n  getSyntheticHandler: (\n    handler: SensorActivatorFunction<any>,\n    sensor: SensorDescriptor<any>\n  ) => SyntheticListener['handler']\n): SyntheticListeners {\n  return useMemo(\n    () =>\n      sensors.reduce<SyntheticListeners>((accumulator, sensor) => {\n        const {sensor: Sensor} = sensor;\n\n        const sensorActivators = Sensor.activators.map((activator) => ({\n          eventName: activator.eventName,\n          handler: getSyntheticHandler(activator.handler, sensor),\n        }));\n\n        return [...accumulator, ...sensorActivators];\n      }, []),\n    [sensors, getSyntheticHandler]\n  );\n}\n", "import {useCallback, useEffect, useRef, useState} from 'react';\nimport {useLatestValue, useLazyMemo} from '@dnd-kit/utilities';\n\nimport {Rect} from '../../utilities/rect';\nimport type {DroppableContainer, RectMap} from '../../store/types';\nimport type {ClientRect, UniqueIdentifier} from '../../types';\n\ninterface Arguments {\n  dragging: boolean;\n  dependencies: any[];\n  config: DroppableMeasuring;\n}\n\nexport enum MeasuringStrategy {\n  Always,\n  BeforeDragging,\n  WhileDragging,\n}\n\nexport enum MeasuringFrequency {\n  Optimized = 'optimized',\n}\n\ntype MeasuringFunction = (element: HTMLElement) => ClientRect;\n\nexport interface DroppableMeasuring {\n  measure: MeasuringFunction;\n  strategy: MeasuringStrategy;\n  frequency: MeasuringFrequency | number;\n}\n\nconst defaultValue: RectMap = new Map();\n\nexport function useDroppableMeasuring(\n  containers: DroppableContainer[],\n  {dragging, dependencies, config}: Arguments\n) {\n  const [queue, setQueue] = useState<UniqueIdentifier[] | null>(null);\n  const {frequency, measure, strategy} = config;\n  const containersRef = useRef(containers);\n  const disabled = isDisabled();\n  const disabledRef = useLatestValue(disabled);\n  const measureDroppableContainers = useCallback(\n    (ids: UniqueIdentifier[] = []) => {\n      if (disabledRef.current) {\n        return;\n      }\n\n      setQueue((value) => {\n        if (value === null) {\n          return ids;\n        }\n\n        return value.concat(ids.filter((id) => !value.includes(id)));\n      });\n    },\n    [disabledRef]\n  );\n  const timeoutId = useRef<NodeJS.Timeout | null>(null);\n  const droppableRects = useLazyMemo<RectMap>(\n    (previousValue) => {\n      if (disabled && !dragging) {\n        return defaultValue;\n      }\n\n      if (\n        !previousValue ||\n        previousValue === defaultValue ||\n        containersRef.current !== containers ||\n        queue != null\n      ) {\n        const map: RectMap = new Map();\n\n        for (let container of containers) {\n          if (!container) {\n            continue;\n          }\n\n          if (\n            queue &&\n            queue.length > 0 &&\n            !queue.includes(container.id) &&\n            container.rect.current\n          ) {\n            // This container does not need to be re-measured\n            map.set(container.id, container.rect.current);\n            continue;\n          }\n\n          const node = container.node.current;\n          const rect = node ? new Rect(measure(node), node) : null;\n\n          container.rect.current = rect;\n\n          if (rect) {\n            map.set(container.id, rect);\n          }\n        }\n\n        return map;\n      }\n\n      return previousValue;\n    },\n    [containers, queue, dragging, disabled, measure]\n  );\n\n  useEffect(() => {\n    containersRef.current = containers;\n  }, [containers]);\n\n  useEffect(\n    () => {\n      if (disabled) {\n        return;\n      }\n\n      measureDroppableContainers();\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [dragging, disabled]\n  );\n\n  useEffect(\n    () => {\n      if (queue && queue.length > 0) {\n        setQueue(null);\n      }\n    },\n    //eslint-disable-next-line react-hooks/exhaustive-deps\n    [JSON.stringify(queue)]\n  );\n\n  useEffect(\n    () => {\n      if (\n        disabled ||\n        typeof frequency !== 'number' ||\n        timeoutId.current !== null\n      ) {\n        return;\n      }\n\n      timeoutId.current = setTimeout(() => {\n        measureDroppableContainers();\n        timeoutId.current = null;\n      }, frequency);\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [frequency, disabled, measureDroppableContainers, ...dependencies]\n  );\n\n  return {\n    droppableRects,\n    measureDroppableContainers,\n    measuringScheduled: queue != null,\n  };\n\n  function isDisabled() {\n    switch (strategy) {\n      case MeasuringStrategy.Always:\n        return false;\n      case MeasuringStrategy.BeforeDragging:\n        return dragging;\n      default:\n        return !dragging;\n    }\n  }\n}\n", "import {useLazyMemo} from '@dnd-kit/utilities';\n\ntype AnyFunction = (...args: any) => any;\n\nexport function useInitialValue<\n  T,\n  U extends AnyFunction | undefined = undefined\n>(\n  value: T | null,\n  computeFn?: U\n): U extends AnyFunction ? ReturnType<U> | null : T | null {\n  return useLazyMemo(\n    (previousValue) => {\n      if (!value) {\n        return null;\n      }\n\n      if (previousValue) {\n        return previousValue;\n      }\n\n      return typeof computeFn === 'function' ? computeFn(value) : value;\n    },\n    [computeFn, value]\n  );\n}\n", "import type {ClientRect} from '../../types';\nimport {useInitialValue} from './useInitialValue';\n\nexport function useInitialRect(\n  node: HTMLElement | null,\n  measure: (node: HTMLElement) => ClientRect\n) {\n  return useInitialValue(node, measure);\n}\n", "import {useEffect, useMemo} from 'react';\nimport {useEvent} from '@dnd-kit/utilities';\n\ninterface Arguments {\n  callback: MutationCallback;\n  disabled?: boolean;\n}\n\n/**\n * Returns a new MutationObserver instance.\n * If `MutationObserver` is undefined in the execution environment, returns `undefined`.\n */\nexport function useMutationObserver({callback, disabled}: Arguments) {\n  const handleMutations = useEvent(callback);\n  const mutationObserver = useMemo(() => {\n    if (\n      disabled ||\n      typeof window === 'undefined' ||\n      typeof window.MutationObserver === 'undefined'\n    ) {\n      return undefined;\n    }\n\n    const {MutationObserver} = window;\n\n    return new MutationObserver(handleMutations);\n  }, [handleMutations, disabled]);\n\n  useEffect(() => {\n    return () => mutationObserver?.disconnect();\n  }, [mutationObserver]);\n\n  return mutationObserver;\n}\n", "import {useEffect, useMemo} from 'react';\nimport {useEvent} from '@dnd-kit/utilities';\n\ninterface Arguments {\n  callback: ResizeObserverCallback;\n  disabled?: boolean;\n}\n\n/**\n * Returns a new ResizeObserver instance bound to the `onResize` callback.\n * If `ResizeObserver` is undefined in the execution environment, returns `undefined`.\n */\nexport function useResizeObserver({callback, disabled}: Arguments) {\n  const handleResize = useEvent(callback);\n  const resizeObserver = useMemo(\n    () => {\n      if (\n        disabled ||\n        typeof window === 'undefined' ||\n        typeof window.ResizeObserver === 'undefined'\n      ) {\n        return undefined;\n      }\n\n      const {ResizeObserver} = window;\n\n      return new ResizeObserver(handleResize);\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [disabled]\n  );\n\n  useEffect(() => {\n    return () => resizeObserver?.disconnect();\n  }, [resizeObserver]);\n\n  return resizeObserver;\n}\n", "import {useState} from 'react';\nimport {useIsomorphicLayoutEffect} from '@dnd-kit/utilities';\n\nimport type {ClientRect} from '../../types';\nimport {getClientRect, Rect} from '../../utilities';\n\nimport {useMutationObserver} from './useMutationObserver';\nimport {useResizeObserver} from './useResizeObserver';\n\nfunction defaultMeasure(element: HTMLElement) {\n  return new Rect(getClientRect(element), element);\n}\n\nexport function useRect(\n  element: HTMLElement | null,\n  measure: (element: HTMLElement) => ClientRect = defaultMeasure,\n  fallbackRect?: ClientRect | null\n) {\n  const [rect, setRect] = useState<ClientRect | null>(null);\n\n  function measureRect() {\n    setRect((currentRect): ClientRect | null => {\n      if (!element) {\n        return null;\n      }\n  \n      if (element.isConnected === false) {\n        // Fall back to last rect we measured if the element is\n        // no longer connected to the DOM.\n        return currentRect ?? fallbackRect ?? null;\n      }\n  \n      const newRect = measure(element);\n  \n      if (JSON.stringify(currentRect) === JSON.stringify(newRect)) {\n        return currentRect;\n      }\n  \n      return newRect;\n    });\n  }\n  \n  const mutationObserver = useMutationObserver({\n    callback(records) {\n      if (!element) {\n        return;\n      }\n\n      for (const record of records) {\n        const {type, target} = record;\n\n        if (\n          type === 'childList' &&\n          target instanceof HTMLElement &&\n          target.contains(element)\n        ) {\n          measureRect();\n          break;\n        }\n      }\n    },\n  });\n  const resizeObserver = useResizeObserver({callback: measureRect});\n\n  useIsomorphicLayoutEffect(() => {\n    measureRect();\n\n    if (element) {\n      resizeObserver?.observe(element);\n      mutationObserver?.observe(document.body, {\n        childList: true,\n        subtree: true,\n      });\n    } else {\n      resizeObserver?.disconnect();\n      mutationObserver?.disconnect();\n    }\n  }, [element]);\n\n  return rect;\n}\n", "import type {ClientRect} from '../../types';\nimport {getRectDelta} from '../../utilities';\n\nimport {useInitialValue} from './useInitialValue';\n\nexport function useRectDelta(rect: ClientRect | null) {\n  const initialRect = useInitialValue(rect);\n\n  return getRectDelta(rect, initialRect);\n}\n", "import {useEffect, useRef} from 'react';\nimport {useLazyMemo} from '@dnd-kit/utilities';\n\nimport {getScrollableAncestors} from '../../utilities';\n\nconst defaultValue: Element[] = [];\n\nexport function useScrollableAncestors(node: HTMLElement | null) {\n  const previousNode = useRef(node);\n\n  const ancestors = useLazyMemo<Element[]>(\n    (previousValue) => {\n      if (!node) {\n        return defaultValue;\n      }\n\n      if (\n        previousValue &&\n        previousValue !== defaultValue &&\n        node &&\n        previousNode.current &&\n        node.parentNode === previousNode.current.parentNode\n      ) {\n        return previousValue;\n      }\n\n      return getScrollableAncestors(node);\n    },\n    [node]\n  );\n\n  useEffect(() => {\n    previousNode.current = node;\n  }, [node]);\n\n  return ancestors;\n}\n", "import {useState, useCallback, useMemo, useRef, useEffect} from 'react';\nimport {add} from '@dnd-kit/utilities';\n\nimport {\n  defaultCoordinates,\n  getScrollableElement,\n  getScrollCoordinates,\n  getScrollOffsets,\n} from '../../utilities';\nimport type {Coordinates} from '../../types';\n\ntype ScrollCoordinates = Map<HTMLElement | Window, Coordinates>;\n\nexport function useScrollOffsets(elements: Element[]): Coordinates {\n  const [\n    scrollCoordinates,\n    setScrollCoordinates,\n  ] = useState<ScrollCoordinates | null>(null);\n  const prevElements = useRef(elements);\n\n  // To-do: Throttle the handleScroll callback\n  const handleScroll = useCallback((event: Event) => {\n    const scrollingElement = getScrollableElement(event.target);\n\n    if (!scrollingElement) {\n      return;\n    }\n\n    setScrollCoordinates((scrollCoordinates) => {\n      if (!scrollCoordinates) {\n        return null;\n      }\n\n      scrollCoordinates.set(\n        scrollingElement,\n        getScrollCoordinates(scrollingElement)\n      );\n\n      return new Map(scrollCoordinates);\n    });\n  }, []);\n\n  useEffect(() => {\n    const previousElements = prevElements.current;\n\n    if (elements !== previousElements) {\n      cleanup(previousElements);\n\n      const entries = elements\n        .map((element) => {\n          const scrollableElement = getScrollableElement(element);\n\n          if (scrollableElement) {\n            scrollableElement.addEventListener('scroll', handleScroll, {\n              passive: true,\n            });\n\n            return [\n              scrollableElement,\n              getScrollCoordinates(scrollableElement),\n            ] as const;\n          }\n\n          return null;\n        })\n        .filter(\n          (\n            entry\n          ): entry is [\n            HTMLElement | (Window & typeof globalThis),\n            Coordinates\n          ] => entry != null\n        );\n\n      setScrollCoordinates(entries.length ? new Map(entries) : null);\n\n      prevElements.current = elements;\n    }\n\n    return () => {\n      cleanup(elements);\n      cleanup(previousElements);\n    };\n\n    function cleanup(elements: Element[]) {\n      elements.forEach((element) => {\n        const scrollableElement = getScrollableElement(element);\n\n        scrollableElement?.removeEventListener('scroll', handleScroll);\n      });\n    }\n  }, [handleScroll, elements]);\n\n  return useMemo(() => {\n    if (elements.length) {\n      return scrollCoordinates\n        ? Array.from(scrollCoordinates.values()).reduce(\n            (acc, coordinates) => add(acc, coordinates),\n            defaultCoordinates\n          )\n        : getScrollOffsets(elements);\n    }\n\n    return defaultCoordinates;\n  }, [elements, scrollCoordinates]);\n}\n", "import {useEffect, useRef} from 'react';\nimport {Coordinates, subtract} from '@dnd-kit/utilities';\n\nimport {defaultCoordinates} from '../../utilities';\n\nexport function useScrollOffsetsDelta(\n  scrollOffsets: Coordinates,\n  dependencies: any[] = []\n) {\n  const initialScrollOffsets = useRef<Coordinates | null>(null);\n\n  useEffect(\n    () => {\n      initialScrollOffsets.current = null;\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    dependencies\n  );\n\n  useEffect(() => {\n    const hasScrollOffsets = scrollOffsets !== defaultCoordinates;\n\n    if (hasScrollOffsets && !initialScrollOffsets.current) {\n      initialScrollOffsets.current = scrollOffsets;\n    }\n\n    if (!hasScrollOffsets && initialScrollOffsets.current) {\n      initialScrollOffsets.current = null;\n    }\n  }, [scrollOffsets]);\n\n  return initialScrollOffsets.current\n    ? subtract(scrollOffsets, initialScrollOffsets.current)\n    : defaultCoordinates;\n}\n", "import {useEffect} from 'react';\nimport {canUseDOM} from '@dnd-kit/utilities';\n\nimport type {SensorDescriptor} from '../../sensors';\n\nexport function useSensorSetup(sensors: SensorDescriptor<any>[]) {\n  useEffect(\n    () => {\n      if (!canUseDOM) {\n        return;\n      }\n\n      const teardownFns = sensors.map(({sensor}) => sensor.setup?.());\n\n      return () => {\n        for (const teardown of teardownFns) {\n          teardown?.();\n        }\n      };\n    },\n    // TO-DO: Sensors length could theoretically change which would not be a valid dependency\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    sensors.map(({sensor}) => sensor)\n  );\n}\n", "import {useMemo} from 'react';\n\nimport type {SyntheticEventName, UniqueIdentifier} from '../../types';\n\nexport type SyntheticListener = {\n  eventName: SyntheticEventName;\n  handler: (event: React.SyntheticEvent, id: UniqueIdentifier) => void;\n};\n\nexport type SyntheticListeners = SyntheticListener[];\n\nexport type SyntheticListenerMap = Record<string, Function>;\n\nexport function useSyntheticListeners(\n  listeners: SyntheticListeners,\n  id: UniqueIdentifier\n): SyntheticListenerMap {\n  return useMemo(() => {\n    return listeners.reduce<SyntheticListenerMap>(\n      (acc, {eventName, handler}) => {\n        acc[eventName] = (event: React.SyntheticEvent) => {\n          handler(event, id);\n        };\n\n        return acc;\n      },\n      {} as SyntheticListenerMap\n    );\n  }, [listeners, id]);\n}\n", "import {useMemo} from 'react';\n\nimport {getWindowClientRect} from '../../utilities/rect';\n\nexport function useWindowRect(element: typeof window | null) {\n  return useMemo(() => (element ? getWindowClientRect(element) : null), [\n    element,\n  ]);\n}\n", "import {useState} from 'react';\nimport {getWindow, useIsomorphicLayoutEffect} from '@dnd-kit/utilities';\n\nimport type {ClientRect} from '../../types';\nimport {Rect, getClientRect} from '../../utilities/rect';\nimport {isDocumentScrollingElement} from '../../utilities';\n\nimport {useResizeObserver} from './useResizeObserver';\nimport {useWindowRect} from './useWindowRect';\n\nconst defaultValue: Rect[] = [];\n\nexport function useRects(\n  elements: Element[],\n  measure: (element: Element) => ClientRect = getClientRect\n): ClientRect[] {\n  const [firstElement] = elements;\n  const windowRect = useWindowRect(\n    firstElement ? getWindow(firstElement) : null\n  );\n  const [rects, setRects] = useState<ClientRect[]>(defaultValue);\n\n  function measureRects() {\n    setRects(() => {\n      if (!elements.length) {\n        return defaultValue;\n      }\n\n      return elements.map((element) =>\n        isDocumentScrollingElement(element)\n          ? (windowRect as ClientRect)\n          : new Rect(measure(element), element)\n      );\n    });\n  }\n\n  const resizeObserver = useResizeObserver({callback: measureRects});\n\n  useIsomorphicLayoutEffect(() => {\n    resizeObserver?.disconnect();\n    measureRects();\n    elements.forEach((element) => resizeObserver?.observe(element));\n  }, [elements]);\n\n  return rects;\n}\n", "import {isHTMLElement} from '@dnd-kit/utilities';\n\nexport function getMeasurableNode(\n  node: HTMLElement | undefined | null\n): HTMLElement | null {\n  if (!node) {\n    return null;\n  }\n\n  if (node.children.length > 1) {\n    return node;\n  }\n  const firstChild = node.children[0];\n\n  return isHTMLElement(firstChild) ? firstChild : node;\n}\n", "import {useMemo, useCallback, useState} from 'react';\nimport {isHTMLElement, useNodeRef} from '@dnd-kit/utilities';\n\nimport {useResizeObserver} from './useResizeObserver';\nimport {getMeasurableNode} from '../../utilities/nodes';\nimport type {PublicContextDescriptor} from '../../store';\nimport type {ClientRect} from '../../types';\n\ninterface Arguments {\n  measure(element: HTMLElement): ClientRect;\n}\n\nexport function useDragOverlayMeasuring({\n  measure,\n}: Arguments): PublicContextDescriptor['dragOverlay'] {\n  const [rect, setRect] = useState<ClientRect | null>(null);\n  const handleResize = useCallback(\n    (entries: ResizeObserverEntry[]) => {\n      for (const {target} of entries) {\n        if (isHTMLElement(target)) {\n          setRect((rect) => {\n            const newRect = measure(target);\n\n            return rect\n              ? {...rect, width: newRect.width, height: newRect.height}\n              : newRect;\n          });\n          break;\n        }\n      }\n    },\n    [measure]\n  );\n  const resizeObserver = useResizeObserver({callback: handleResize});\n  const handleNodeChange = useCallback(\n    (element) => {\n      const node = getMeasurableNode(element);\n\n      resizeObserver?.disconnect();\n\n      if (node) {\n        resizeObserver?.observe(node);\n      }\n\n      setRect(node ? measure(node) : null);\n    },\n    [measure, resizeObserver]\n  );\n  const [nodeRef, setRef] = useNodeRef(handleNodeChange);\n\n  return useMemo(\n    () => ({\n      nodeRef,\n      rect,\n      setRef,\n    }),\n    [rect, nodeRef, setRef]\n  );\n}\n", "import type {DeepRequired} from '@dnd-kit/utilities';\n\nimport type {DataRef} from '../../store/types';\nimport {KeyboardSensor, PointerSensor} from '../../sensors';\nimport {MeasuringStrategy, MeasuringFrequency} from '../../hooks/utilities';\nimport {\n  getClientRect,\n  getTransformAgnosticClientRect,\n} from '../../utilities/rect';\n\nimport type {MeasuringConfiguration} from './types';\n\nexport const defaultSensors = [\n  {sensor: PointerSensor, options: {}},\n  {sensor: KeyboardSensor, options: {}},\n];\n\nexport const defaultData: DataRef = {current: {}};\n\nexport const defaultMeasuringConfiguration: DeepRequired<MeasuringConfiguration> = {\n  draggable: {\n    measure: getTransformAgnosticClientRect,\n  },\n  droppable: {\n    measure: getTransformAgnosticClientRect,\n    strategy: MeasuringStrategy.WhileDragging,\n    frequency: MeasuringFrequency.Optimized,\n  },\n  dragOverlay: {\n    measure: getClientRect,\n  },\n};\n", "import type {UniqueIdentifier} from '../types';\nimport type {DroppableContainer} from './types';\n\ntype Identifier = UniqueIdentifier | null | undefined;\n\nexport class DroppableContainersMap extends Map<\n  UniqueIdentifier,\n  DroppableContainer\n> {\n  get(id: Identifier) {\n    return id != null ? super.get(id) ?? undefined : undefined;\n  }\n\n  toArray(): DroppableContainer[] {\n    return Array.from(this.values());\n  }\n\n  getEnabled(): DroppableContainer[] {\n    return this.toArray().filter(({disabled}) => !disabled);\n  }\n\n  getNodeFor(id: Identifier) {\n    return this.get(id)?.node.current ?? undefined;\n  }\n}\n", "import {createContext} from 'react';\n\nimport {noop} from '../utilities/other';\nimport {defaultMeasuringConfiguration} from '../components/DndContext/defaults';\nimport {DroppableContainersMap} from './constructors';\nimport type {InternalContextDescriptor, PublicContextDescriptor} from './types';\n\nexport const defaultPublicContext: PublicContextDescriptor = {\n  activatorEvent: null,\n  active: null,\n  activeNode: null,\n  activeNodeRect: null,\n  collisions: null,\n  containerNodeRect: null,\n  draggableNodes: new Map(),\n  droppableRects: new Map(),\n  droppableContainers: new DroppableContainersMap(),\n  over: null,\n  dragOverlay: {\n    nodeRef: {\n      current: null,\n    },\n    rect: null,\n    setRef: noop,\n  },\n  scrollableAncestors: [],\n  scrollableAncestorRects: [],\n  measuringConfiguration: defaultMeasuringConfiguration,\n  measureDroppableContainers: noop,\n  windowRect: null,\n  measuringScheduled: false,\n};\n\nexport const defaultInternalContext: InternalContextDescriptor = {\n  activatorEvent: null,\n  activators: [],\n  active: null,\n  activeNodeRect: null,\n  ariaDescribedById: {\n    draggable: '',\n  },\n  dispatch: noop,\n  draggableNodes: new Map(),\n  over: null,\n  measureDroppableContainers: noop,\n};\n\nexport const InternalContext = createContext<InternalContextDescriptor>(\n  defaultInternalContext\n);\n\nexport const PublicContext = createContext<PublicContextDescriptor>(\n  defaultPublicContext\n);\n", "import {Action, Actions} from './actions';\nimport {DroppableContainersMap} from './constructors';\nimport type {State} from './types';\n\nexport function getInitialState(): State {\n  return {\n    draggable: {\n      active: null,\n      initialCoordinates: {x: 0, y: 0},\n      nodes: new Map(),\n      translate: {x: 0, y: 0},\n    },\n    droppable: {\n      containers: new DroppableContainersMap(),\n    },\n  };\n}\n\nexport function reducer(state: State, action: Actions): State {\n  switch (action.type) {\n    case Action.DragStart:\n      return {\n        ...state,\n        draggable: {\n          ...state.draggable,\n          initialCoordinates: action.initialCoordinates,\n          active: action.active,\n        },\n      };\n    case Action.DragMove:\n      if (state.draggable.active == null) {\n        return state;\n      }\n\n      return {\n        ...state,\n        draggable: {\n          ...state.draggable,\n          translate: {\n            x: action.coordinates.x - state.draggable.initialCoordinates.x,\n            y: action.coordinates.y - state.draggable.initialCoordinates.y,\n          },\n        },\n      };\n    case Action.DragEnd:\n    case Action.DragCancel:\n      return {\n        ...state,\n        draggable: {\n          ...state.draggable,\n          active: null,\n          initialCoordinates: {x: 0, y: 0},\n          translate: {x: 0, y: 0},\n        },\n      };\n\n    case Action.RegisterDroppable: {\n      const {element} = action;\n      const {id} = element;\n      const containers = new DroppableContainersMap(state.droppable.containers);\n      containers.set(id, element);\n\n      return {\n        ...state,\n        droppable: {\n          ...state.droppable,\n          containers,\n        },\n      };\n    }\n\n    case Action.SetDroppableDisabled: {\n      const {id, key, disabled} = action;\n      const element = state.droppable.containers.get(id);\n\n      if (!element || key !== element.key) {\n        return state;\n      }\n\n      const containers = new DroppableContainersMap(state.droppable.containers);\n      containers.set(id, {\n        ...element,\n        disabled,\n      });\n\n      return {\n        ...state,\n        droppable: {\n          ...state.droppable,\n          containers,\n        },\n      };\n    }\n\n    case Action.UnregisterDroppable: {\n      const {id, key} = action;\n      const element = state.droppable.containers.get(id);\n\n      if (!element || key !== element.key) {\n        return state;\n      }\n\n      const containers = new DroppableContainersMap(state.droppable.containers);\n      containers.delete(id);\n\n      return {\n        ...state,\n        droppable: {\n          ...state.droppable,\n          containers,\n        },\n      };\n    }\n\n    default: {\n      return state;\n    }\n  }\n}\n", "import {useContext, useEffect} from 'react';\nimport {\n  findFirstFocusableNode,\n  isKeyboardEvent,\n  usePrevious,\n} from '@dnd-kit/utilities';\n\nimport {InternalContext} from '../../../store';\n\ninterface Props {\n  disabled: boolean;\n}\n\nexport function RestoreFocus({disabled}: Props) {\n  const {active, activatorEvent, draggableNodes} = useContext(InternalContext);\n  const previousActivatorEvent = usePrevious(activatorEvent);\n  const previousActiveId = usePrevious(active?.id);\n\n  // Restore keyboard focus on the activator node\n  useEffect(() => {\n    if (disabled) {\n      return;\n    }\n\n    if (!activatorEvent && previousActivatorEvent && previousActiveId != null) {\n      if (!isKeyboardEvent(previousActivatorEvent)) {\n        return;\n      }\n\n      if (document.activeElement === previousActivatorEvent.target) {\n        // No need to restore focus\n        return;\n      }\n\n      const draggableNode = draggableNodes.get(previousActiveId);\n\n      if (!draggableNode) {\n        return;\n      }\n\n      const {activatorNode, node} = draggableNode;\n\n      if (!activatorNode.current && !node.current) {\n        return;\n      }\n\n      requestAnimationFrame(() => {\n        for (const element of [activatorNode.current, node.current]) {\n          if (!element) {\n            continue;\n          }\n\n          const focusableNode = findFirstFocusableNode(element);\n\n          if (focusableNode) {\n            focusableNode.focus();\n            break;\n          }\n        }\n      });\n    }\n  }, [\n    activatorEvent,\n    disabled,\n    draggableNodes,\n    previousActiveId,\n    previousActivatorEvent,\n  ]);\n\n  return null;\n}\n", "import type {FirstArgument, Transform} from '@dnd-kit/utilities';\n\nimport type {Modifiers, Modifier} from './types';\n\nexport function applyModifiers(\n  modifiers: Modifiers | undefined,\n  {transform, ...args}: FirstArgument<Modifier>\n): Transform {\n  return modifiers?.length\n    ? modifiers.reduce<Transform>((accumulator, modifier) => {\n        return modifier({\n          transform: accumulator,\n          ...args,\n        });\n      }, transform)\n    : transform;\n}\n", "import {useMemo} from 'react';\nimport type {DeepRequired} from '@dnd-kit/utilities';\n\nimport {defaultMeasuringConfiguration} from '../defaults';\nimport type {MeasuringConfiguration} from '../types';\n\nexport function useMeasuringConfiguration(\n  config: MeasuringConfiguration | undefined\n): DeepRequired<MeasuringConfiguration> {\n  return useMemo(\n    () => ({\n      draggable: {\n        ...defaultMeasuringConfiguration.draggable,\n        ...config?.draggable,\n      },\n      droppable: {\n        ...defaultMeasuringConfiguration.droppable,\n        ...config?.droppable,\n      },\n      dragOverlay: {\n        ...defaultMeasuringConfiguration.dragOverlay,\n        ...config?.dragOverlay,\n      },\n    }),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [config?.draggable, config?.droppable, config?.dragOverlay]\n  );\n}\n", "import {useRef} from 'react';\nimport {useIsomorphicLayoutEffect} from '@dnd-kit/utilities';\n\nimport {getRectDelta} from '../../../utilities/rect';\nimport {getFirstScrollableAncestor} from '../../../utilities/scroll';\nimport type {ClientRect} from '../../../types';\nimport type {DraggableNode} from '../../../store';\nimport type {MeasuringFunction} from '../types';\n\ninterface Options {\n  activeNode: DraggableNode | null | undefined;\n  config: boolean | {x: boolean; y: boolean} | undefined;\n  initialRect: ClientRect | null;\n  measure: MeasuringFunction;\n}\n\nexport function useLayoutShiftScrollCompensation({\n  activeNode,\n  measure,\n  initialRect,\n  config = true,\n}: Options) {\n  const initialized = useRef(false);\n  const {x, y} = typeof config === 'boolean' ? {x: config, y: config} : config;\n\n  useIsomorphicLayoutEffect(() => {\n    const disabled = !x && !y;\n\n    if (disabled || !activeNode) {\n      initialized.current = false;\n      return;\n    }\n\n    if (initialized.current || !initialRect) {\n      // Return early if layout shift scroll compensation was already attempted\n      // or if there is no initialRect to compare to.\n      return;\n    }\n\n    // Get the most up to date node ref for the active draggable\n    const node = activeNode?.node.current;\n\n    if (!node || node.isConnected === false) {\n      // Return early if there is no attached node ref or if the node is\n      // disconnected from the document.\n      return;\n    }\n\n    const rect = measure(node);\n    const rectDelta = getRectDelta(rect, initialRect);\n\n    if (!x) {\n      rectDelta.x = 0;\n    }\n\n    if (!y) {\n      rectDelta.y = 0;\n    }\n\n    // Only perform layout shift scroll compensation once\n    initialized.current = true;\n\n    if (Math.abs(rectDelta.x) > 0 || Math.abs(rectDelta.y) > 0) {\n      const firstScrollableAncestor = getFirstScrollableAncestor(node);\n\n      if (firstScrollableAncestor) {\n        firstScrollableAncestor.scrollBy({\n          top: rectDelta.y,\n          left: rectDelta.x,\n        });\n      }\n    }\n  }, [activeNode, x, y, initialRect, measure]);\n}\n", "import React, {\n  memo,\n  createContext,\n  useCallback,\n  useEffect,\n  useMemo,\n  useReducer,\n  useRef,\n  useState,\n} from 'react';\nimport {unstable_batchedUpdates} from 'react-dom';\nimport {\n  add,\n  getEventCoordinates,\n  getWindow,\n  useLatestValue,\n  useIsomorphicLayoutEffect,\n  useUniqueId,\n} from '@dnd-kit/utilities';\nimport type {Transform} from '@dnd-kit/utilities';\n\nimport {\n  Action,\n  PublicContext,\n  InternalContext,\n  PublicContextDescriptor,\n  InternalContextDescriptor,\n  getInitialState,\n  reducer,\n} from '../../store';\nimport {DndMonitorContext, useDndMonitorProvider} from '../DndMonitor';\nimport {\n  useAutoScroller,\n  useCachedNode,\n  useCombineActivators,\n  useDragOverlayMeasuring,\n  useDroppableMeasuring,\n  useInitialRect,\n  useRect,\n  useRectDelta,\n  useRects,\n  useScrollableAncestors,\n  useScrollOffsets,\n  useScrollOffsetsDelta,\n  useSensorSetup,\n  useWindowRect,\n} from '../../hooks/utilities';\nimport type {AutoScrollOptions, SyntheticListener} from '../../hooks/utilities';\nimport type {\n  Sensor,\n  SensorContext,\n  SensorDescriptor,\n  SensorActivatorFunction,\n  SensorInstance,\n} from '../../sensors';\nimport {\n  adjustScale,\n  CollisionDetection,\n  defaultCoordinates,\n  getAdjustedRect,\n  getFirstCollision,\n  rectIntersection,\n} from '../../utilities';\nimport {applyModifiers, Modifiers} from '../../modifiers';\nimport type {Active, Over} from '../../store/types';\nimport type {\n  DragStartEvent,\n  DragCancelEvent,\n  DragEndEvent,\n  DragMoveEvent,\n  DragOverEvent,\n  UniqueIdentifier,\n  DragPendingEvent,\n  DragAbortEvent,\n} from '../../types';\nimport {\n  Accessibility,\n  Announcements,\n  RestoreFocus,\n  ScreenReaderInstructions,\n} from '../Accessibility';\n\nimport {defaultData, defaultSensors} from './defaults';\nimport {\n  useLayoutShiftScrollCompensation,\n  useMeasuringConfiguration,\n} from './hooks';\nimport type {MeasuringConfiguration} from './types';\n\nexport interface Props {\n  id?: string;\n  accessibility?: {\n    announcements?: Announcements;\n    container?: Element;\n    restoreFocus?: boolean;\n    screenReaderInstructions?: ScreenReaderInstructions;\n  };\n  autoScroll?: boolean | AutoScrollOptions;\n  cancelDrop?: CancelDrop;\n  children?: React.ReactNode;\n  collisionDetection?: CollisionDetection;\n  measuring?: MeasuringConfiguration;\n  modifiers?: Modifiers;\n  sensors?: SensorDescriptor<any>[];\n  onDragAbort?(event: DragAbortEvent): void;\n  onDragPending?(event: DragPendingEvent): void;\n  onDragStart?(event: DragStartEvent): void;\n  onDragMove?(event: DragMoveEvent): void;\n  onDragOver?(event: DragOverEvent): void;\n  onDragEnd?(event: DragEndEvent): void;\n  onDragCancel?(event: DragCancelEvent): void;\n}\n\nexport interface CancelDropArguments extends DragEndEvent {}\n\nexport type CancelDrop = (\n  args: CancelDropArguments\n) => boolean | Promise<boolean>;\n\ninterface DndEvent extends Event {\n  dndKit?: {\n    capturedBy: Sensor<any>;\n  };\n}\n\nexport const ActiveDraggableContext = createContext<Transform>({\n  ...defaultCoordinates,\n  scaleX: 1,\n  scaleY: 1,\n});\n\nenum Status {\n  Uninitialized,\n  Initializing,\n  Initialized,\n}\n\nexport const DndContext = memo(function DndContext({\n  id,\n  accessibility,\n  autoScroll = true,\n  children,\n  sensors = defaultSensors,\n  collisionDetection = rectIntersection,\n  measuring,\n  modifiers,\n  ...props\n}: Props) {\n  const store = useReducer(reducer, undefined, getInitialState);\n  const [state, dispatch] = store;\n  const [dispatchMonitorEvent, registerMonitorListener] =\n    useDndMonitorProvider();\n  const [status, setStatus] = useState<Status>(Status.Uninitialized);\n  const isInitialized = status === Status.Initialized;\n  const {\n    draggable: {active: activeId, nodes: draggableNodes, translate},\n    droppable: {containers: droppableContainers},\n  } = state;\n  const node = activeId != null ? draggableNodes.get(activeId) : null;\n  const activeRects = useRef<Active['rect']['current']>({\n    initial: null,\n    translated: null,\n  });\n  const active = useMemo<Active | null>(\n    () =>\n      activeId != null\n        ? {\n            id: activeId,\n            // It's possible for the active node to unmount while dragging\n            data: node?.data ?? defaultData,\n            rect: activeRects,\n          }\n        : null,\n    [activeId, node]\n  );\n  const activeRef = useRef<UniqueIdentifier | null>(null);\n  const [activeSensor, setActiveSensor] = useState<SensorInstance | null>(null);\n  const [activatorEvent, setActivatorEvent] = useState<Event | null>(null);\n  const latestProps = useLatestValue(props, Object.values(props));\n  const draggableDescribedById = useUniqueId(`DndDescribedBy`, id);\n  const enabledDroppableContainers = useMemo(\n    () => droppableContainers.getEnabled(),\n    [droppableContainers]\n  );\n  const measuringConfiguration = useMeasuringConfiguration(measuring);\n  const {droppableRects, measureDroppableContainers, measuringScheduled} =\n    useDroppableMeasuring(enabledDroppableContainers, {\n      dragging: isInitialized,\n      dependencies: [translate.x, translate.y],\n      config: measuringConfiguration.droppable,\n    });\n  const activeNode = useCachedNode(draggableNodes, activeId);\n  const activationCoordinates = useMemo(\n    () => (activatorEvent ? getEventCoordinates(activatorEvent) : null),\n    [activatorEvent]\n  );\n  const autoScrollOptions = getAutoScrollerOptions();\n  const initialActiveNodeRect = useInitialRect(\n    activeNode,\n    measuringConfiguration.draggable.measure\n  );\n\n  useLayoutShiftScrollCompensation({\n    activeNode: activeId != null ? draggableNodes.get(activeId) : null,\n    config: autoScrollOptions.layoutShiftCompensation,\n    initialRect: initialActiveNodeRect,\n    measure: measuringConfiguration.draggable.measure,\n  });\n\n  const activeNodeRect = useRect(\n    activeNode,\n    measuringConfiguration.draggable.measure,\n    initialActiveNodeRect\n  );\n  const containerNodeRect = useRect(\n    activeNode ? activeNode.parentElement : null\n  );\n  const sensorContext = useRef<SensorContext>({\n    activatorEvent: null,\n    active: null,\n    activeNode,\n    collisionRect: null,\n    collisions: null,\n    droppableRects,\n    draggableNodes,\n    draggingNode: null,\n    draggingNodeRect: null,\n    droppableContainers,\n    over: null,\n    scrollableAncestors: [],\n    scrollAdjustedTranslate: null,\n  });\n  const overNode = droppableContainers.getNodeFor(\n    sensorContext.current.over?.id\n  );\n  const dragOverlay = useDragOverlayMeasuring({\n    measure: measuringConfiguration.dragOverlay.measure,\n  });\n\n  // Use the rect of the drag overlay if it is mounted\n  const draggingNode = dragOverlay.nodeRef.current ?? activeNode;\n  const draggingNodeRect = isInitialized\n    ? dragOverlay.rect ?? activeNodeRect\n    : null;\n  const usesDragOverlay = Boolean(\n    dragOverlay.nodeRef.current && dragOverlay.rect\n  );\n  // The delta between the previous and new position of the draggable node\n  // is only relevant when there is no drag overlay\n  const nodeRectDelta = useRectDelta(usesDragOverlay ? null : activeNodeRect);\n\n  // Get the window rect of the dragging node\n  const windowRect = useWindowRect(\n    draggingNode ? getWindow(draggingNode) : null\n  );\n\n  // Get scrollable ancestors of the dragging node\n  const scrollableAncestors = useScrollableAncestors(\n    isInitialized ? overNode ?? activeNode : null\n  );\n  const scrollableAncestorRects = useRects(scrollableAncestors);\n\n  // Apply modifiers\n  const modifiedTranslate = applyModifiers(modifiers, {\n    transform: {\n      x: translate.x - nodeRectDelta.x,\n      y: translate.y - nodeRectDelta.y,\n      scaleX: 1,\n      scaleY: 1,\n    },\n    activatorEvent,\n    active,\n    activeNodeRect,\n    containerNodeRect,\n    draggingNodeRect,\n    over: sensorContext.current.over,\n    overlayNodeRect: dragOverlay.rect,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    windowRect,\n  });\n\n  const pointerCoordinates = activationCoordinates\n    ? add(activationCoordinates, translate)\n    : null;\n\n  const scrollOffsets = useScrollOffsets(scrollableAncestors);\n  // Represents the scroll delta since dragging was initiated\n  const scrollAdjustment = useScrollOffsetsDelta(scrollOffsets);\n  // Represents the scroll delta since the last time the active node rect was measured\n  const activeNodeScrollDelta = useScrollOffsetsDelta(scrollOffsets, [\n    activeNodeRect,\n  ]);\n\n  const scrollAdjustedTranslate = add(modifiedTranslate, scrollAdjustment);\n\n  const collisionRect = draggingNodeRect\n    ? getAdjustedRect(draggingNodeRect, modifiedTranslate)\n    : null;\n\n  const collisions =\n    active && collisionRect\n      ? collisionDetection({\n          active,\n          collisionRect,\n          droppableRects,\n          droppableContainers: enabledDroppableContainers,\n          pointerCoordinates,\n        })\n      : null;\n  const overId = getFirstCollision(collisions, 'id');\n  const [over, setOver] = useState<Over | null>(null);\n\n  // When there is no drag overlay used, we need to account for the\n  // window scroll delta\n  const appliedTranslate = usesDragOverlay\n    ? modifiedTranslate\n    : add(modifiedTranslate, activeNodeScrollDelta);\n\n  const transform = adjustScale(\n    appliedTranslate,\n    over?.rect ?? null,\n    activeNodeRect\n  );\n\n  const activeSensorRef = useRef<SensorInstance | null>(null);\n  const instantiateSensor = useCallback(\n    (\n      event: React.SyntheticEvent,\n      {sensor: Sensor, options}: SensorDescriptor<any>\n    ) => {\n      if (activeRef.current == null) {\n        return;\n      }\n\n      const activeNode = draggableNodes.get(activeRef.current);\n\n      if (!activeNode) {\n        return;\n      }\n\n      const activatorEvent = event.nativeEvent;\n\n      const sensorInstance = new Sensor({\n        active: activeRef.current,\n        activeNode,\n        event: activatorEvent,\n        options,\n        // Sensors need to be instantiated with refs for arguments that change over time\n        // otherwise they are frozen in time with the stale arguments\n        context: sensorContext,\n        onAbort(id) {\n          const draggableNode = draggableNodes.get(id);\n\n          if (!draggableNode) {\n            return;\n          }\n\n          const {onDragAbort} = latestProps.current;\n          const event: DragAbortEvent = {id};\n          onDragAbort?.(event);\n          dispatchMonitorEvent({type: 'onDragAbort', event});\n        },\n        onPending(id, constraint, initialCoordinates, offset) {\n          const draggableNode = draggableNodes.get(id);\n\n          if (!draggableNode) {\n            return;\n          }\n\n          const {onDragPending} = latestProps.current;\n          const event: DragPendingEvent = {\n            id,\n            constraint,\n            initialCoordinates,\n            offset,\n          };\n\n          onDragPending?.(event);\n          dispatchMonitorEvent({type: 'onDragPending', event});\n        },\n        onStart(initialCoordinates) {\n          const id = activeRef.current;\n\n          if (id == null) {\n            return;\n          }\n\n          const draggableNode = draggableNodes.get(id);\n\n          if (!draggableNode) {\n            return;\n          }\n\n          const {onDragStart} = latestProps.current;\n          const event: DragStartEvent = {\n            activatorEvent,\n            active: {id, data: draggableNode.data, rect: activeRects},\n          };\n\n          unstable_batchedUpdates(() => {\n            onDragStart?.(event);\n            setStatus(Status.Initializing);\n            dispatch({\n              type: Action.DragStart,\n              initialCoordinates,\n              active: id,\n            });\n            dispatchMonitorEvent({type: 'onDragStart', event});\n            setActiveSensor(activeSensorRef.current);\n            setActivatorEvent(activatorEvent);\n          });\n        },\n        onMove(coordinates) {\n          dispatch({\n            type: Action.DragMove,\n            coordinates,\n          });\n        },\n        onEnd: createHandler(Action.DragEnd),\n        onCancel: createHandler(Action.DragCancel),\n      });\n\n      activeSensorRef.current = sensorInstance;\n\n      function createHandler(type: Action.DragEnd | Action.DragCancel) {\n        return async function handler() {\n          const {active, collisions, over, scrollAdjustedTranslate} =\n            sensorContext.current;\n          let event: DragEndEvent | null = null;\n\n          if (active && scrollAdjustedTranslate) {\n            const {cancelDrop} = latestProps.current;\n\n            event = {\n              activatorEvent,\n              active: active,\n              collisions,\n              delta: scrollAdjustedTranslate,\n              over,\n            };\n\n            if (type === Action.DragEnd && typeof cancelDrop === 'function') {\n              const shouldCancel = await Promise.resolve(cancelDrop(event));\n\n              if (shouldCancel) {\n                type = Action.DragCancel;\n              }\n            }\n          }\n\n          activeRef.current = null;\n\n          unstable_batchedUpdates(() => {\n            dispatch({type});\n            setStatus(Status.Uninitialized);\n            setOver(null);\n            setActiveSensor(null);\n            setActivatorEvent(null);\n            activeSensorRef.current = null;\n\n            const eventName =\n              type === Action.DragEnd ? 'onDragEnd' : 'onDragCancel';\n\n            if (event) {\n              const handler = latestProps.current[eventName];\n\n              handler?.(event);\n              dispatchMonitorEvent({type: eventName, event});\n            }\n          });\n        };\n      }\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [draggableNodes]\n  );\n\n  const bindActivatorToSensorInstantiator = useCallback(\n    (\n      handler: SensorActivatorFunction<any>,\n      sensor: SensorDescriptor<any>\n    ): SyntheticListener['handler'] => {\n      return (event, active) => {\n        const nativeEvent = event.nativeEvent as DndEvent;\n        const activeDraggableNode = draggableNodes.get(active);\n\n        if (\n          // Another sensor is already instantiating\n          activeRef.current !== null ||\n          // No active draggable\n          !activeDraggableNode ||\n          // Event has already been captured\n          nativeEvent.dndKit ||\n          nativeEvent.defaultPrevented\n        ) {\n          return;\n        }\n\n        const activationContext = {\n          active: activeDraggableNode,\n        };\n        const shouldActivate = handler(\n          event,\n          sensor.options,\n          activationContext\n        );\n\n        if (shouldActivate === true) {\n          nativeEvent.dndKit = {\n            capturedBy: sensor.sensor,\n          };\n\n          activeRef.current = active;\n          instantiateSensor(event, sensor);\n        }\n      };\n    },\n    [draggableNodes, instantiateSensor]\n  );\n\n  const activators = useCombineActivators(\n    sensors,\n    bindActivatorToSensorInstantiator\n  );\n\n  useSensorSetup(sensors);\n\n  useIsomorphicLayoutEffect(() => {\n    if (activeNodeRect && status === Status.Initializing) {\n      setStatus(Status.Initialized);\n    }\n  }, [activeNodeRect, status]);\n\n  useEffect(\n    () => {\n      const {onDragMove} = latestProps.current;\n      const {active, activatorEvent, collisions, over} = sensorContext.current;\n\n      if (!active || !activatorEvent) {\n        return;\n      }\n\n      const event: DragMoveEvent = {\n        active,\n        activatorEvent,\n        collisions,\n        delta: {\n          x: scrollAdjustedTranslate.x,\n          y: scrollAdjustedTranslate.y,\n        },\n        over,\n      };\n\n      unstable_batchedUpdates(() => {\n        onDragMove?.(event);\n        dispatchMonitorEvent({type: 'onDragMove', event});\n      });\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [scrollAdjustedTranslate.x, scrollAdjustedTranslate.y]\n  );\n\n  useEffect(\n    () => {\n      const {\n        active,\n        activatorEvent,\n        collisions,\n        droppableContainers,\n        scrollAdjustedTranslate,\n      } = sensorContext.current;\n\n      if (\n        !active ||\n        activeRef.current == null ||\n        !activatorEvent ||\n        !scrollAdjustedTranslate\n      ) {\n        return;\n      }\n\n      const {onDragOver} = latestProps.current;\n      const overContainer = droppableContainers.get(overId);\n      const over =\n        overContainer && overContainer.rect.current\n          ? {\n              id: overContainer.id,\n              rect: overContainer.rect.current,\n              data: overContainer.data,\n              disabled: overContainer.disabled,\n            }\n          : null;\n      const event: DragOverEvent = {\n        active,\n        activatorEvent,\n        collisions,\n        delta: {\n          x: scrollAdjustedTranslate.x,\n          y: scrollAdjustedTranslate.y,\n        },\n        over,\n      };\n\n      unstable_batchedUpdates(() => {\n        setOver(over);\n        onDragOver?.(event);\n        dispatchMonitorEvent({type: 'onDragOver', event});\n      });\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [overId]\n  );\n\n  useIsomorphicLayoutEffect(() => {\n    sensorContext.current = {\n      activatorEvent,\n      active,\n      activeNode,\n      collisionRect,\n      collisions,\n      droppableRects,\n      draggableNodes,\n      draggingNode,\n      draggingNodeRect,\n      droppableContainers,\n      over,\n      scrollableAncestors,\n      scrollAdjustedTranslate,\n    };\n\n    activeRects.current = {\n      initial: draggingNodeRect,\n      translated: collisionRect,\n    };\n  }, [\n    active,\n    activeNode,\n    collisions,\n    collisionRect,\n    draggableNodes,\n    draggingNode,\n    draggingNodeRect,\n    droppableRects,\n    droppableContainers,\n    over,\n    scrollableAncestors,\n    scrollAdjustedTranslate,\n  ]);\n\n  useAutoScroller({\n    ...autoScrollOptions,\n    delta: translate,\n    draggingRect: collisionRect,\n    pointerCoordinates,\n    scrollableAncestors,\n    scrollableAncestorRects,\n  });\n\n  const publicContext = useMemo(() => {\n    const context: PublicContextDescriptor = {\n      active,\n      activeNode,\n      activeNodeRect,\n      activatorEvent,\n      collisions,\n      containerNodeRect,\n      dragOverlay,\n      draggableNodes,\n      droppableContainers,\n      droppableRects,\n      over,\n      measureDroppableContainers,\n      scrollableAncestors,\n      scrollableAncestorRects,\n      measuringConfiguration,\n      measuringScheduled,\n      windowRect,\n    };\n\n    return context;\n  }, [\n    active,\n    activeNode,\n    activeNodeRect,\n    activatorEvent,\n    collisions,\n    containerNodeRect,\n    dragOverlay,\n    draggableNodes,\n    droppableContainers,\n    droppableRects,\n    over,\n    measureDroppableContainers,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    measuringConfiguration,\n    measuringScheduled,\n    windowRect,\n  ]);\n\n  const internalContext = useMemo(() => {\n    const context: InternalContextDescriptor = {\n      activatorEvent,\n      activators,\n      active,\n      activeNodeRect,\n      ariaDescribedById: {\n        draggable: draggableDescribedById,\n      },\n      dispatch,\n      draggableNodes,\n      over,\n      measureDroppableContainers,\n    };\n\n    return context;\n  }, [\n    activatorEvent,\n    activators,\n    active,\n    activeNodeRect,\n    dispatch,\n    draggableDescribedById,\n    draggableNodes,\n    over,\n    measureDroppableContainers,\n  ]);\n\n  return (\n    <DndMonitorContext.Provider value={registerMonitorListener}>\n      <InternalContext.Provider value={internalContext}>\n        <PublicContext.Provider value={publicContext}>\n          <ActiveDraggableContext.Provider value={transform}>\n            {children}\n          </ActiveDraggableContext.Provider>\n        </PublicContext.Provider>\n        <RestoreFocus disabled={accessibility?.restoreFocus === false} />\n      </InternalContext.Provider>\n      <Accessibility\n        {...accessibility}\n        hiddenTextDescribedById={draggableDescribedById}\n      />\n    </DndMonitorContext.Provider>\n  );\n\n  function getAutoScrollerOptions() {\n    const activeSensorDisablesAutoscroll =\n      activeSensor?.autoScrollEnabled === false;\n    const autoScrollGloballyDisabled =\n      typeof autoScroll === 'object'\n        ? autoScroll.enabled === false\n        : autoScroll === false;\n    const enabled =\n      isInitialized &&\n      !activeSensorDisablesAutoscroll &&\n      !autoScrollGloballyDisabled;\n\n    if (typeof autoScroll === 'object') {\n      return {\n        ...autoScroll,\n        enabled,\n      };\n    }\n\n    return {enabled};\n  }\n});\n", "import {createContext, useContext, useMemo} from 'react';\nimport {\n  Transform,\n  useNodeRef,\n  useIsomorphicLayoutEffect,\n  useLatestValue,\n  useUniqueId,\n} from '@dnd-kit/utilities';\n\nimport {InternalContext, Data} from '../store';\nimport type {UniqueIdentifier} from '../types';\nimport {ActiveDraggableContext} from '../components/DndContext';\nimport {useSyntheticListeners, SyntheticListenerMap} from './utilities';\n\nexport interface UseDraggableArguments {\n  id: UniqueIdentifier;\n  data?: Data;\n  disabled?: boolean;\n  attributes?: {\n    role?: string;\n    roleDescription?: string;\n    tabIndex?: number;\n  };\n}\n\nexport interface DraggableAttributes {\n  role: string;\n  tabIndex: number;\n  'aria-disabled': boolean;\n  'aria-pressed': boolean | undefined;\n  'aria-roledescription': string;\n  'aria-describedby': string;\n}\n\nexport type DraggableSyntheticListeners = SyntheticListenerMap | undefined;\n\nconst NullContext = createContext<any>(null);\n\nconst defaultRole = 'button';\n\nconst ID_PREFIX = 'Draggable';\n\nexport function useDraggable({\n  id,\n  data,\n  disabled = false,\n  attributes,\n}: UseDraggableArguments) {\n  const key = useUniqueId(ID_PREFIX);\n  const {\n    activators,\n    activatorEvent,\n    active,\n    activeNodeRect,\n    ariaDescribedById,\n    draggableNodes,\n    over,\n  } = useContext(InternalContext);\n  const {\n    role = defaultRole,\n    roleDescription = 'draggable',\n    tabIndex = 0,\n  } = attributes ?? {};\n  const isDragging = active?.id === id;\n  const transform: Transform | null = useContext(\n    isDragging ? ActiveDraggableContext : NullContext\n  );\n  const [node, setNodeRef] = useNodeRef();\n  const [activatorNode, setActivatorNodeRef] = useNodeRef();\n  const listeners = useSyntheticListeners(activators, id);\n  const dataRef = useLatestValue(data);\n\n  useIsomorphicLayoutEffect(\n    () => {\n      draggableNodes.set(id, {id, key, node, activatorNode, data: dataRef});\n\n      return () => {\n        const node = draggableNodes.get(id);\n\n        if (node && node.key === key) {\n          draggableNodes.delete(id);\n        }\n      };\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [draggableNodes, id]\n  );\n\n  const memoizedAttributes: DraggableAttributes = useMemo(\n    () => ({\n      role,\n      tabIndex,\n      'aria-disabled': disabled,\n      'aria-pressed': isDragging && role === defaultRole ? true : undefined,\n      'aria-roledescription': roleDescription,\n      'aria-describedby': ariaDescribedById.draggable,\n    }),\n    [\n      disabled,\n      role,\n      tabIndex,\n      isDragging,\n      roleDescription,\n      ariaDescribedById.draggable,\n    ]\n  );\n\n  return {\n    active,\n    activatorEvent,\n    activeNodeRect,\n    attributes: memoizedAttributes,\n    isDragging,\n    listeners: disabled ? undefined : listeners,\n    node,\n    over,\n    setNodeRef,\n    setActivatorNodeRef,\n    transform,\n  };\n}\n", "import {ContextType, useContext} from 'react';\nimport {PublicContext} from '../store';\n\nexport function useDndContext() {\n  return useContext(PublicContext);\n}\n\nexport type UseDndContextReturnValue = ContextType<typeof PublicContext>;\n", "import {useCallback, useContext, useEffect, useRef} from 'react';\nimport {useLatestValue, useNodeRef, useUniqueId} from '@dnd-kit/utilities';\n\nimport {InternalContext, Action, Data} from '../store';\nimport type {ClientRect, UniqueIdentifier} from '../types';\n\nimport {useResizeObserver} from './utilities';\n\ninterface ResizeObserverConfig {\n  /** Whether the ResizeObserver should be disabled entirely */\n  disabled?: boolean;\n  /** Resize events may affect the layout and position of other droppable containers.\n   * Specify an array of `UniqueIdentifier` of droppable containers that should also be re-measured\n   * when this droppable container resizes. Specifying an empty array re-measures all droppable containers.\n   */\n  updateMeasurementsFor?: UniqueIdentifier[];\n  /** Represents the debounce timeout between when resize events are observed and when elements are re-measured */\n  timeout?: number;\n}\n\nexport interface UseDroppableArguments {\n  id: UniqueIdentifier;\n  disabled?: boolean;\n  data?: Data;\n  resizeObserverConfig?: ResizeObserverConfig;\n}\n\nconst ID_PREFIX = 'Droppable';\n\nconst defaultResizeObserverConfig = {\n  timeout: 25,\n};\n\nexport function useDroppable({\n  data,\n  disabled = false,\n  id,\n  resizeObserverConfig,\n}: UseDroppableArguments) {\n  const key = useUniqueId(ID_PREFIX);\n  const {active, dispatch, over, measureDroppableContainers} =\n    useContext(InternalContext);\n  const previous = useRef({disabled});\n  const resizeObserverConnected = useRef(false);\n  const rect = useRef<ClientRect | null>(null);\n  const callbackId = useRef<NodeJS.Timeout | null>(null);\n  const {\n    disabled: resizeObserverDisabled,\n    updateMeasurementsFor,\n    timeout: resizeObserverTimeout,\n  } = {\n    ...defaultResizeObserverConfig,\n    ...resizeObserverConfig,\n  };\n  const ids = useLatestValue(updateMeasurementsFor ?? id);\n  const handleResize = useCallback(\n    () => {\n      if (!resizeObserverConnected.current) {\n        // ResizeObserver invokes the `handleResize` callback as soon as `observe` is called,\n        // assuming the element is rendered and displayed.\n        resizeObserverConnected.current = true;\n        return;\n      }\n\n      if (callbackId.current != null) {\n        clearTimeout(callbackId.current);\n      }\n\n      callbackId.current = setTimeout(() => {\n        measureDroppableContainers(\n          Array.isArray(ids.current) ? ids.current : [ids.current]\n        );\n        callbackId.current = null;\n      }, resizeObserverTimeout);\n    },\n    //eslint-disable-next-line react-hooks/exhaustive-deps\n    [resizeObserverTimeout]\n  );\n  const resizeObserver = useResizeObserver({\n    callback: handleResize,\n    disabled: resizeObserverDisabled || !active,\n  });\n  const handleNodeChange = useCallback(\n    (newElement: HTMLElement | null, previousElement: HTMLElement | null) => {\n      if (!resizeObserver) {\n        return;\n      }\n\n      if (previousElement) {\n        resizeObserver.unobserve(previousElement);\n        resizeObserverConnected.current = false;\n      }\n\n      if (newElement) {\n        resizeObserver.observe(newElement);\n      }\n    },\n    [resizeObserver]\n  );\n  const [nodeRef, setNodeRef] = useNodeRef(handleNodeChange);\n  const dataRef = useLatestValue(data);\n\n  useEffect(() => {\n    if (!resizeObserver || !nodeRef.current) {\n      return;\n    }\n\n    resizeObserver.disconnect();\n    resizeObserverConnected.current = false;\n    resizeObserver.observe(nodeRef.current);\n  }, [nodeRef, resizeObserver]);\n\n  useEffect(\n    () => {\n      dispatch({\n        type: Action.RegisterDroppable,\n        element: {\n          id,\n          key,\n          disabled,\n          node: nodeRef,\n          rect,\n          data: dataRef,\n        },\n      });\n\n      return () =>\n        dispatch({\n          type: Action.UnregisterDroppable,\n          key,\n          id,\n        });\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [id]\n  );\n\n  useEffect(() => {\n    if (disabled !== previous.current.disabled) {\n      dispatch({\n        type: Action.SetDroppableDisabled,\n        id,\n        key,\n        disabled,\n      });\n\n      previous.current.disabled = disabled;\n    }\n  }, [id, key, disabled, dispatch]);\n\n  return {\n    active,\n    rect,\n    isOver: over?.id === id,\n    node: nodeRef,\n    over,\n    setNodeRef,\n  };\n}\n", "import React, {cloneElement, useState} from 'react';\nimport {useIsomorphicLayoutEffect, usePrevious} from '@dnd-kit/utilities';\n\nimport type {UniqueIdentifier} from '../../../../types';\n\nexport type Animation = (\n  key: UniqueIdentifier,\n  node: HTMLElement\n) => Promise<void> | void;\n\nexport interface Props {\n  animation: Animation;\n  children: React.ReactElement | null;\n}\n\nexport function AnimationManager({animation, children}: Props) {\n  const [\n    clonedChildren,\n    setClonedChildren,\n  ] = useState<React.ReactElement | null>(null);\n  const [element, setElement] = useState<HTMLElement | null>(null);\n  const previousChildren = usePrevious(children);\n\n  if (!children && !clonedChildren && previousChildren) {\n    setClonedChildren(previousChildren);\n  }\n\n  useIsomorphicLayoutEffect(() => {\n    if (!element) {\n      return;\n    }\n\n    const key = clonedChildren?.key;\n    const id = clonedChildren?.props.id;\n\n    if (key == null || id == null) {\n      setClonedChildren(null);\n      return;\n    }\n\n    Promise.resolve(animation(id, element)).then(() => {\n      setClonedChildren(null);\n    });\n  }, [animation, clonedChildren, element]);\n\n  return (\n    <>\n      {children}\n      {clonedChildren ? cloneElement(clonedChildren, {ref: setElement}) : null}\n    </>\n  );\n}\n", "import React from 'react';\nimport type {Transform} from '@dnd-kit/utilities';\n\nimport {InternalContext, defaultInternalContext} from '../../../../store';\nimport {ActiveDraggableContext} from '../../../DndContext';\n\ninterface Props {\n  children: React.ReactNode;\n}\n\nconst defaultTransform: Transform = {\n  x: 0,\n  y: 0,\n  scaleX: 1,\n  scaleY: 1,\n};\n\nexport function NullifiedContextProvider({children}: Props) {\n  return (\n    <InternalContext.Provider value={defaultInternalContext}>\n      <ActiveDraggableContext.Provider value={defaultTransform}>\n        {children}\n      </ActiveDraggableContext.Provider>\n    </InternalContext.Provider>\n  );\n}\n", "import React, {forwardRef} from 'react';\nimport {CSS, isKeyboardEvent} from '@dnd-kit/utilities';\n\nimport type {Transform} from '@dnd-kit/utilities';\n\nimport {getRelativeTransformOrigin} from '../../../../utilities';\nimport type {ClientRect, UniqueIdentifier} from '../../../../types';\n\ntype TransitionGetter = (\n  activatorEvent: Event | null\n) => React.CSSProperties['transition'] | undefined;\n\nexport interface Props {\n  as: keyof JSX.IntrinsicElements;\n  activatorEvent: Event | null;\n  adjustScale?: boolean;\n  children?: React.ReactNode;\n  className?: string;\n  id: UniqueIdentifier;\n  rect: ClientRect | null;\n  style?: React.CSSProperties;\n  transition?: string | TransitionGetter;\n  transform: Transform;\n}\n\nconst baseStyles: React.CSSProperties = {\n  position: 'fixed',\n  touchAction: 'none',\n};\n\nconst defaultTransition: TransitionGetter = (activatorEvent) => {\n  const isKeyboardActivator = isKeyboardEvent(activatorEvent);\n\n  return isKeyboardActivator ? 'transform 250ms ease' : undefined;\n};\n\nexport const PositionedOverlay = forwardRef<HTMLElement, Props>(\n  (\n    {\n      as,\n      activatorEvent,\n      adjustScale,\n      children,\n      className,\n      rect,\n      style,\n      transform,\n      transition = defaultTransition,\n    },\n    ref\n  ) => {\n    if (!rect) {\n      return null;\n    }\n\n    const scaleAdjustedTransform = adjustScale\n      ? transform\n      : {\n          ...transform,\n          scaleX: 1,\n          scaleY: 1,\n        };\n    const styles: React.CSSProperties | undefined = {\n      ...baseStyles,\n      width: rect.width,\n      height: rect.height,\n      top: rect.top,\n      left: rect.left,\n      transform: CSS.Transform.toString(scaleAdjustedTransform),\n      transformOrigin:\n        adjustScale && activatorEvent\n          ? getRelativeTransformOrigin(\n              activatorEvent as MouseEvent | KeyboardEvent | TouchEvent,\n              rect\n            )\n          : undefined,\n      transition:\n        typeof transition === 'function'\n          ? transition(activatorEvent)\n          : transition,\n      ...style,\n    };\n\n    return React.createElement(\n      as,\n      {\n        className,\n        style: styles,\n        ref,\n      },\n      children\n    );\n  }\n);\n", "import {CSS, useEvent, getWindow} from '@dnd-kit/utilities';\nimport type {DeepRequired, Transform} from '@dnd-kit/utilities';\n\nimport type {\n  Active,\n  DraggableNode,\n  DraggableNodes,\n  DroppableContainers,\n} from '../../../store';\nimport type {ClientRect, UniqueIdentifier} from '../../../types';\nimport {getMeasurableNode} from '../../../utilities/nodes';\nimport {scrollIntoViewIfNeeded} from '../../../utilities/scroll';\nimport {parseTransform} from '../../../utilities/transform';\nimport type {MeasuringConfiguration} from '../../DndContext';\nimport type {Animation} from '../components';\n\ninterface SharedParameters {\n  active: {\n    id: UniqueIdentifier;\n    data: Active['data'];\n    node: HTMLElement;\n    rect: ClientRect;\n  };\n  dragOverlay: {\n    node: HTMLElement;\n    rect: ClientRect;\n  };\n  draggableNodes: DraggableNodes;\n  droppableContainers: DroppableContainers;\n  measuringConfiguration: DeepRequired<MeasuringConfiguration>;\n}\n\nexport interface KeyframeResolverParameters extends SharedParameters {\n  transform: {\n    initial: Transform;\n    final: Transform;\n  };\n}\n\nexport type KeyframeResolver = (\n  parameters: KeyframeResolverParameters\n) => Keyframe[];\n\nexport interface DropAnimationOptions {\n  keyframes?: KeyframeResolver;\n  duration?: number;\n  easing?: string;\n  sideEffects?: DropAnimationSideEffects | null;\n}\n\nexport type DropAnimation = DropAnimationFunction | DropAnimationOptions;\n\ninterface Arguments {\n  draggableNodes: DraggableNodes;\n  droppableContainers: DroppableContainers;\n  measuringConfiguration: DeepRequired<MeasuringConfiguration>;\n  config?: DropAnimation | null;\n}\n\nexport interface DropAnimationFunctionArguments extends SharedParameters {\n  transform: Transform;\n}\n\nexport type DropAnimationFunction = (\n  args: DropAnimationFunctionArguments\n) => Promise<void> | void;\n\ntype CleanupFunction = () => void;\n\nexport interface DropAnimationSideEffectsParameters extends SharedParameters {}\n\nexport type DropAnimationSideEffects = (\n  parameters: DropAnimationSideEffectsParameters\n) => CleanupFunction | void;\n\ntype ExtractStringProperties<T> = {\n  [K in keyof T]?: T[K] extends string ? string : never;\n};\n\ntype Styles = ExtractStringProperties<CSSStyleDeclaration>;\n\ninterface DefaultDropAnimationSideEffectsOptions {\n  className?: {\n    active?: string;\n    dragOverlay?: string;\n  };\n  styles?: {\n    active?: Styles;\n    dragOverlay?: Styles;\n  };\n}\n\nexport const defaultDropAnimationSideEffects = (\n  options: DefaultDropAnimationSideEffectsOptions\n): DropAnimationSideEffects => ({active, dragOverlay}) => {\n  const originalStyles: Record<string, string> = {};\n  const {styles, className} = options;\n\n  if (styles?.active) {\n    for (const [key, value] of Object.entries(styles.active)) {\n      if (value === undefined) {\n        continue;\n      }\n\n      originalStyles[key] = active.node.style.getPropertyValue(key);\n      active.node.style.setProperty(key, value);\n    }\n  }\n\n  if (styles?.dragOverlay) {\n    for (const [key, value] of Object.entries(styles.dragOverlay)) {\n      if (value === undefined) {\n        continue;\n      }\n\n      dragOverlay.node.style.setProperty(key, value);\n    }\n  }\n\n  if (className?.active) {\n    active.node.classList.add(className.active);\n  }\n\n  if (className?.dragOverlay) {\n    dragOverlay.node.classList.add(className.dragOverlay);\n  }\n\n  return function cleanup() {\n    for (const [key, value] of Object.entries(originalStyles)) {\n      active.node.style.setProperty(key, value);\n    }\n\n    if (className?.active) {\n      active.node.classList.remove(className.active);\n    }\n  };\n};\n\nconst defaultKeyframeResolver: KeyframeResolver = ({\n  transform: {initial, final},\n}) => [\n  {\n    transform: CSS.Transform.toString(initial),\n  },\n  {\n    transform: CSS.Transform.toString(final),\n  },\n];\n\nexport const defaultDropAnimationConfiguration: Required<DropAnimationOptions> = {\n  duration: 250,\n  easing: 'ease',\n  keyframes: defaultKeyframeResolver,\n  sideEffects: defaultDropAnimationSideEffects({\n    styles: {\n      active: {\n        opacity: '0',\n      },\n    },\n  }),\n};\n\nexport function useDropAnimation({\n  config,\n  draggableNodes,\n  droppableContainers,\n  measuringConfiguration,\n}: Arguments) {\n  return useEvent<Animation>((id, node) => {\n    if (config === null) {\n      return;\n    }\n\n    const activeDraggable: DraggableNode | undefined = draggableNodes.get(id);\n\n    if (!activeDraggable) {\n      return;\n    }\n\n    const activeNode = activeDraggable.node.current;\n\n    if (!activeNode) {\n      return;\n    }\n\n    const measurableNode = getMeasurableNode(node);\n\n    if (!measurableNode) {\n      return;\n    }\n    const {transform} = getWindow(node).getComputedStyle(node);\n    const parsedTransform = parseTransform(transform);\n\n    if (!parsedTransform) {\n      return;\n    }\n\n    const animation: DropAnimationFunction =\n      typeof config === 'function'\n        ? config\n        : createDefaultDropAnimation(config);\n\n    scrollIntoViewIfNeeded(\n      activeNode,\n      measuringConfiguration.draggable.measure\n    );\n\n    return animation({\n      active: {\n        id,\n        data: activeDraggable.data,\n        node: activeNode,\n        rect: measuringConfiguration.draggable.measure(activeNode),\n      },\n      draggableNodes,\n      dragOverlay: {\n        node,\n        rect: measuringConfiguration.dragOverlay.measure(measurableNode),\n      },\n      droppableContainers,\n      measuringConfiguration,\n      transform: parsedTransform,\n    });\n  });\n}\n\nfunction createDefaultDropAnimation(\n  options: DropAnimationOptions | undefined\n): DropAnimationFunction {\n  const {duration, easing, sideEffects, keyframes} = {\n    ...defaultDropAnimationConfiguration,\n    ...options,\n  };\n\n  return ({active, dragOverlay, transform, ...rest}) => {\n    if (!duration) {\n      // Do not animate if animation duration is zero.\n      return;\n    }\n\n    const delta = {\n      x: dragOverlay.rect.left - active.rect.left,\n      y: dragOverlay.rect.top - active.rect.top,\n    };\n\n    const scale = {\n      scaleX:\n        transform.scaleX !== 1\n          ? (active.rect.width * transform.scaleX) / dragOverlay.rect.width\n          : 1,\n      scaleY:\n        transform.scaleY !== 1\n          ? (active.rect.height * transform.scaleY) / dragOverlay.rect.height\n          : 1,\n    };\n    const finalTransform = {\n      x: transform.x - delta.x,\n      y: transform.y - delta.y,\n      ...scale,\n    };\n\n    const animationKeyframes = keyframes({\n      ...rest,\n      active,\n      dragOverlay,\n      transform: {initial: transform, final: finalTransform},\n    });\n\n    const [firstKeyframe] = animationKeyframes;\n    const lastKeyframe = animationKeyframes[animationKeyframes.length - 1];\n\n    if (JSON.stringify(firstKeyframe) === JSON.stringify(lastKeyframe)) {\n      // The start and end keyframes are the same, infer that there is no animation needed.\n      return;\n    }\n\n    const cleanup = sideEffects?.({active, dragOverlay, ...rest});\n    const animation = dragOverlay.node.animate(animationKeyframes, {\n      duration,\n      easing,\n      fill: 'forwards',\n    });\n\n    return new Promise((resolve) => {\n      animation.onfinish = () => {\n        cleanup?.();\n        resolve();\n      };\n    });\n  };\n}\n", "import {useMemo} from 'react';\n\nimport type {UniqueIdentifier} from '../../../types';\n\nlet key = 0;\n\nexport function useKey(id: UniqueIdentifier | undefined) {\n  return useMemo(() => {\n    if (id == null) {\n      return;\n    }\n\n    key++;\n    return key;\n  }, [id]);\n}\n", "import React, {useContext} from 'react';\n\nimport {applyModifiers, Modifiers} from '../../modifiers';\nimport {ActiveDraggableContext} from '../DndContext';\nimport {useDndContext} from '../../hooks';\nimport {useInitialValue} from '../../hooks/utilities';\n\nimport {\n  AnimationManager,\n  NullifiedContextProvider,\n  PositionedOverlay,\n} from './components';\nimport type {PositionedOverlayProps} from './components';\n\nimport {useDropAnimation, useKey} from './hooks';\nimport type {DropAnimation} from './hooks';\n\nexport interface Props\n  extends Pick<\n    PositionedOverlayProps,\n    'adjustScale' | 'children' | 'className' | 'style' | 'transition'\n  > {\n  dropAnimation?: DropAnimation | null | undefined;\n  modifiers?: Modifiers;\n  wrapperElement?: keyof JSX.IntrinsicElements;\n  zIndex?: number;\n}\n\nexport const DragOverlay = React.memo(\n  ({\n    adjustScale = false,\n    children,\n    dropAnimation: dropAnimationConfig,\n    style,\n    transition,\n    modifiers,\n    wrapperElement = 'div',\n    className,\n    zIndex = 999,\n  }: Props) => {\n    const {\n      activatorEvent,\n      active,\n      activeNodeRect,\n      containerNodeRect,\n      draggableNodes,\n      droppableContainers,\n      dragOverlay,\n      over,\n      measuringConfiguration,\n      scrollableAncestors,\n      scrollableAncestorRects,\n      windowRect,\n    } = useDndContext();\n    const transform = useContext(ActiveDraggableContext);\n    const key = useKey(active?.id);\n    const modifiedTransform = applyModifiers(modifiers, {\n      activatorEvent,\n      active,\n      activeNodeRect,\n      containerNodeRect,\n      draggingNodeRect: dragOverlay.rect,\n      over,\n      overlayNodeRect: dragOverlay.rect,\n      scrollableAncestors,\n      scrollableAncestorRects,\n      transform,\n      windowRect,\n    });\n    const initialRect = useInitialValue(activeNodeRect);\n    const dropAnimation = useDropAnimation({\n      config: dropAnimationConfig,\n      draggableNodes,\n      droppableContainers,\n      measuringConfiguration,\n    });\n    // We need to wait for the active node to be measured before connecting the drag overlay ref\n    // otherwise collisions can be computed against a mispositioned drag overlay\n    const ref = initialRect ? dragOverlay.setRef : undefined;\n\n    return (\n      <NullifiedContextProvider>\n        <AnimationManager animation={dropAnimation}>\n          {active && key ? (\n            <PositionedOverlay\n              key={key}\n              id={active.id}\n              ref={ref}\n              as={wrapperElement}\n              activatorEvent={activatorEvent}\n              adjustScale={adjustScale}\n              className={className}\n              transition={transition}\n              rect={initialRect}\n              style={{\n                zIndex,\n                ...style,\n              }}\n              transform={modifiedTransform}\n            >\n              {children}\n            </PositionedOverlay>\n          ) : null}\n        </AnimationManager>\n      </NullifiedContextProvider>\n    );\n  }\n);\n"], "names": ["DndMonitorContext", "createContext", "useDndMonitor", "listener", "registerListener", "useContext", "useEffect", "Error", "unsubscribe", "useDndMonitorProvider", "listeners", "useState", "Set", "useCallback", "add", "delete", "dispatch", "type", "event", "for<PERSON>ach", "defaultScreenReaderInstructions", "draggable", "defaultAnnouncements", "onDragStart", "active", "id", "onDragOver", "over", "onDragEnd", "onDragCancel", "Accessibility", "announcements", "container", "hiddenTextDescribedById", "screenReaderInstructions", "announce", "announcement", "useAnnouncement", "liveRegionId", "useUniqueId", "mounted", "setMounted", "useMemo", "onDragMove", "markup", "React", "HiddenText", "value", "LiveRegion", "createPortal", "Action", "noop", "useSensor", "sensor", "options", "useSensors", "sensors", "filter", "defaultCoordinates", "Object", "freeze", "x", "y", "distanceBetween", "p1", "p2", "Math", "sqrt", "pow", "getRelativeTransformOrigin", "rect", "eventCoordinates", "getEventCoordinates", "transform<PERSON><PERSON>in", "left", "width", "top", "height", "sortCollisionsAsc", "data", "a", "b", "sortCollisionsDesc", "cornersOfRectangle", "getFirstCollision", "collisions", "property", "length", "firstCollision", "centerOfRectangle", "closestCenter", "collisionRect", "droppableRects", "droppableContainers", "centerRect", "droppableContainer", "get", "distBetween", "push", "sort", "closestCorners", "corners", "rectCorners", "distances", "reduce", "accumulator", "corner", "index", "effectiveDistance", "Number", "toFixed", "getIntersectionRatio", "entry", "target", "max", "right", "min", "bottom", "targetArea", "entryArea", "intersectionArea", "intersectionRatio", "rectIntersection", "isPointWithinRect", "point", "pointer<PERSON><PERSON><PERSON>", "pointerCoordinates", "adjustScale", "transform", "rect1", "rect2", "scaleX", "scaleY", "getRectDelta", "createRectAdjustmentFn", "modifier", "adjustClientRect", "adjustments", "acc", "adjustment", "getAdjustedRect", "parseTransform", "startsWith", "transformArray", "slice", "split", "inverseTransform", "parsedTransform", "translateX", "translateY", "parseFloat", "indexOf", "w", "h", "defaultOptions", "ignoreTransform", "getClientRect", "element", "getBoundingClientRect", "getWindow", "getComputedStyle", "getTransformAgnosticClientRect", "getWindowClientRect", "innerWidth", "innerHeight", "isFixed", "node", "computedStyle", "position", "isScrollable", "overflowRegex", "properties", "some", "test", "getScrollableAncestors", "limit", "scrollParents", "findScrollableAncestors", "isDocument", "scrollingElement", "includes", "isHTMLElement", "isSVGElement", "parentNode", "getFirstScrollableAncestor", "firstScrollableAncestor", "getScrollableElement", "canUseDOM", "isWindow", "isNode", "getOwnerDocument", "window", "getScrollXCoordinate", "scrollX", "scrollLeft", "getScrollYCoordinate", "scrollY", "scrollTop", "getScrollCoordinates", "Direction", "isDocumentScrollingElement", "document", "getScrollPosition", "scrollingContainer", "minScroll", "dimensions", "clientHeight", "clientWidth", "maxScroll", "scrollWidth", "scrollHeight", "isTop", "isLeft", "isBottom", "isRight", "defaultThreshold", "getScrollDirectionAndSpeed", "scrollContainer", "scrollContainerRect", "acceleration", "thresholdPercentage", "direction", "speed", "threshold", "Backward", "abs", "Forward", "getScrollElementRect", "getScrollOffsets", "scrollableAncestors", "getScrollXOffset", "getScrollYOffset", "scrollIntoViewIfNeeded", "measure", "scrollIntoView", "block", "inline", "Rect", "constructor", "scrollOffsets", "axis", "keys", "getScrollOffset", "key", "defineProperty", "currentOffsets", "scrollOffsetsDeltla", "enumerable", "Listeners", "removeAll", "removeEventListener", "eventName", "handler", "addEventListener", "getEventListenerTarget", "EventTarget", "hasExceededDistance", "delta", "measurement", "dx", "dy", "EventName", "preventDefault", "stopPropagation", "KeyboardCode", "defaultKeyboardCodes", "start", "Space", "Enter", "cancel", "Esc", "end", "Tab", "defaultKeyboardCoordinateGetter", "currentCoordinates", "code", "Right", "Left", "Down", "Up", "undefined", "KeyboardSensor", "props", "autoScrollEnabled", "referenceCoordinates", "windowListeners", "handleKeyDown", "bind", "handleCancel", "attach", "handleStart", "Resize", "VisibilityChange", "setTimeout", "Keydown", "activeNode", "onStart", "current", "isKeyboardEvent", "context", "keyboardCodes", "coordinateGetter", "scroll<PERSON>eh<PERSON>or", "handleEnd", "newCoordinates", "coordinates<PERSON><PERSON><PERSON>", "getCoordinatesDelta", "scrollDelta", "scrollElementRect", "clampedCoordinates", "canScrollX", "canScrollY", "newScrollCoordinates", "canScrollToNewCoordinates", "scrollTo", "behavior", "scrollBy", "handleMove", "getAdjustedCoordinates", "coordinates", "onMove", "onEnd", "detach", "onCancel", "activators", "onActivation", "nativeEvent", "activator", "activatorNode", "isDistanceConstraint", "constraint", "Boolean", "isDelayConstraint", "AbstractPointerSensor", "events", "<PERSON><PERSON><PERSON><PERSON>", "activated", "initialCoordinates", "timeoutId", "documentListeners", "handleKeydown", "removeTextSelection", "activationConstraint", "bypassActivationConstraint", "move", "name", "passive", "DragStart", "ContextMenu", "delay", "handlePending", "clearTimeout", "offset", "onPending", "Click", "capture", "SelectionChange", "tolerance", "distance", "cancelable", "onAbort", "getSelection", "removeAllRanges", "PointerSensor", "isPrimary", "button", "MouseB<PERSON>on", "MouseSensor", "RightClick", "TouchSensor", "setup", "teardown", "touches", "AutoScrollActivator", "TraversalOrder", "useAutoScroller", "Pointer", "canScroll", "draggingRect", "enabled", "interval", "order", "TreeOrder", "scrollableAncestorRects", "scrollIntent", "useScrollIntent", "disabled", "setAutoScrollInterval", "clearAutoScrollInterval", "useInterval", "scrollSpeed", "useRef", "scrollDirection", "DraggableRect", "scrollContainerRef", "autoScroll", "sortedScrollableAncestors", "reverse", "JSON", "stringify", "defaultScrollIntent", "previousDel<PERSON>", "usePrevious", "useLazyMemo", "previousIntent", "sign", "useCachedNode", "draggableNodes", "draggableNode", "cachedNode", "useCombineActivators", "getSyntheticHandler", "Sensor", "sensorActivators", "map", "MeasuringStrategy", "MeasuringFrequency", "defaultValue", "Map", "useDroppableMeasuring", "containers", "dragging", "dependencies", "config", "queue", "setQueue", "frequency", "strategy", "containersRef", "isDisabled", "disabledRef", "useLatestValue", "measureDroppableContainers", "ids", "concat", "previousValue", "set", "measuringScheduled", "Always", "BeforeDragging", "useInitialValue", "computeFn", "useInitialRect", "useMutationObserver", "callback", "handleMutations", "useEvent", "mutationObserver", "MutationObserver", "disconnect", "useResizeObserver", "handleResize", "resizeObserver", "ResizeObserver", "defaultMeasure", "useRect", "fallbackRect", "setRect", "measureRect", "currentRect", "isConnected", "newRect", "records", "record", "HTMLElement", "contains", "useIsomorphicLayoutEffect", "observe", "body", "childList", "subtree", "useRectDelta", "initialRect", "useScrollableAncestors", "previousNode", "ancestors", "useScrollOffsets", "elements", "scrollCoordinates", "setScrollCoordinates", "prevElements", "handleScroll", "previousElements", "cleanup", "entries", "scrollableElement", "Array", "from", "values", "useScrollOffsetsDelta", "initialScrollOffsets", "hasScrollOffsets", "subtract", "useSensorSetup", "teardownFns", "useSyntheticListeners", "useWindowRect", "useRects", "firstElement", "windowRect", "rects", "setRects", "measureRects", "getMeasurableNode", "children", "<PERSON><PERSON><PERSON><PERSON>", "useDragOverlayMeasuring", "handleNodeChange", "nodeRef", "setRef", "useNodeRef", "defaultSensors", "defaultData", "defaultMeasuringConfiguration", "droppable", "WhileDragging", "Optimized", "dragOverlay", "DroppableContainersMap", "toArray", "getEnabled", "getNodeFor", "defaultPublicContext", "activatorEvent", "activeNodeRect", "containerNodeRect", "measuringConfiguration", "defaultInternalContext", "ariaDescribedById", "InternalContext", "PublicContext", "getInitialState", "nodes", "translate", "reducer", "state", "action", "<PERSON><PERSON><PERSON><PERSON>", "DragEnd", "DragCancel", "RegisterDroppable", "SetDroppableDisabled", "UnregisterDroppable", "RestoreFocus", "previousActivatorEvent", "previousActiveId", "activeElement", "requestAnimationFrame", "focusableNode", "findFirstFocusableNode", "focus", "applyModifiers", "modifiers", "args", "useMeasuringConfiguration", "useLayoutShiftScrollCompensation", "initialized", "rectD<PERSON><PERSON>", "ActiveDraggableContext", "Status", "DndContext", "memo", "accessibility", "collisionDetection", "measuring", "store", "useReducer", "dispatchMonitorEvent", "registerMonitorListener", "status", "setStatus", "Uninitialized", "isInitialized", "Initialized", "activeId", "activeRects", "initial", "translated", "activeRef", "activeSensor", "setActiveSensor", "setActivatorEvent", "latestProps", "draggableDescribedById", "enabledDroppableContainers", "activationCoordinates", "autoScrollOptions", "getAutoScrollerOptions", "initialActiveNodeRect", "layoutShiftCompensation", "parentElement", "sensorContext", "draggingNode", "draggingNodeRect", "scrollAdjustedTranslate", "overNode", "usesDragOverlay", "nodeRectDelta", "modifiedTranslate", "overlayNodeRect", "scrollAdjustment", "activeNodeScrollDelta", "overId", "setOver", "appliedTranslate", "activeSensorRef", "instantiateSensor", "sensorInstance", "onDragAbort", "onDragPending", "unstable_batchedUpdates", "Initializing", "createHandler", "cancelDrop", "shouldCancel", "Promise", "resolve", "bindActivatorToSensorInstantiator", "activeDraggableNode", "dndKit", "defaultPrevented", "activationContext", "shouldActivate", "capturedBy", "over<PERSON><PERSON><PERSON>", "publicContext", "internalContext", "Provider", "restoreFocus", "activeSensorDisablesAutoscroll", "autoScrollGloballyDisabled", "NullContext", "defaultRole", "ID_PREFIX", "useDraggable", "attributes", "role", "roleDescription", "tabIndex", "isDragging", "setNodeRef", "setActivatorNodeRef", "dataRef", "memoizedAttributes", "useDndContext", "defaultResizeObserverConfig", "timeout", "useDroppable", "resizeObserverConfig", "previous", "resizeObserverConnected", "callbackId", "resizeObserverDisabled", "updateMeasurementsFor", "resizeObserverTimeout", "isArray", "newElement", "previousElement", "unobserve", "isOver", "AnimationManager", "animation", "cloned<PERSON><PERSON><PERSON><PERSON>", "setClonedChildren", "setElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "then", "cloneElement", "ref", "defaultTransform", "NullifiedContextProvider", "baseStyles", "touchAction", "defaultTransition", "isKeyboardActivator", "PositionedOverlay", "forwardRef", "as", "className", "style", "transition", "scaleAdjustedTransform", "styles", "CSS", "Transform", "toString", "createElement", "defaultDropAnimationSideEffects", "originalStyles", "getPropertyValue", "setProperty", "classList", "remove", "defaultKeyframeResolver", "final", "defaultDropAnimationConfiguration", "duration", "easing", "keyframes", "sideEffects", "opacity", "useDropAnimation", "activeDraggable", "measurableNode", "createDefaultDropAnimation", "rest", "scale", "finalTransform", "animationKeyframes", "firstKeyframe", "lastKeyframe", "animate", "fill", "onfinish", "useKey", "DragOverlay", "dropAnimation", "dropAnimationConfig", "wrapperElement", "zIndex", "modifiedTransform"], "mappings": ";;;;;AAIO,MAAMA,iBAAiB,gBAAGC,aAAa,CAA0B,IAA1B,CAAvC;;SCCSC,cAAcC;EAC5B,MAAMC,gBAAgB,GAAGC,UAAU,CAACL,iBAAD,CAAnC;EAEAM,SAAS,CAAC;IACR,IAAI,CAACF,gBAAL,EAAuB;MACrB,MAAM,IAAIG,KAAJ,CACJ,8DADI,CAAN;;;IAKF,MAAMC,WAAW,GAAGJ,gBAAgB,CAACD,QAAD,CAApC;IAEA,OAAOK,WAAP;GATO,EAUN,CAACL,QAAD,EAAWC,gBAAX,CAVM,CAAT;AAWD;;SCfeK;EACd,MAAM,CAACC,SAAD,IAAcC,QAAQ,CAAC,MAAM,IAAIC,GAAJ,EAAP,CAA5B;EAEA,MAAMR,gBAAgB,GAAGS,WAAW,CACjCV,QAAD;IACEO,SAAS,CAACI,GAAV,CAAcX,QAAd;IACA,OAAO,MAAMO,SAAS,CAACK,MAAV,CAAiBZ,QAAjB,CAAb;GAHgC,EAKlC,CAACO,SAAD,CALkC,CAApC;EAQA,MAAMM,QAAQ,GAAGH,WAAW,CAC1B;QAAC;MAACI,IAAD;MAAOC;;IACNR,SAAS,CAACS,OAAV,CAAmBhB,QAAD;MAAA;;MAAA,yBAAcA,QAAQ,CAACc,IAAD,CAAtB,qBAAc,oBAAAd,QAAQ,EAASe,KAAT,CAAtB;KAAlB;GAFwB,EAI1B,CAACR,SAAD,CAJ0B,CAA5B;EAOA,OAAO,CAACM,QAAD,EAAWZ,gBAAX,CAAP;AACD;;MCrBYgB,+BAA+B,GAA6B;EACvEC,SAAS;AAD8D,CAAlE;AAQP,MAAaC,oBAAoB,GAAkB;EACjDC,WAAW;QAAC;MAACC;;IACX,qCAAmCA,MAAM,CAACC,EAA1C;GAF+C;;EAIjDC,UAAU;QAAC;MAACF,MAAD;MAASG;;;IAClB,IAAIA,IAAJ,EAAU;MACR,2BAAyBH,MAAM,CAACC,EAAhC,uCAAoEE,IAAI,CAACF,EAAzE;;;IAGF,2BAAyBD,MAAM,CAACC,EAAhC;GAT+C;;EAWjDG,SAAS;QAAC;MAACJ,MAAD;MAASG;;;IACjB,IAAIA,IAAJ,EAAU;MACR,2BAAyBH,MAAM,CAACC,EAAhC,yCAAsEE,IAAI,CAACF,EAA3E;;;IAGF,2BAAyBD,MAAM,CAACC,EAAhC;GAhB+C;;EAkBjDI,YAAY;QAAC;MAACL;;IACZ,mDAAiDA,MAAM,CAACC,EAAxD;;;AAnB+C,CAA5C;;SCUSK;MAAc;IAC5BC,aAAa,GAAGT,oBADY;IAE5BU,SAF4B;IAG5BC,uBAH4B;IAI5BC,wBAAwB,GAAGd;;EAE3B,MAAM;IAACe,QAAD;IAAWC;MAAgBC,eAAe,EAAhD;EACA,MAAMC,YAAY,GAAGC,WAAW,iBAAhC;EACA,MAAM,CAACC,OAAD,EAAUC,UAAV,IAAwB9B,QAAQ,CAAC,KAAD,CAAtC;EAEAL,SAAS,CAAC;IACRmC,UAAU,CAAC,IAAD,CAAV;GADO,EAEN,EAFM,CAAT;EAIAvC,aAAa,CACXwC,OAAO,CACL,OAAO;IACLnB,WAAW;UAAC;QAACC;;MACXW,QAAQ,CAACJ,aAAa,CAACR,WAAd,CAA0B;QAACC;OAA3B,CAAD,CAAR;KAFG;;IAILmB,UAAU;UAAC;QAACnB,MAAD;QAASG;;;MAClB,IAAII,aAAa,CAACY,UAAlB,EAA8B;QAC5BR,QAAQ,CAACJ,aAAa,CAACY,UAAd,CAAyB;UAACnB,MAAD;UAASG;SAAlC,CAAD,CAAR;;KANC;;IASLD,UAAU;UAAC;QAACF,MAAD;QAASG;;MAClBQ,QAAQ,CAACJ,aAAa,CAACL,UAAd,CAAyB;QAACF,MAAD;QAASG;OAAlC,CAAD,CAAR;KAVG;;IAYLC,SAAS;UAAC;QAACJ,MAAD;QAASG;;MACjBQ,QAAQ,CAACJ,aAAa,CAACH,SAAd,CAAwB;QAACJ,MAAD;QAASG;OAAjC,CAAD,CAAR;KAbG;;IAeLE,YAAY;UAAC;QAACL,MAAD;QAASG;;MACpBQ,QAAQ,CAACJ,aAAa,CAACF,YAAd,CAA2B;QAACL,MAAD;QAASG;OAApC,CAAD,CAAR;;;GAhBJ,CADK,EAoBL,CAACQ,QAAD,EAAWJ,aAAX,CApBK,CADI,CAAb;;EAyBA,IAAI,CAACS,OAAL,EAAc;IACZ,OAAO,IAAP;;;EAGF,MAAMI,MAAM,GACVC,mBAAA,eAAA,MAAA,EACEA,mBAAA,CAACC,UAAD;IACErB,EAAE,EAAEQ;IACJc,KAAK,EAAEb,wBAAwB,CAACb;GAFlC,CADF,EAKEwB,mBAAA,CAACG,UAAD;IAAYvB,EAAE,EAAEa;IAAcF,YAAY,EAAEA;GAA5C,CALF,CADF;EAUA,OAAOJ,SAAS,GAAGiB,YAAY,CAACL,MAAD,EAASZ,SAAT,CAAf,GAAqCY,MAArD;AACD;;ACvED,IAAYM,MAAZ;;AAAA,WAAYA;EACVA,mBAAA,cAAA;EACAA,kBAAA,aAAA;EACAA,iBAAA,YAAA;EACAA,oBAAA,eAAA;EACAA,kBAAA,aAAA;EACAA,2BAAA,sBAAA;EACAA,8BAAA,yBAAA;EACAA,6BAAA,wBAAA;AACD,CATD,EAAYA,MAAM,KAANA,MAAM,KAAA,CAAlB;;SCHgBC;;SCIAC,UACdC,QACAC;EAEA,OAAOZ,OAAO,CACZ,OAAO;IACLW,MADK;IAELC,OAAO,EAAEA,OAAF,WAAEA,OAAF,GAAc;GAFvB,CADY;EAMZ,CAACD,MAAD,EAASC,OAAT,CANY,CAAd;AAQD;;SCZeC;oCACXC;IAAAA;;;EAEH,OAAOd,OAAO,CACZ,MACE,CAAC,GAAGc,OAAJ,EAAaC,MAAb,CACGJ,MAAD,IAA6CA,MAAM,IAAI,IADzD,CAFU;EAMZ,CAAC,GAAGG,OAAJ,CANY,CAAd;AAQD;;MCbYE,kBAAkB,gBAAgBC,MAAM,CAACC,MAAP,CAAc;EAC3DC,CAAC,EAAE,CADwD;EAE3DC,CAAC,EAAE;AAFwD,CAAd,CAAxC;;ACAP;;;AAGA,SAAgBC,gBAAgBC,IAAiBC;EAC/C,OAAOC,IAAI,CAACC,IAAL,CAAUD,IAAI,CAACE,GAAL,CAASJ,EAAE,CAACH,CAAH,GAAOI,EAAE,CAACJ,CAAnB,EAAsB,CAAtB,IAA2BK,IAAI,CAACE,GAAL,CAASJ,EAAE,CAACF,CAAH,GAAOG,EAAE,CAACH,CAAnB,EAAsB,CAAtB,CAArC,CAAP;AACD;;SCJeO,2BACdnD,OACAoD;EAEA,MAAMC,gBAAgB,GAAGC,mBAAmB,CAACtD,KAAD,CAA5C;;EAEA,IAAI,CAACqD,gBAAL,EAAuB;IACrB,OAAO,KAAP;;;EAGF,MAAME,eAAe,GAAG;IACtBZ,CAAC,EAAG,CAACU,gBAAgB,CAACV,CAAjB,GAAqBS,IAAI,CAACI,IAA3B,IAAmCJ,IAAI,CAACK,KAAzC,GAAkD,GAD/B;IAEtBb,CAAC,EAAG,CAACS,gBAAgB,CAACT,CAAjB,GAAqBQ,IAAI,CAACM,GAA3B,IAAkCN,IAAI,CAACO,MAAxC,GAAkD;GAFvD;EAKA,OAAUJ,eAAe,CAACZ,CAA1B,UAAgCY,eAAe,CAACX,CAAhD;AACD;;ACdD;;;AAGA,SAAgBgB;MACd;IAACC,IAAI,EAAE;MAAChC,KAAK,EAAEiC;;;MACf;IAACD,IAAI,EAAE;MAAChC,KAAK,EAAEkC;;;EAEf,OAAOD,CAAC,GAAGC,CAAX;AACD;AAED;;;;AAGA,SAAgBC;MACd;IAACH,IAAI,EAAE;MAAChC,KAAK,EAAEiC;;;MACf;IAACD,IAAI,EAAE;MAAChC,KAAK,EAAEkC;;;EAEf,OAAOA,CAAC,GAAGD,CAAX;AACD;AAED;;;;;AAIA,SAAgBG;MAAmB;IAACT,IAAD;IAAOE,GAAP;IAAYC,MAAZ;IAAoBF;;EACrD,OAAO,CACL;IACEd,CAAC,EAAEa,IADL;IAEEZ,CAAC,EAAEc;GAHA,EAKL;IACEf,CAAC,EAAEa,IAAI,GAAGC,KADZ;IAEEb,CAAC,EAAEc;GAPA,EASL;IACEf,CAAC,EAAEa,IADL;IAEEZ,CAAC,EAAEc,GAAG,GAAGC;GAXN,EAaL;IACEhB,CAAC,EAAEa,IAAI,GAAGC,KADZ;IAEEb,CAAC,EAAEc,GAAG,GAAGC;GAfN,CAAP;AAkBD;AAaD,SAAgBO,kBACdC,YACAC;EAEA,IAAI,CAACD,UAAD,IAAeA,UAAU,CAACE,MAAX,KAAsB,CAAzC,EAA4C;IAC1C,OAAO,IAAP;;;EAGF,MAAM,CAACC,cAAD,IAAmBH,UAAzB;EAEA,OAAOC,QAAQ,GAAGE,cAAc,CAACF,QAAD,CAAjB,GAA8BE,cAA7C;AACD;;AClED;;;;AAGA,SAASC,iBAAT,CACEnB,IADF,EAEEI,IAFF,EAGEE,GAHF;MAEEF;IAAAA,OAAOJ,IAAI,CAACI;;;MACZE;IAAAA,MAAMN,IAAI,CAACM;;;EAEX,OAAO;IACLf,CAAC,EAAEa,IAAI,GAAGJ,IAAI,CAACK,KAAL,GAAa,GADlB;IAELb,CAAC,EAAEc,GAAG,GAAGN,IAAI,CAACO,MAAL,GAAc;GAFzB;AAID;AAED;;;;;;AAIA,MAAaa,aAAa,GAAuB;MAAC;IAChDC,aADgD;IAEhDC,cAFgD;IAGhDC;;EAEA,MAAMC,UAAU,GAAGL,iBAAiB,CAClCE,aADkC,EAElCA,aAAa,CAACjB,IAFoB,EAGlCiB,aAAa,CAACf,GAHoB,CAApC;EAKA,MAAMS,UAAU,GAA0B,EAA1C;;EAEA,KAAK,MAAMU,kBAAX,IAAiCF,mBAAjC,EAAsD;IACpD,MAAM;MAACpE;QAAMsE,kBAAb;IACA,MAAMzB,IAAI,GAAGsB,cAAc,CAACI,GAAf,CAAmBvE,EAAnB,CAAb;;IAEA,IAAI6C,IAAJ,EAAU;MACR,MAAM2B,WAAW,GAAGlC,eAAe,CAAC0B,iBAAiB,CAACnB,IAAD,CAAlB,EAA0BwB,UAA1B,CAAnC;MAEAT,UAAU,CAACa,IAAX,CAAgB;QAACzE,EAAD;QAAKsD,IAAI,EAAE;UAACgB,kBAAD;UAAqBhD,KAAK,EAAEkD;;OAAvD;;;;EAIJ,OAAOZ,UAAU,CAACc,IAAX,CAAgBrB,iBAAhB,CAAP;AACD,CAxBM;;ACnBP;;;;;AAIA,MAAasB,cAAc,GAAuB;MAAC;IACjDT,aADiD;IAEjDC,cAFiD;IAGjDC;;EAEA,MAAMQ,OAAO,GAAGlB,kBAAkB,CAACQ,aAAD,CAAlC;EACA,MAAMN,UAAU,GAA0B,EAA1C;;EAEA,KAAK,MAAMU,kBAAX,IAAiCF,mBAAjC,EAAsD;IACpD,MAAM;MAACpE;QAAMsE,kBAAb;IACA,MAAMzB,IAAI,GAAGsB,cAAc,CAACI,GAAf,CAAmBvE,EAAnB,CAAb;;IAEA,IAAI6C,IAAJ,EAAU;MACR,MAAMgC,WAAW,GAAGnB,kBAAkB,CAACb,IAAD,CAAtC;MACA,MAAMiC,SAAS,GAAGF,OAAO,CAACG,MAAR,CAAe,CAACC,WAAD,EAAcC,MAAd,EAAsBC,KAAtB;QAC/B,OAAOF,WAAW,GAAG1C,eAAe,CAACuC,WAAW,CAACK,KAAD,CAAZ,EAAqBD,MAArB,CAApC;OADgB,EAEf,CAFe,CAAlB;MAGA,MAAME,iBAAiB,GAAGC,MAAM,CAAC,CAACN,SAAS,GAAG,CAAb,EAAgBO,OAAhB,CAAwB,CAAxB,CAAD,CAAhC;MAEAzB,UAAU,CAACa,IAAX,CAAgB;QACdzE,EADc;QAEdsD,IAAI,EAAE;UAACgB,kBAAD;UAAqBhD,KAAK,EAAE6D;;OAFpC;;;;EAOJ,OAAOvB,UAAU,CAACc,IAAX,CAAgBrB,iBAAhB,CAAP;AACD,CA3BM;;ACJP;;;;AAGA,SAAgBiC,qBACdC,OACAC;EAEA,MAAMrC,GAAG,GAAGV,IAAI,CAACgD,GAAL,CAASD,MAAM,CAACrC,GAAhB,EAAqBoC,KAAK,CAACpC,GAA3B,CAAZ;EACA,MAAMF,IAAI,GAAGR,IAAI,CAACgD,GAAL,CAASD,MAAM,CAACvC,IAAhB,EAAsBsC,KAAK,CAACtC,IAA5B,CAAb;EACA,MAAMyC,KAAK,GAAGjD,IAAI,CAACkD,GAAL,CAASH,MAAM,CAACvC,IAAP,GAAcuC,MAAM,CAACtC,KAA9B,EAAqCqC,KAAK,CAACtC,IAAN,GAAasC,KAAK,CAACrC,KAAxD,CAAd;EACA,MAAM0C,MAAM,GAAGnD,IAAI,CAACkD,GAAL,CAASH,MAAM,CAACrC,GAAP,GAAaqC,MAAM,CAACpC,MAA7B,EAAqCmC,KAAK,CAACpC,GAAN,GAAYoC,KAAK,CAACnC,MAAvD,CAAf;EACA,MAAMF,KAAK,GAAGwC,KAAK,GAAGzC,IAAtB;EACA,MAAMG,MAAM,GAAGwC,MAAM,GAAGzC,GAAxB;;EAEA,IAAIF,IAAI,GAAGyC,KAAP,IAAgBvC,GAAG,GAAGyC,MAA1B,EAAkC;IAChC,MAAMC,UAAU,GAAGL,MAAM,CAACtC,KAAP,GAAesC,MAAM,CAACpC,MAAzC;IACA,MAAM0C,SAAS,GAAGP,KAAK,CAACrC,KAAN,GAAcqC,KAAK,CAACnC,MAAtC;IACA,MAAM2C,gBAAgB,GAAG7C,KAAK,GAAGE,MAAjC;IACA,MAAM4C,iBAAiB,GACrBD,gBAAgB,IAAIF,UAAU,GAAGC,SAAb,GAAyBC,gBAA7B,CADlB;IAGA,OAAOX,MAAM,CAACY,iBAAiB,CAACX,OAAlB,CAA0B,CAA1B,CAAD,CAAb;;;;EAIF,OAAO,CAAP;AACD;AAED;;;;;AAIA,MAAaY,gBAAgB,GAAuB;MAAC;IACnD/B,aADmD;IAEnDC,cAFmD;IAGnDC;;EAEA,MAAMR,UAAU,GAA0B,EAA1C;;EAEA,KAAK,MAAMU,kBAAX,IAAiCF,mBAAjC,EAAsD;IACpD,MAAM;MAACpE;QAAMsE,kBAAb;IACA,MAAMzB,IAAI,GAAGsB,cAAc,CAACI,GAAf,CAAmBvE,EAAnB,CAAb;;IAEA,IAAI6C,IAAJ,EAAU;MACR,MAAMmD,iBAAiB,GAAGV,oBAAoB,CAACzC,IAAD,EAAOqB,aAAP,CAA9C;;MAEA,IAAI8B,iBAAiB,GAAG,CAAxB,EAA2B;QACzBpC,UAAU,CAACa,IAAX,CAAgB;UACdzE,EADc;UAEdsD,IAAI,EAAE;YAACgB,kBAAD;YAAqBhD,KAAK,EAAE0E;;SAFpC;;;;;EAQN,OAAOpC,UAAU,CAACc,IAAX,CAAgBjB,kBAAhB,CAAP;AACD,CAxBM;;AC/BP;;;;AAGA,SAASyC,iBAAT,CAA2BC,KAA3B,EAA+CtD,IAA/C;EACE,MAAM;IAACM,GAAD;IAAMF,IAAN;IAAY2C,MAAZ;IAAoBF;MAAS7C,IAAnC;EAEA,OACEM,GAAG,IAAIgD,KAAK,CAAC9D,CAAb,IAAkB8D,KAAK,CAAC9D,CAAN,IAAWuD,MAA7B,IAAuC3C,IAAI,IAAIkD,KAAK,CAAC/D,CAArD,IAA0D+D,KAAK,CAAC/D,CAAN,IAAWsD,KADvE;AAGD;AAED;;;;;AAGA,MAAaU,aAAa,GAAuB;MAAC;IAChDhC,mBADgD;IAEhDD,cAFgD;IAGhDkC;;;EAEA,IAAI,CAACA,kBAAL,EAAyB;IACvB,OAAO,EAAP;;;EAGF,MAAMzC,UAAU,GAA0B,EAA1C;;EAEA,KAAK,MAAMU,kBAAX,IAAiCF,mBAAjC,EAAsD;IACpD,MAAM;MAACpE;QAAMsE,kBAAb;IACA,MAAMzB,IAAI,GAAGsB,cAAc,CAACI,GAAf,CAAmBvE,EAAnB,CAAb;;IAEA,IAAI6C,IAAI,IAAIqD,iBAAiB,CAACG,kBAAD,EAAqBxD,IAArB,CAA7B,EAAyD;;;;;;MAMvD,MAAM+B,OAAO,GAAGlB,kBAAkB,CAACb,IAAD,CAAlC;MACA,MAAMiC,SAAS,GAAGF,OAAO,CAACG,MAAR,CAAe,CAACC,WAAD,EAAcC,MAAd;QAC/B,OAAOD,WAAW,GAAG1C,eAAe,CAAC+D,kBAAD,EAAqBpB,MAArB,CAApC;OADgB,EAEf,CAFe,CAAlB;MAGA,MAAME,iBAAiB,GAAGC,MAAM,CAAC,CAACN,SAAS,GAAG,CAAb,EAAgBO,OAAhB,CAAwB,CAAxB,CAAD,CAAhC;MAEAzB,UAAU,CAACa,IAAX,CAAgB;QACdzE,EADc;QAEdsD,IAAI,EAAE;UAACgB,kBAAD;UAAqBhD,KAAK,EAAE6D;;OAFpC;;;;EAOJ,OAAOvB,UAAU,CAACc,IAAX,CAAgBrB,iBAAhB,CAAP;AACD,CAnCM;;SCjBSiD,YACdC,WACAC,OACAC;EAEA,OAAO,EACL,GAAGF,SADE;IAELG,MAAM,EAAEF,KAAK,IAAIC,KAAT,GAAiBD,KAAK,CAACtD,KAAN,GAAcuD,KAAK,CAACvD,KAArC,GAA6C,CAFhD;IAGLyD,MAAM,EAAEH,KAAK,IAAIC,KAAT,GAAiBD,KAAK,CAACpD,MAAN,GAAeqD,KAAK,CAACrD,MAAtC,GAA+C;GAHzD;AAKD;;SCVewD,aACdJ,OACAC;EAEA,OAAOD,KAAK,IAAIC,KAAT,GACH;IACErE,CAAC,EAAEoE,KAAK,CAACvD,IAAN,GAAawD,KAAK,CAACxD,IADxB;IAEEZ,CAAC,EAAEmE,KAAK,CAACrD,GAAN,GAAYsD,KAAK,CAACtD;GAHpB,GAKHlB,kBALJ;AAMD;;SCXe4E,uBAAuBC;EACrC,OAAO,SAASC,gBAAT,CACLlE,IADK;sCAEFmE;MAAAA;;;IAEH,OAAOA,WAAW,CAACjC,MAAZ,CACL,CAACkC,GAAD,EAAMC,UAAN,MAAsB,EACpB,GAAGD,GADiB;MAEpB9D,GAAG,EAAE8D,GAAG,CAAC9D,GAAJ,GAAU2D,QAAQ,GAAGI,UAAU,CAAC7E,CAFjB;MAGpBuD,MAAM,EAAEqB,GAAG,CAACrB,MAAJ,GAAakB,QAAQ,GAAGI,UAAU,CAAC7E,CAHvB;MAIpBY,IAAI,EAAEgE,GAAG,CAAChE,IAAJ,GAAW6D,QAAQ,GAAGI,UAAU,CAAC9E,CAJnB;MAKpBsD,KAAK,EAAEuB,GAAG,CAACvB,KAAJ,GAAYoB,QAAQ,GAAGI,UAAU,CAAC9E;KAL3C,CADK,EAQL,EAAC,GAAGS;KARC,CAAP;GAJF;AAeD;AAED,AAAO,MAAMsE,eAAe,gBAAGN,sBAAsB,CAAC,CAAD,CAA9C;;SClBSO,eAAeb;EAC7B,IAAIA,SAAS,CAACc,UAAV,CAAqB,WAArB,CAAJ,EAAuC;IACrC,MAAMC,cAAc,GAAGf,SAAS,CAACgB,KAAV,CAAgB,CAAhB,EAAmB,CAAC,CAApB,EAAuBC,KAAvB,CAA6B,IAA7B,CAAvB;IAEA,OAAO;MACLpF,CAAC,EAAE,CAACkF,cAAc,CAAC,EAAD,CADb;MAELjF,CAAC,EAAE,CAACiF,cAAc,CAAC,EAAD,CAFb;MAGLZ,MAAM,EAAE,CAACY,cAAc,CAAC,CAAD,CAHlB;MAILX,MAAM,EAAE,CAACW,cAAc,CAAC,CAAD;KAJzB;GAHF,MASO,IAAIf,SAAS,CAACc,UAAV,CAAqB,SAArB,CAAJ,EAAqC;IAC1C,MAAMC,cAAc,GAAGf,SAAS,CAACgB,KAAV,CAAgB,CAAhB,EAAmB,CAAC,CAApB,EAAuBC,KAAvB,CAA6B,IAA7B,CAAvB;IAEA,OAAO;MACLpF,CAAC,EAAE,CAACkF,cAAc,CAAC,CAAD,CADb;MAELjF,CAAC,EAAE,CAACiF,cAAc,CAAC,CAAD,CAFb;MAGLZ,MAAM,EAAE,CAACY,cAAc,CAAC,CAAD,CAHlB;MAILX,MAAM,EAAE,CAACW,cAAc,CAAC,CAAD;KAJzB;;;EAQF,OAAO,IAAP;AACD;;SCpBeG,iBACd5E,MACA0D,WACAvD;EAEA,MAAM0E,eAAe,GAAGN,cAAc,CAACb,SAAD,CAAtC;;EAEA,IAAI,CAACmB,eAAL,EAAsB;IACpB,OAAO7E,IAAP;;;EAGF,MAAM;IAAC6D,MAAD;IAASC,MAAT;IAAiBvE,CAAC,EAAEuF,UAApB;IAAgCtF,CAAC,EAAEuF;MAAcF,eAAvD;EAEA,MAAMtF,CAAC,GAAGS,IAAI,CAACI,IAAL,GAAY0E,UAAZ,GAAyB,CAAC,IAAIjB,MAAL,IAAemB,UAAU,CAAC7E,eAAD,CAA5D;EACA,MAAMX,CAAC,GACLQ,IAAI,CAACM,GAAL,GACAyE,UADA,GAEA,CAAC,IAAIjB,MAAL,IACEkB,UAAU,CAAC7E,eAAe,CAACuE,KAAhB,CAAsBvE,eAAe,CAAC8E,OAAhB,CAAwB,GAAxB,IAA+B,CAArD,CAAD,CAJd;EAKA,MAAMC,CAAC,GAAGrB,MAAM,GAAG7D,IAAI,CAACK,KAAL,GAAawD,MAAhB,GAAyB7D,IAAI,CAACK,KAA9C;EACA,MAAM8E,CAAC,GAAGrB,MAAM,GAAG9D,IAAI,CAACO,MAAL,GAAcuD,MAAjB,GAA0B9D,IAAI,CAACO,MAA/C;EAEA,OAAO;IACLF,KAAK,EAAE6E,CADF;IAEL3E,MAAM,EAAE4E,CAFH;IAGL7E,GAAG,EAAEd,CAHA;IAILqD,KAAK,EAAEtD,CAAC,GAAG2F,CAJN;IAKLnC,MAAM,EAAEvD,CAAC,GAAG2F,CALP;IAML/E,IAAI,EAAEb;GANR;AAQD;;ACzBD,MAAM6F,cAAc,GAAY;EAACC,eAAe,EAAE;AAAlB,CAAhC;AAEA;;;;AAGA,SAAgBC,cACdC,SACAvG;MAAAA;IAAAA,UAAmBoG;;;EAEnB,IAAIpF,IAAI,GAAeuF,OAAO,CAACC,qBAAR,EAAvB;;EAEA,IAAIxG,OAAO,CAACqG,eAAZ,EAA6B;IAC3B,MAAM;MAAC3B,SAAD;MAAYvD;QAChBsF,SAAS,CAACF,OAAD,CAAT,CAAmBG,gBAAnB,CAAoCH,OAApC,CADF;;IAGA,IAAI7B,SAAJ,EAAe;MACb1D,IAAI,GAAG4E,gBAAgB,CAAC5E,IAAD,EAAO0D,SAAP,EAAkBvD,eAAlB,CAAvB;;;;EAIJ,MAAM;IAACG,GAAD;IAAMF,IAAN;IAAYC,KAAZ;IAAmBE,MAAnB;IAA2BwC,MAA3B;IAAmCF;MAAS7C,IAAlD;EAEA,OAAO;IACLM,GADK;IAELF,IAFK;IAGLC,KAHK;IAILE,MAJK;IAKLwC,MALK;IAMLF;GANF;AAQD;AAED;;;;;;;;;AAQA,SAAgB8C,+BAA+BJ;EAC7C,OAAOD,aAAa,CAACC,OAAD,EAAU;IAACF,eAAe,EAAE;GAA5B,CAApB;AACD;;SCjDeO,oBAAoBL;EAClC,MAAMlF,KAAK,GAAGkF,OAAO,CAACM,UAAtB;EACA,MAAMtF,MAAM,GAAGgF,OAAO,CAACO,WAAvB;EAEA,OAAO;IACLxF,GAAG,EAAE,CADA;IAELF,IAAI,EAAE,CAFD;IAGLyC,KAAK,EAAExC,KAHF;IAIL0C,MAAM,EAAExC,MAJH;IAKLF,KALK;IAMLE;GANF;AAQD;;SCZewF,QACdC,MACAC;MAAAA;IAAAA,gBAAqCR,SAAS,CAACO,IAAD,CAAT,CAAgBN,gBAAhB,CAAiCM,IAAjC;;;EAErC,OAAOC,aAAa,CAACC,QAAd,KAA2B,OAAlC;AACD;;SCLeC,aACdZ,SACAU;MAAAA;IAAAA,gBAAqCR,SAAS,CAACF,OAAD,CAAT,CAAmBG,gBAAnB,CACnCH,OADmC;;;EAIrC,MAAMa,aAAa,GAAG,uBAAtB;EACA,MAAMC,UAAU,GAAG,CAAC,UAAD,EAAa,WAAb,EAA0B,WAA1B,CAAnB;EAEA,OAAOA,UAAU,CAACC,IAAX,CAAiBtF,QAAD;IACrB,MAAMvC,KAAK,GAAGwH,aAAa,CAACjF,QAAD,CAA3B;IAEA,OAAO,OAAOvC,KAAP,KAAiB,QAAjB,GAA4B2H,aAAa,CAACG,IAAd,CAAmB9H,KAAnB,CAA5B,GAAwD,KAA/D;GAHK,CAAP;AAKD;;SCNe+H,uBACdjB,SACAkB;EAEA,MAAMC,aAAa,GAAc,EAAjC;;EAEA,SAASC,uBAAT,CAAiCX,IAAjC;IACE,IAAIS,KAAK,IAAI,IAAT,IAAiBC,aAAa,CAACzF,MAAd,IAAwBwF,KAA7C,EAAoD;MAClD,OAAOC,aAAP;;;IAGF,IAAI,CAACV,IAAL,EAAW;MACT,OAAOU,aAAP;;;IAGF,IACEE,UAAU,CAACZ,IAAD,CAAV,IACAA,IAAI,CAACa,gBAAL,IAAyB,IADzB,IAEA,CAACH,aAAa,CAACI,QAAd,CAAuBd,IAAI,CAACa,gBAA5B,CAHH,EAIE;MACAH,aAAa,CAAC9E,IAAd,CAAmBoE,IAAI,CAACa,gBAAxB;MAEA,OAAOH,aAAP;;;IAGF,IAAI,CAACK,aAAa,CAACf,IAAD,CAAd,IAAwBgB,YAAY,CAAChB,IAAD,CAAxC,EAAgD;MAC9C,OAAOU,aAAP;;;IAGF,IAAIA,aAAa,CAACI,QAAd,CAAuBd,IAAvB,CAAJ,EAAkC;MAChC,OAAOU,aAAP;;;IAGF,MAAMT,aAAa,GAAGR,SAAS,CAACF,OAAD,CAAT,CAAmBG,gBAAnB,CAAoCM,IAApC,CAAtB;;IAEA,IAAIA,IAAI,KAAKT,OAAb,EAAsB;MACpB,IAAIY,YAAY,CAACH,IAAD,EAAOC,aAAP,CAAhB,EAAuC;QACrCS,aAAa,CAAC9E,IAAd,CAAmBoE,IAAnB;;;;IAIJ,IAAID,OAAO,CAACC,IAAD,EAAOC,aAAP,CAAX,EAAkC;MAChC,OAAOS,aAAP;;;IAGF,OAAOC,uBAAuB,CAACX,IAAI,CAACiB,UAAN,CAA9B;;;EAGF,IAAI,CAAC1B,OAAL,EAAc;IACZ,OAAOmB,aAAP;;;EAGF,OAAOC,uBAAuB,CAACpB,OAAD,CAA9B;AACD;AAED,SAAgB2B,2BAA2BlB;EACzC,MAAM,CAACmB,uBAAD,IAA4BX,sBAAsB,CAACR,IAAD,EAAO,CAAP,CAAxD;EAEA,OAAOmB,uBAAP,WAAOA,uBAAP,GAAkC,IAAlC;AACD;;SC5DeC,qBAAqB7B;EACnC,IAAI,CAAC8B,SAAD,IAAc,CAAC9B,OAAnB,EAA4B;IAC1B,OAAO,IAAP;;;EAGF,IAAI+B,QAAQ,CAAC/B,OAAD,CAAZ,EAAuB;IACrB,OAAOA,OAAP;;;EAGF,IAAI,CAACgC,MAAM,CAAChC,OAAD,CAAX,EAAsB;IACpB,OAAO,IAAP;;;EAGF,IACEqB,UAAU,CAACrB,OAAD,CAAV,IACAA,OAAO,KAAKiC,gBAAgB,CAACjC,OAAD,CAAhB,CAA0BsB,gBAFxC,EAGE;IACA,OAAOY,MAAP;;;EAGF,IAAIV,aAAa,CAACxB,OAAD,CAAjB,EAA4B;IAC1B,OAAOA,OAAP;;;EAGF,OAAO,IAAP;AACD;;SC9BemC,qBAAqBnC;EACnC,IAAI+B,QAAQ,CAAC/B,OAAD,CAAZ,EAAuB;IACrB,OAAOA,OAAO,CAACoC,OAAf;;;EAGF,OAAOpC,OAAO,CAACqC,UAAf;AACD;AAED,SAAgBC,qBAAqBtC;EACnC,IAAI+B,QAAQ,CAAC/B,OAAD,CAAZ,EAAuB;IACrB,OAAOA,OAAO,CAACuC,OAAf;;;EAGF,OAAOvC,OAAO,CAACwC,SAAf;AACD;AAED,SAAgBC,qBACdzC;EAEA,OAAO;IACLhG,CAAC,EAAEmI,oBAAoB,CAACnC,OAAD,CADlB;IAEL/F,CAAC,EAAEqI,oBAAoB,CAACtC,OAAD;GAFzB;AAID;;AC3BD,IAAY0C,SAAZ;;AAAA,WAAYA;EACVA,mCAAA,YAAA;EACAA,qCAAA,aAAA;AACD,CAHD,EAAYA,SAAS,KAATA,SAAS,KAAA,CAArB;;SCEgBC,2BAA2B3C;EACzC,IAAI,CAAC8B,SAAD,IAAc,CAAC9B,OAAnB,EAA4B;IAC1B,OAAO,KAAP;;;EAGF,OAAOA,OAAO,KAAK4C,QAAQ,CAACtB,gBAA5B;AACD;;SCNeuB,kBAAkBC;EAChC,MAAMC,SAAS,GAAG;IAChB/I,CAAC,EAAE,CADa;IAEhBC,CAAC,EAAE;GAFL;EAIA,MAAM+I,UAAU,GAAGL,0BAA0B,CAACG,kBAAD,CAA1B,GACf;IACE9H,MAAM,EAAEkH,MAAM,CAAC3B,WADjB;IAEEzF,KAAK,EAAEoH,MAAM,CAAC5B;GAHD,GAKf;IACEtF,MAAM,EAAE8H,kBAAkB,CAACG,YAD7B;IAEEnI,KAAK,EAAEgI,kBAAkB,CAACI;GAPhC;EASA,MAAMC,SAAS,GAAG;IAChBnJ,CAAC,EAAE8I,kBAAkB,CAACM,WAAnB,GAAiCJ,UAAU,CAAClI,KAD/B;IAEhBb,CAAC,EAAE6I,kBAAkB,CAACO,YAAnB,GAAkCL,UAAU,CAAChI;GAFlD;EAKA,MAAMsI,KAAK,GAAGR,kBAAkB,CAACN,SAAnB,IAAgCO,SAAS,CAAC9I,CAAxD;EACA,MAAMsJ,MAAM,GAAGT,kBAAkB,CAACT,UAAnB,IAAiCU,SAAS,CAAC/I,CAA1D;EACA,MAAMwJ,QAAQ,GAAGV,kBAAkB,CAACN,SAAnB,IAAgCW,SAAS,CAAClJ,CAA3D;EACA,MAAMwJ,OAAO,GAAGX,kBAAkB,CAACT,UAAnB,IAAiCc,SAAS,CAACnJ,CAA3D;EAEA,OAAO;IACLsJ,KADK;IAELC,MAFK;IAGLC,QAHK;IAILC,OAJK;IAKLN,SALK;IAMLJ;GANF;AAQD;;AC5BD,MAAMW,gBAAgB,GAAG;EACvB1J,CAAC,EAAE,GADoB;EAEvBC,CAAC,EAAE;AAFoB,CAAzB;AAKA,SAAgB0J,2BACdC,iBACAC,2BAEAC,cACAC;MAFA;IAAChJ,GAAD;IAAMF,IAAN;IAAYyC,KAAZ;IAAmBE;;;MACnBsG;IAAAA,eAAe;;;MACfC;IAAAA,sBAAsBL;;;EAEtB,MAAM;IAACJ,KAAD;IAAQE,QAAR;IAAkBD,MAAlB;IAA0BE;MAAWZ,iBAAiB,CAACe,eAAD,CAA5D;EAEA,MAAMI,SAAS,GAAG;IAChBhK,CAAC,EAAE,CADa;IAEhBC,CAAC,EAAE;GAFL;EAIA,MAAMgK,KAAK,GAAG;IACZjK,CAAC,EAAE,CADS;IAEZC,CAAC,EAAE;GAFL;EAIA,MAAMiK,SAAS,GAAG;IAChBlJ,MAAM,EAAE6I,mBAAmB,CAAC7I,MAApB,GAA6B+I,mBAAmB,CAAC9J,CADzC;IAEhBa,KAAK,EAAE+I,mBAAmB,CAAC/I,KAApB,GAA4BiJ,mBAAmB,CAAC/J;GAFzD;;EAKA,IAAI,CAACsJ,KAAD,IAAUvI,GAAG,IAAI8I,mBAAmB,CAAC9I,GAApB,GAA0BmJ,SAAS,CAAClJ,MAAzD,EAAiE;;IAE/DgJ,SAAS,CAAC/J,CAAV,GAAcyI,SAAS,CAACyB,QAAxB;IACAF,KAAK,CAAChK,CAAN,GACE6J,YAAY,GACZzJ,IAAI,CAAC+J,GAAL,CACE,CAACP,mBAAmB,CAAC9I,GAApB,GAA0BmJ,SAAS,CAAClJ,MAApC,GAA6CD,GAA9C,IAAqDmJ,SAAS,CAAClJ,MADjE,CAFF;GAHF,MAQO,IACL,CAACwI,QAAD,IACAhG,MAAM,IAAIqG,mBAAmB,CAACrG,MAApB,GAA6B0G,SAAS,CAAClJ,MAF5C,EAGL;;IAEAgJ,SAAS,CAAC/J,CAAV,GAAcyI,SAAS,CAAC2B,OAAxB;IACAJ,KAAK,CAAChK,CAAN,GACE6J,YAAY,GACZzJ,IAAI,CAAC+J,GAAL,CACE,CAACP,mBAAmB,CAACrG,MAApB,GAA6B0G,SAAS,CAAClJ,MAAvC,GAAgDwC,MAAjD,IACE0G,SAAS,CAAClJ,MAFd,CAFF;;;EAQF,IAAI,CAACyI,OAAD,IAAYnG,KAAK,IAAIuG,mBAAmB,CAACvG,KAApB,GAA4B4G,SAAS,CAACpJ,KAA/D,EAAsE;;IAEpEkJ,SAAS,CAAChK,CAAV,GAAc0I,SAAS,CAAC2B,OAAxB;IACAJ,KAAK,CAACjK,CAAN,GACE8J,YAAY,GACZzJ,IAAI,CAAC+J,GAAL,CACE,CAACP,mBAAmB,CAACvG,KAApB,GAA4B4G,SAAS,CAACpJ,KAAtC,GAA8CwC,KAA/C,IAAwD4G,SAAS,CAACpJ,KADpE,CAFF;GAHF,MAQO,IAAI,CAACyI,MAAD,IAAW1I,IAAI,IAAIgJ,mBAAmB,CAAChJ,IAApB,GAA2BqJ,SAAS,CAACpJ,KAA5D,EAAmE;;IAExEkJ,SAAS,CAAChK,CAAV,GAAc0I,SAAS,CAACyB,QAAxB;IACAF,KAAK,CAACjK,CAAN,GACE8J,YAAY,GACZzJ,IAAI,CAAC+J,GAAL,CACE,CAACP,mBAAmB,CAAChJ,IAApB,GAA2BqJ,SAAS,CAACpJ,KAArC,GAA6CD,IAA9C,IAAsDqJ,SAAS,CAACpJ,KADlE,CAFF;;;EAOF,OAAO;IACLkJ,SADK;IAELC;GAFF;AAID;;SC7EeK,qBAAqBtE;EACnC,IAAIA,OAAO,KAAK4C,QAAQ,CAACtB,gBAAzB,EAA2C;IACzC,MAAM;MAAChB,UAAD;MAAaC;QAAe2B,MAAlC;IAEA,OAAO;MACLnH,GAAG,EAAE,CADA;MAELF,IAAI,EAAE,CAFD;MAGLyC,KAAK,EAAEgD,UAHF;MAIL9C,MAAM,EAAE+C,WAJH;MAKLzF,KAAK,EAAEwF,UALF;MAMLtF,MAAM,EAAEuF;KANV;;;EAUF,MAAM;IAACxF,GAAD;IAAMF,IAAN;IAAYyC,KAAZ;IAAmBE;MAAUwC,OAAO,CAACC,qBAAR,EAAnC;EAEA,OAAO;IACLlF,GADK;IAELF,IAFK;IAGLyC,KAHK;IAILE,MAJK;IAKL1C,KAAK,EAAEkF,OAAO,CAACkD,WALV;IAMLlI,MAAM,EAAEgF,OAAO,CAACiD;GANlB;AAQD;;SCdesB,iBAAiBC;EAC/B,OAAOA,mBAAmB,CAAC7H,MAApB,CAAwC,CAACkC,GAAD,EAAM4B,IAAN;IAC7C,OAAOxJ,GAAG,CAAC4H,GAAD,EAAM4D,oBAAoB,CAAChC,IAAD,CAA1B,CAAV;GADK,EAEJ5G,kBAFI,CAAP;AAGD;AAED,SAAgB4K,iBAAiBD;EAC/B,OAAOA,mBAAmB,CAAC7H,MAApB,CAAmC,CAACkC,GAAD,EAAM4B,IAAN;IACxC,OAAO5B,GAAG,GAAGsD,oBAAoB,CAAC1B,IAAD,CAAjC;GADK,EAEJ,CAFI,CAAP;AAGD;AAED,SAAgBiE,iBAAiBF;EAC/B,OAAOA,mBAAmB,CAAC7H,MAApB,CAAmC,CAACkC,GAAD,EAAM4B,IAAN;IACxC,OAAO5B,GAAG,GAAGyD,oBAAoB,CAAC7B,IAAD,CAAjC;GADK,EAEJ,CAFI,CAAP;AAGD;;SCtBekE,uBACd3E,SACA4E;MAAAA;IAAAA,UAA6C7E;;;EAE7C,IAAI,CAACC,OAAL,EAAc;IACZ;;;EAGF,MAAM;IAACjF,GAAD;IAAMF,IAAN;IAAY2C,MAAZ;IAAoBF;MAASsH,OAAO,CAAC5E,OAAD,CAA1C;EACA,MAAM4B,uBAAuB,GAAGD,0BAA0B,CAAC3B,OAAD,CAA1D;;EAEA,IAAI,CAAC4B,uBAAL,EAA8B;IAC5B;;;EAGF,IACEpE,MAAM,IAAI,CAAV,IACAF,KAAK,IAAI,CADT,IAEAvC,GAAG,IAAImH,MAAM,CAAC3B,WAFd,IAGA1F,IAAI,IAAIqH,MAAM,CAAC5B,UAJjB,EAKE;IACAN,OAAO,CAAC6E,cAAR,CAAuB;MACrBC,KAAK,EAAE,QADc;MAErBC,MAAM,EAAE;KAFV;;AAKH;;ACtBD,MAAMjE,UAAU,GAAG,CACjB,CAAC,GAAD,EAAM,CAAC,MAAD,EAAS,OAAT,CAAN,EAAyB2D,gBAAzB,CADiB,EAEjB,CAAC,GAAD,EAAM,CAAC,KAAD,EAAQ,QAAR,CAAN,EAAyBC,gBAAzB,CAFiB,CAAnB;AAKA,MAAaM;EACXC,YAAYxK,MAAkBuF;SAyBtBvF;SAEDK;SAEAE;SAIAD;SAEAyC;SAEAF;SAEAzC;IAtCL,MAAM2J,mBAAmB,GAAGvD,sBAAsB,CAACjB,OAAD,CAAlD;IACA,MAAMkF,aAAa,GAAGX,gBAAgB,CAACC,mBAAD,CAAtC;IAEA,KAAK/J,IAAL,GAAY,EAAC,GAAGA;KAAhB;IACA,KAAKK,KAAL,GAAaL,IAAI,CAACK,KAAlB;IACA,KAAKE,MAAL,GAAcP,IAAI,CAACO,MAAnB;;IAEA,KAAK,MAAM,CAACmK,IAAD,EAAOC,IAAP,EAAaC,eAAb,CAAX,IAA4CvE,UAA5C,EAAwD;MACtD,KAAK,MAAMwE,GAAX,IAAkBF,IAAlB,EAAwB;QACtBtL,MAAM,CAACyL,cAAP,CAAsB,IAAtB,EAA4BD,GAA5B,EAAiC;UAC/BnJ,GAAG,EAAE;YACH,MAAMqJ,cAAc,GAAGH,eAAe,CAACb,mBAAD,CAAtC;YACA,MAAMiB,mBAAmB,GAAGP,aAAa,CAACC,IAAD,CAAb,GAAsBK,cAAlD;YAEA,OAAO,KAAK/K,IAAL,CAAU6K,GAAV,IAAiBG,mBAAxB;WAL6B;UAO/BC,UAAU,EAAE;SAPd;;;;IAYJ5L,MAAM,CAACyL,cAAP,CAAsB,IAAtB,EAA4B,MAA5B,EAAoC;MAACG,UAAU,EAAE;KAAjD;;;;;MCpCSC;EAOXV,YAAoB7H;SAAAA;SANZvG,YAIF;;SAaC+O,YAAY;MACjB,KAAK/O,SAAL,CAAeS,OAAf,CAAwBhB,QAAD;QAAA;;QAAA,uBACrB,KAAK8G,MADgB,qBACrB,aAAayI,mBAAb,CAAiC,GAAGvP,QAApC,CADqB;OAAvB;;;IAZkB,WAAA,GAAA8G,MAAA;;;EAEbnG,GAAG,CACR6O,SADQ,EAERC,OAFQ,EAGRtM,OAHQ;;;IAKR,sBAAK2D,MAAL,mCAAa4I,gBAAb,CAA8BF,SAA9B,EAAyCC,OAAzC,EAAmEtM,OAAnE;IACA,KAAK5C,SAAL,CAAewF,IAAf,CAAoB,CAACyJ,SAAD,EAAYC,OAAZ,EAAsCtM,OAAtC,CAApB;;;;;SCbYwM,uBACd7I;;;;;;EAQA,MAAM;IAAC8I;MAAehG,SAAS,CAAC9C,MAAD,CAA/B;EAEA,OAAOA,MAAM,YAAY8I,WAAlB,GAAgC9I,MAAhC,GAAyC6E,gBAAgB,CAAC7E,MAAD,CAAhE;AACD;;SCZe+I,oBACdC,OACAC;EAEA,MAAMC,EAAE,GAAGjM,IAAI,CAAC+J,GAAL,CAASgC,KAAK,CAACpM,CAAf,CAAX;EACA,MAAMuM,EAAE,GAAGlM,IAAI,CAAC+J,GAAL,CAASgC,KAAK,CAACnM,CAAf,CAAX;;EAEA,IAAI,OAAOoM,WAAP,KAAuB,QAA3B,EAAqC;IACnC,OAAOhM,IAAI,CAACC,IAAL,CAAUgM,EAAE,IAAI,CAAN,GAAUC,EAAE,IAAI,CAA1B,IAA+BF,WAAtC;;;EAGF,IAAI,OAAOA,WAAP,IAAsB,OAAOA,WAAjC,EAA8C;IAC5C,OAAOC,EAAE,GAAGD,WAAW,CAACrM,CAAjB,IAAsBuM,EAAE,GAAGF,WAAW,CAACpM,CAA9C;;;EAGF,IAAI,OAAOoM,WAAX,EAAwB;IACtB,OAAOC,EAAE,GAAGD,WAAW,CAACrM,CAAxB;;;EAGF,IAAI,OAAOqM,WAAX,EAAwB;IACtB,OAAOE,EAAE,GAAGF,WAAW,CAACpM,CAAxB;;;EAGF,OAAO,KAAP;AACD;;AC1BD,IAAYuM,SAAZ;;AAAA,WAAYA;EACVA,kBAAA,UAAA;EACAA,sBAAA,cAAA;EACAA,oBAAA,YAAA;EACAA,wBAAA,gBAAA;EACAA,mBAAA,WAAA;EACAA,4BAAA,oBAAA;EACAA,6BAAA,qBAAA;AACD,CARD,EAAYA,SAAS,KAATA,SAAS,KAAA,CAArB;;AAUA,SAAgBC,eAAepP;EAC7BA,KAAK,CAACoP,cAAN;AACD;AAED,SAAgBC,gBAAgBrP;EAC9BA,KAAK,CAACqP,eAAN;AACD;;ICbWC,YAAZ;;AAAA,WAAYA;EACVA,qBAAA,UAAA;EACAA,oBAAA,cAAA;EACAA,qBAAA,eAAA;EACAA,oBAAA,cAAA;EACAA,kBAAA,YAAA;EACAA,mBAAA,WAAA;EACAA,qBAAA,UAAA;EACAA,mBAAA,QAAA;AACD,CATD,EAAYA,YAAY,KAAZA,YAAY,KAAA,CAAxB;;ACDO,MAAMC,oBAAoB,GAAkB;EACjDC,KAAK,EAAE,CAACF,YAAY,CAACG,KAAd,EAAqBH,YAAY,CAACI,KAAlC,CAD0C;EAEjDC,MAAM,EAAE,CAACL,YAAY,CAACM,GAAd,CAFyC;EAGjDC,GAAG,EAAE,CAACP,YAAY,CAACG,KAAd,EAAqBH,YAAY,CAACI,KAAlC,EAAyCJ,YAAY,CAACQ,GAAtD;AAH4C,CAA5C;AAMP,MAAaC,+BAA+B,GAA6B,CACvE/P,KADuE;MAEvE;IAACgQ;;;EAED,QAAQhQ,KAAK,CAACiQ,IAAd;IACE,KAAKX,YAAY,CAACY,KAAlB;MACE,OAAO,EACL,GAAGF,kBADE;QAELrN,CAAC,EAAEqN,kBAAkB,CAACrN,CAAnB,GAAuB;OAF5B;;IAIF,KAAK2M,YAAY,CAACa,IAAlB;MACE,OAAO,EACL,GAAGH,kBADE;QAELrN,CAAC,EAAEqN,kBAAkB,CAACrN,CAAnB,GAAuB;OAF5B;;IAIF,KAAK2M,YAAY,CAACc,IAAlB;MACE,OAAO,EACL,GAAGJ,kBADE;QAELpN,CAAC,EAAEoN,kBAAkB,CAACpN,CAAnB,GAAuB;OAF5B;;IAIF,KAAK0M,YAAY,CAACe,EAAlB;MACE,OAAO,EACL,GAAGL,kBADE;QAELpN,CAAC,EAAEoN,kBAAkB,CAACpN,CAAnB,GAAuB;OAF5B;;;EAMJ,OAAO0N,SAAP;AACD,CA5BM;;MC+BMC;EAMX3C,YAAoB4C;SAAAA;SALbC,oBAAoB;SACnBC;SACAlR;SACAmR;IAEY,UAAA,GAAAH,KAAA;IAClB,MAAM;MACJxQ,KAAK,EAAE;QAAC+F;;QACNyK,KAFJ;IAIA,KAAKA,KAAL,GAAaA,KAAb;IACA,KAAKhR,SAAL,GAAiB,IAAI8O,SAAJ,CAAc1D,gBAAgB,CAAC7E,MAAD,CAA9B,CAAjB;IACA,KAAK4K,eAAL,GAAuB,IAAIrC,SAAJ,CAAczF,SAAS,CAAC9C,MAAD,CAAvB,CAAvB;IACA,KAAK6K,aAAL,GAAqB,KAAKA,aAAL,CAAmBC,IAAnB,CAAwB,IAAxB,CAArB;IACA,KAAKC,YAAL,GAAoB,KAAKA,YAAL,CAAkBD,IAAlB,CAAuB,IAAvB,CAApB;IAEA,KAAKE,MAAL;;;EAGMA,MAAM;IACZ,KAAKC,WAAL;IAEA,KAAKL,eAAL,CAAqB/Q,GAArB,CAAyBuP,SAAS,CAAC8B,MAAnC,EAA2C,KAAKH,YAAhD;IACA,KAAKH,eAAL,CAAqB/Q,GAArB,CAAyBuP,SAAS,CAAC+B,gBAAnC,EAAqD,KAAKJ,YAA1D;IAEAK,UAAU,CAAC,MAAM,KAAK3R,SAAL,CAAeI,GAAf,CAAmBuP,SAAS,CAACiC,OAA7B,EAAsC,KAAKR,aAA3C,CAAP,CAAV;;;EAGMI,WAAW;IACjB,MAAM;MAACK,UAAD;MAAaC;QAAW,KAAKd,KAAnC;IACA,MAAMpH,IAAI,GAAGiI,UAAU,CAACjI,IAAX,CAAgBmI,OAA7B;;IAEA,IAAInI,IAAJ,EAAU;MACRkE,sBAAsB,CAAClE,IAAD,CAAtB;;;IAGFkI,OAAO,CAAC9O,kBAAD,CAAP;;;EAGMoO,aAAa,CAAC5Q,KAAD;IACnB,IAAIwR,eAAe,CAACxR,KAAD,CAAnB,EAA4B;MAC1B,MAAM;QAACM,MAAD;QAASmR,OAAT;QAAkBrP;UAAW,KAAKoO,KAAxC;MACA,MAAM;QACJkB,aAAa,GAAGnC,oBADZ;QAEJoC,gBAAgB,GAAG5B,+BAFf;QAGJ6B,cAAc,GAAG;UACfxP,OAJJ;MAKA,MAAM;QAAC6N;UAAQjQ,KAAf;;MAEA,IAAI0R,aAAa,CAAC7B,GAAd,CAAkB3F,QAAlB,CAA2B+F,IAA3B,CAAJ,EAAsC;QACpC,KAAK4B,SAAL,CAAe7R,KAAf;QACA;;;MAGF,IAAI0R,aAAa,CAAC/B,MAAd,CAAqBzF,QAArB,CAA8B+F,IAA9B,CAAJ,EAAyC;QACvC,KAAKa,YAAL,CAAkB9Q,KAAlB;QACA;;;MAGF,MAAM;QAACyE;UAAiBgN,OAAO,CAACF,OAAhC;MACA,MAAMvB,kBAAkB,GAAGvL,aAAa,GACpC;QAAC9B,CAAC,EAAE8B,aAAa,CAACjB,IAAlB;QAAwBZ,CAAC,EAAE6B,aAAa,CAACf;OADL,GAEpClB,kBAFJ;;MAIA,IAAI,CAAC,KAAKkO,oBAAV,EAAgC;QAC9B,KAAKA,oBAAL,GAA4BV,kBAA5B;;;MAGF,MAAM8B,cAAc,GAAGH,gBAAgB,CAAC3R,KAAD,EAAQ;QAC7CM,MAD6C;QAE7CmR,OAAO,EAAEA,OAAO,CAACF,OAF4B;QAG7CvB;OAHqC,CAAvC;;MAMA,IAAI8B,cAAJ,EAAoB;QAClB,MAAMC,gBAAgB,GAAGC,QAAmB,CAC1CF,cAD0C,EAE1C9B,kBAF0C,CAA5C;QAIA,MAAMiC,WAAW,GAAG;UAClBtP,CAAC,EAAE,CADe;UAElBC,CAAC,EAAE;SAFL;QAIA,MAAM;UAACuK;YAAuBsE,OAAO,CAACF,OAAtC;;QAEA,KAAK,MAAMhF,eAAX,IAA8BY,mBAA9B,EAAmD;UACjD,MAAMR,SAAS,GAAG3M,KAAK,CAACiQ,IAAxB;UACA,MAAM;YAAChE,KAAD;YAAQG,OAAR;YAAiBF,MAAjB;YAAyBC,QAAzB;YAAmCL,SAAnC;YAA8CJ;cAClDF,iBAAiB,CAACe,eAAD,CADnB;UAEA,MAAM2F,iBAAiB,GAAGjF,oBAAoB,CAACV,eAAD,CAA9C;UAEA,MAAM4F,kBAAkB,GAAG;YACzBxP,CAAC,EAAEK,IAAI,CAACkD,GAAL,CACDyG,SAAS,KAAK2C,YAAY,CAACY,KAA3B,GACIgC,iBAAiB,CAACjM,KAAlB,GAA0BiM,iBAAiB,CAACzO,KAAlB,GAA0B,CADxD,GAEIyO,iBAAiB,CAACjM,KAHrB,EAIDjD,IAAI,CAACgD,GAAL,CACE2G,SAAS,KAAK2C,YAAY,CAACY,KAA3B,GACIgC,iBAAiB,CAAC1O,IADtB,GAEI0O,iBAAiB,CAAC1O,IAAlB,GAAyB0O,iBAAiB,CAACzO,KAAlB,GAA0B,CAHzD,EAIEqO,cAAc,CAACnP,CAJjB,CAJC,CADsB;YAYzBC,CAAC,EAAEI,IAAI,CAACkD,GAAL,CACDyG,SAAS,KAAK2C,YAAY,CAACc,IAA3B,GACI8B,iBAAiB,CAAC/L,MAAlB,GAA2B+L,iBAAiB,CAACvO,MAAlB,GAA2B,CAD1D,GAEIuO,iBAAiB,CAAC/L,MAHrB,EAIDnD,IAAI,CAACgD,GAAL,CACE2G,SAAS,KAAK2C,YAAY,CAACc,IAA3B,GACI8B,iBAAiB,CAACxO,GADtB,GAEIwO,iBAAiB,CAACxO,GAAlB,GAAwBwO,iBAAiB,CAACvO,MAAlB,GAA2B,CAHzD,EAIEmO,cAAc,CAAClP,CAJjB,CAJC;WAZL;UAyBA,MAAMwP,UAAU,GACbzF,SAAS,KAAK2C,YAAY,CAACY,KAA3B,IAAoC,CAAC9D,OAAtC,IACCO,SAAS,KAAK2C,YAAY,CAACa,IAA3B,IAAmC,CAACjE,MAFvC;UAGA,MAAMmG,UAAU,GACb1F,SAAS,KAAK2C,YAAY,CAACc,IAA3B,IAAmC,CAACjE,QAArC,IACCQ,SAAS,KAAK2C,YAAY,CAACe,EAA3B,IAAiC,CAACpE,KAFrC;;UAIA,IAAImG,UAAU,IAAID,kBAAkB,CAACxP,CAAnB,KAAyBmP,cAAc,CAACnP,CAA1D,EAA6D;YAC3D,MAAM2P,oBAAoB,GACxB/F,eAAe,CAACvB,UAAhB,GAA6B+G,gBAAgB,CAACpP,CADhD;YAEA,MAAM4P,yBAAyB,GAC5B5F,SAAS,KAAK2C,YAAY,CAACY,KAA3B,IACCoC,oBAAoB,IAAIxG,SAAS,CAACnJ,CADpC,IAECgK,SAAS,KAAK2C,YAAY,CAACa,IAA3B,IACCmC,oBAAoB,IAAI5G,SAAS,CAAC/I,CAJtC;;YAMA,IAAI4P,yBAAyB,IAAI,CAACR,gBAAgB,CAACnP,CAAnD,EAAsD;;;cAGpD2J,eAAe,CAACiG,QAAhB,CAAyB;gBACvBhP,IAAI,EAAE8O,oBADiB;gBAEvBG,QAAQ,EAAEb;eAFZ;cAIA;;;YAGF,IAAIW,yBAAJ,EAA+B;cAC7BN,WAAW,CAACtP,CAAZ,GAAgB4J,eAAe,CAACvB,UAAhB,GAA6BsH,oBAA7C;aADF,MAEO;cACLL,WAAW,CAACtP,CAAZ,GACEgK,SAAS,KAAK2C,YAAY,CAACY,KAA3B,GACI3D,eAAe,CAACvB,UAAhB,GAA6Bc,SAAS,CAACnJ,CAD3C,GAEI4J,eAAe,CAACvB,UAAhB,GAA6BU,SAAS,CAAC/I,CAH7C;;;YAMF,IAAIsP,WAAW,CAACtP,CAAhB,EAAmB;cACjB4J,eAAe,CAACmG,QAAhB,CAAyB;gBACvBlP,IAAI,EAAE,CAACyO,WAAW,CAACtP,CADI;gBAEvB8P,QAAQ,EAAEb;eAFZ;;;YAKF;WAlCF,MAmCO,IAAIS,UAAU,IAAIF,kBAAkB,CAACvP,CAAnB,KAAyBkP,cAAc,CAAClP,CAA1D,EAA6D;YAClE,MAAM0P,oBAAoB,GACxB/F,eAAe,CAACpB,SAAhB,GAA4B4G,gBAAgB,CAACnP,CAD/C;YAEA,MAAM2P,yBAAyB,GAC5B5F,SAAS,KAAK2C,YAAY,CAACc,IAA3B,IACCkC,oBAAoB,IAAIxG,SAAS,CAAClJ,CADpC,IAEC+J,SAAS,KAAK2C,YAAY,CAACe,EAA3B,IACCiC,oBAAoB,IAAI5G,SAAS,CAAC9I,CAJtC;;YAMA,IAAI2P,yBAAyB,IAAI,CAACR,gBAAgB,CAACpP,CAAnD,EAAsD;;;cAGpD4J,eAAe,CAACiG,QAAhB,CAAyB;gBACvB9O,GAAG,EAAE4O,oBADkB;gBAEvBG,QAAQ,EAAEb;eAFZ;cAIA;;;YAGF,IAAIW,yBAAJ,EAA+B;cAC7BN,WAAW,CAACrP,CAAZ,GAAgB2J,eAAe,CAACpB,SAAhB,GAA4BmH,oBAA5C;aADF,MAEO;cACLL,WAAW,CAACrP,CAAZ,GACE+J,SAAS,KAAK2C,YAAY,CAACc,IAA3B,GACI7D,eAAe,CAACpB,SAAhB,GAA4BW,SAAS,CAAClJ,CAD1C,GAEI2J,eAAe,CAACpB,SAAhB,GAA4BO,SAAS,CAAC9I,CAH5C;;;YAMF,IAAIqP,WAAW,CAACrP,CAAhB,EAAmB;cACjB2J,eAAe,CAACmG,QAAhB,CAAyB;gBACvBhP,GAAG,EAAE,CAACuO,WAAW,CAACrP,CADK;gBAEvB6P,QAAQ,EAAEb;eAFZ;;;YAMF;;;;QAIJ,KAAKe,UAAL,CACE3S,KADF,EAEE4S,GAAsB,CACpBZ,QAAmB,CAACF,cAAD,EAAiB,KAAKpB,oBAAtB,CADC,EAEpBuB,WAFoB,CAFxB;;;;;EAWEU,UAAU,CAAC3S,KAAD,EAAe6S,WAAf;IAChB,MAAM;MAACC;QAAU,KAAKtC,KAAtB;IAEAxQ,KAAK,CAACoP,cAAN;IACA0D,MAAM,CAACD,WAAD,CAAN;;;EAGMhB,SAAS,CAAC7R,KAAD;IACf,MAAM;MAAC+S;QAAS,KAAKvC,KAArB;IAEAxQ,KAAK,CAACoP,cAAN;IACA,KAAK4D,MAAL;IACAD,KAAK;;;EAGCjC,YAAY,CAAC9Q,KAAD;IAClB,MAAM;MAACiT;QAAY,KAAKzC,KAAxB;IAEAxQ,KAAK,CAACoP,cAAN;IACA,KAAK4D,MAAL;IACAC,QAAQ;;;EAGFD,MAAM;IACZ,KAAKxT,SAAL,CAAe+O,SAAf;IACA,KAAKoC,eAAL,CAAqBpC,SAArB;;;;AA1OSgC,eA6OJ2C,aAAgD,CACrD;EACEzE,SAAS,EAAE,WADb;EAEEC,OAAO,EAAE,CACP1O,KADO;QAEP;MAAC0R,aAAa,GAAGnC,oBAAjB;MAAuC4D;;QACvC;MAAC7S;;IAED,MAAM;MAAC2P;QAAQjQ,KAAK,CAACoT,WAArB;;IAEA,IAAI1B,aAAa,CAAClC,KAAd,CAAoBtF,QAApB,CAA6B+F,IAA7B,CAAJ,EAAwC;MACtC,MAAMoD,SAAS,GAAG/S,MAAM,CAACgT,aAAP,CAAqB/B,OAAvC;;MAEA,IAAI8B,SAAS,IAAIrT,KAAK,CAAC+F,MAAN,KAAiBsN,SAAlC,EAA6C;QAC3C,OAAO,KAAP;;;MAGFrT,KAAK,CAACoP,cAAN;MAEA+D,YAAY,QAAZ,YAAAA,YAAY,CAAG;QAACnT,KAAK,EAAEA,KAAK,CAACoT;OAAjB,CAAZ;MAEA,OAAO,IAAP;;;IAGF,OAAO,KAAP;;AAvBJ,CADqD;;ACxOzD,SAASG,oBAAT,CACEC,UADF;EAGE,OAAOC,OAAO,CAACD,UAAU,IAAI,cAAcA,UAA7B,CAAd;AACD;;AAED,SAASE,iBAAT,CACEF,UADF;EAGE,OAAOC,OAAO,CAACD,UAAU,IAAI,WAAWA,UAA1B,CAAd;AACD;;AAaD,MAAaG;EAUX/F,YACU4C,OACAoD,QACRC;;;QAAAA;MAAAA,iBAAiBjF,sBAAsB,CAAC4B,KAAK,CAACxQ,KAAN,CAAY+F,MAAb;;;SAF/ByK;SACAoD;SAXHnD,oBAAoB;SACnBlF;SACAuI,YAAqB;SACrBC;SACAC,YAAmC;SACnCxU;SACAyU;SACAtD;IAGE,UAAA,GAAAH,KAAA;IACA,WAAA,GAAAoD,MAAA;IAGR,MAAM;MAAC5T;QAASwQ,KAAhB;IACA,MAAM;MAACzK;QAAU/F,KAAjB;IAEA,KAAKwQ,KAAL,GAAaA,KAAb;IACA,KAAKoD,MAAL,GAAcA,MAAd;IACA,KAAKrI,QAAL,GAAgBX,gBAAgB,CAAC7E,MAAD,CAAhC;IACA,KAAKkO,iBAAL,GAAyB,IAAI3F,SAAJ,CAAc,KAAK/C,QAAnB,CAAzB;IACA,KAAK/L,SAAL,GAAiB,IAAI8O,SAAJ,CAAcuF,cAAd,CAAjB;IACA,KAAKlD,eAAL,GAAuB,IAAIrC,SAAJ,CAAczF,SAAS,CAAC9C,MAAD,CAAvB,CAAvB;IACA,KAAKgO,kBAAL,2BAA0BzQ,mBAAmB,CAACtD,KAAD,CAA7C,mCAAwDwC,kBAAxD;IACA,KAAKwO,WAAL,GAAmB,KAAKA,WAAL,CAAiBH,IAAjB,CAAsB,IAAtB,CAAnB;IACA,KAAK8B,UAAL,GAAkB,KAAKA,UAAL,CAAgB9B,IAAhB,CAAqB,IAArB,CAAlB;IACA,KAAKgB,SAAL,GAAiB,KAAKA,SAAL,CAAehB,IAAf,CAAoB,IAApB,CAAjB;IACA,KAAKC,YAAL,GAAoB,KAAKA,YAAL,CAAkBD,IAAlB,CAAuB,IAAvB,CAApB;IACA,KAAKqD,aAAL,GAAqB,KAAKA,aAAL,CAAmBrD,IAAnB,CAAwB,IAAxB,CAArB;IACA,KAAKsD,mBAAL,GAA2B,KAAKA,mBAAL,CAAyBtD,IAAzB,CAA8B,IAA9B,CAA3B;IAEA,KAAKE,MAAL;;;EAGMA,MAAM;IACZ,MAAM;MACJ6C,MADI;MAEJpD,KAAK,EAAE;QACLpO,OAAO,EAAE;UAACgS,oBAAD;UAAuBC;;;QAEhC,IALJ;IAOA,KAAK7U,SAAL,CAAeI,GAAf,CAAmBgU,MAAM,CAACU,IAAP,CAAYC,IAA/B,EAAqC,KAAK5B,UAA1C,EAAsD;MAAC6B,OAAO,EAAE;KAAhE;IACA,KAAKhV,SAAL,CAAeI,GAAf,CAAmBgU,MAAM,CAAC/D,GAAP,CAAW0E,IAA9B,EAAoC,KAAK1C,SAAzC;;IAEA,IAAI+B,MAAM,CAACjE,MAAX,EAAmB;MACjB,KAAKnQ,SAAL,CAAeI,GAAf,CAAmBgU,MAAM,CAACjE,MAAP,CAAc4E,IAAjC,EAAuC,KAAKzD,YAA5C;;;IAGF,KAAKH,eAAL,CAAqB/Q,GAArB,CAAyBuP,SAAS,CAAC8B,MAAnC,EAA2C,KAAKH,YAAhD;IACA,KAAKH,eAAL,CAAqB/Q,GAArB,CAAyBuP,SAAS,CAACsF,SAAnC,EAA8CrF,cAA9C;IACA,KAAKuB,eAAL,CAAqB/Q,GAArB,CAAyBuP,SAAS,CAAC+B,gBAAnC,EAAqD,KAAKJ,YAA1D;IACA,KAAKH,eAAL,CAAqB/Q,GAArB,CAAyBuP,SAAS,CAACuF,WAAnC,EAAgDtF,cAAhD;IACA,KAAK6E,iBAAL,CAAuBrU,GAAvB,CAA2BuP,SAAS,CAACiC,OAArC,EAA8C,KAAK8C,aAAnD;;IAEA,IAAIE,oBAAJ,EAA0B;MACxB,IACEC,0BADF,YACEA,0BAA0B,CAAG;QAC3BrU,KAAK,EAAE,KAAKwQ,KAAL,CAAWxQ,KADS;QAE3BqR,UAAU,EAAE,KAAKb,KAAL,CAAWa,UAFI;QAG3BjP,OAAO,EAAE,KAAKoO,KAAL,CAAWpO;OAHI,CAD5B,EAME;QACA,OAAO,KAAK4O,WAAL,EAAP;;;MAGF,IAAI0C,iBAAiB,CAACU,oBAAD,CAArB,EAA6C;QAC3C,KAAKJ,SAAL,GAAiB7C,UAAU,CACzB,KAAKH,WADoB,EAEzBoD,oBAAoB,CAACO,KAFI,CAA3B;QAIA,KAAKC,aAAL,CAAmBR,oBAAnB;QACA;;;MAGF,IAAIb,oBAAoB,CAACa,oBAAD,CAAxB,EAAgD;QAC9C,KAAKQ,aAAL,CAAmBR,oBAAnB;QACA;;;;IAIJ,KAAKpD,WAAL;;;EAGMgC,MAAM;IACZ,KAAKxT,SAAL,CAAe+O,SAAf;IACA,KAAKoC,eAAL,CAAqBpC,SAArB;;;IAIA4C,UAAU,CAAC,KAAK8C,iBAAL,CAAuB1F,SAAxB,EAAmC,EAAnC,CAAV;;IAEA,IAAI,KAAKyF,SAAL,KAAmB,IAAvB,EAA6B;MAC3Ba,YAAY,CAAC,KAAKb,SAAN,CAAZ;MACA,KAAKA,SAAL,GAAiB,IAAjB;;;;EAIIY,aAAa,CACnBpB,UADmB,EAEnBsB,MAFmB;IAInB,MAAM;MAACxU,MAAD;MAASyU;QAAa,KAAKvE,KAAjC;IACAuE,SAAS,CAACzU,MAAD,EAASkT,UAAT,EAAqB,KAAKO,kBAA1B,EAA8Ce,MAA9C,CAAT;;;EAGM9D,WAAW;IACjB,MAAM;MAAC+C;QAAsB,IAA7B;IACA,MAAM;MAACzC;QAAW,KAAKd,KAAvB;;IAEA,IAAIuD,kBAAJ,EAAwB;MACtB,KAAKD,SAAL,GAAiB,IAAjB,CADsB;;MAItB,KAAKG,iBAAL,CAAuBrU,GAAvB,CAA2BuP,SAAS,CAAC6F,KAArC,EAA4C3F,eAA5C,EAA6D;QAC3D4F,OAAO,EAAE;OADX,EAJsB;;MAStB,KAAKd,mBAAL,GATsB;;MAYtB,KAAKF,iBAAL,CAAuBrU,GAAvB,CACEuP,SAAS,CAAC+F,eADZ,EAEE,KAAKf,mBAFP;MAKA7C,OAAO,CAACyC,kBAAD,CAAP;;;;EAIIpB,UAAU,CAAC3S,KAAD;;;IAChB,MAAM;MAAC8T,SAAD;MAAYC,kBAAZ;MAAgCvD;QAAS,IAA/C;IACA,MAAM;MACJsC,MADI;MAEJ1Q,OAAO,EAAE;QAACgS;;QACR5D,KAHJ;;IAKA,IAAI,CAACuD,kBAAL,EAAyB;MACvB;;;IAGF,MAAMlB,WAAW,4BAAGvP,mBAAmB,CAACtD,KAAD,CAAtB,oCAAiCwC,kBAAlD;IACA,MAAMuM,KAAK,GAAGiD,QAAmB,CAAC+B,kBAAD,EAAqBlB,WAArB,CAAjC;;IAGA,IAAI,CAACiB,SAAD,IAAcM,oBAAlB,EAAwC;MACtC,IAAIb,oBAAoB,CAACa,oBAAD,CAAxB,EAAgD;QAC9C,IACEA,oBAAoB,CAACe,SAArB,IAAkC,IAAlC,IACArG,mBAAmB,CAACC,KAAD,EAAQqF,oBAAoB,CAACe,SAA7B,CAFrB,EAGE;UACA,OAAO,KAAKrE,YAAL,EAAP;;;QAGF,IAAIhC,mBAAmB,CAACC,KAAD,EAAQqF,oBAAoB,CAACgB,QAA7B,CAAvB,EAA+D;UAC7D,OAAO,KAAKpE,WAAL,EAAP;;;;MAIJ,IAAI0C,iBAAiB,CAACU,oBAAD,CAArB,EAA6C;QAC3C,IAAItF,mBAAmB,CAACC,KAAD,EAAQqF,oBAAoB,CAACe,SAA7B,CAAvB,EAAgE;UAC9D,OAAO,KAAKrE,YAAL,EAAP;;;;MAIJ,KAAK8D,aAAL,CAAmBR,oBAAnB,EAAyCrF,KAAzC;MACA;;;IAGF,IAAI/O,KAAK,CAACqV,UAAV,EAAsB;MACpBrV,KAAK,CAACoP,cAAN;;;IAGF0D,MAAM,CAACD,WAAD,CAAN;;;EAGMhB,SAAS;IACf,MAAM;MAACyD,OAAD;MAAUvC;QAAS,KAAKvC,KAA9B;IAEA,KAAKwC,MAAL;;IACA,IAAI,CAAC,KAAKc,SAAV,EAAqB;MACnBwB,OAAO,CAAC,KAAK9E,KAAL,CAAWlQ,MAAZ,CAAP;;;IAEFyS,KAAK;;;EAGCjC,YAAY;IAClB,MAAM;MAACwE,OAAD;MAAUrC;QAAY,KAAKzC,KAAjC;IAEA,KAAKwC,MAAL;;IACA,IAAI,CAAC,KAAKc,SAAV,EAAqB;MACnBwB,OAAO,CAAC,KAAK9E,KAAL,CAAWlQ,MAAZ,CAAP;;;IAEF2S,QAAQ;;;EAGFiB,aAAa,CAAClU,KAAD;IACnB,IAAIA,KAAK,CAACiQ,IAAN,KAAeX,YAAY,CAACM,GAAhC,EAAqC;MACnC,KAAKkB,YAAL;;;;EAIIqD,mBAAmB;;;IACzB,8BAAK5I,QAAL,CAAcgK,YAAd,6CAA8BC,eAA9B;;;;;ACtQJ,MAAM5B,MAAM,GAAyB;EACnCjE,MAAM,EAAE;IAAC4E,IAAI,EAAE;GADoB;EAEnCD,IAAI,EAAE;IAACC,IAAI,EAAE;GAFsB;EAGnC1E,GAAG,EAAE;IAAC0E,IAAI,EAAE;;AAHuB,CAArC;AAUA,MAAakB,sBAAsB9B;EACjC/F,YAAY4C;IACV,MAAM;MAACxQ;QAASwQ,KAAhB;;;IAGA,MAAMqD,cAAc,GAAGjJ,gBAAgB,CAAC5K,KAAK,CAAC+F,MAAP,CAAvC;IAEA,MAAMyK,KAAN,EAAaoD,MAAb,EAAqBC,cAArB;;;;AAPS4B,cAUJvC,aAAa,CAClB;EACEzE,SAAS,EAAE,eADb;EAEEC,OAAO,EAAE;QACP;MAAC0E,WAAW,EAAEpT;;QACd;MAACmT;;;IAED,IAAI,CAACnT,KAAK,CAAC0V,SAAP,IAAoB1V,KAAK,CAAC2V,MAAN,KAAiB,CAAzC,EAA4C;MAC1C,OAAO,KAAP;;;IAGFxC,YAAY,QAAZ,YAAAA,YAAY,CAAG;MAACnT;KAAJ,CAAZ;IAEA,OAAO,IAAP;;AAZJ,CADkB;;ACpBtB,MAAM4T,QAAM,GAAyB;EACnCU,IAAI,EAAE;IAACC,IAAI,EAAE;GADsB;EAEnC1E,GAAG,EAAE;IAAC0E,IAAI,EAAE;;AAFuB,CAArC;AAKA,IAAKqB,WAAL;;AAAA,WAAKA;EACHA,0CAAA,eAAA;AACD,CAFD,EAAKA,WAAW,KAAXA,WAAW,KAAA,CAAhB;;AAQA,MAAaC,oBAAoBlC;EAC/B/F,YAAY4C;IACV,MAAMA,KAAN,EAAaoD,QAAb,EAAqBhJ,gBAAgB,CAAC4F,KAAK,CAACxQ,KAAN,CAAY+F,MAAb,CAArC;;;;AAFS8P,YAKJ3C,aAAa,CAClB;EACEzE,SAAS,EAAE,aADb;EAEEC,OAAO,EAAE;QACP;MAAC0E,WAAW,EAAEpT;;QACd;MAACmT;;;IAED,IAAInT,KAAK,CAAC2V,MAAN,KAAiBC,WAAW,CAACE,UAAjC,EAA6C;MAC3C,OAAO,KAAP;;;IAGF3C,YAAY,QAAZ,YAAAA,YAAY,CAAG;MAACnT;KAAJ,CAAZ;IAEA,OAAO,IAAP;;AAZJ,CADkB;;AClBtB,MAAM4T,QAAM,GAAyB;EACnCjE,MAAM,EAAE;IAAC4E,IAAI,EAAE;GADoB;EAEnCD,IAAI,EAAE;IAACC,IAAI,EAAE;GAFsB;EAGnC1E,GAAG,EAAE;IAAC0E,IAAI,EAAE;;AAHuB,CAArC;AAUA,MAAawB,oBAAoBpC;EAC/B/F,YAAY4C;IACV,MAAMA,KAAN,EAAaoD,QAAb;;;EAuBU,OAALoC,KAAK;;;;IAIVnL,MAAM,CAAC8D,gBAAP,CAAwBiF,QAAM,CAACU,IAAP,CAAYC,IAApC,EAA0CtS,IAA1C,EAAgD;MAC9CgT,OAAO,EAAE,KADqC;MAE9CT,OAAO,EAAE;KAFX;IAKA,OAAO,SAASyB,QAAT;MACLpL,MAAM,CAAC2D,mBAAP,CAA2BoF,QAAM,CAACU,IAAP,CAAYC,IAAvC,EAA6CtS,IAA7C;KADF;;;IAMA,SAASA,IAAT;;;;AAxCS8T,YAKJ7C,aAAa,CAClB;EACEzE,SAAS,EAAE,cADb;EAEEC,OAAO,EAAE;QACP;MAAC0E,WAAW,EAAEpT;;QACd;MAACmT;;IAED,MAAM;MAAC+C;QAAWlW,KAAlB;;IAEA,IAAIkW,OAAO,CAAC7R,MAAR,GAAiB,CAArB,EAAwB;MACtB,OAAO,KAAP;;;IAGF8O,YAAY,QAAZ,YAAAA,YAAY,CAAG;MAACnT;KAAJ,CAAZ;IAEA,OAAO,IAAP;;AAdJ,CADkB;;IChBVmW,mBAAZ;;AAAA,WAAYA;EACVA,uDAAA,YAAA;EACAA,6DAAA,kBAAA;AACD,CAHD,EAAYA,mBAAmB,KAAnBA,mBAAmB,KAAA,CAA/B;;AAmCA,IAAYC,cAAZ;;AAAA,WAAYA;EACVA,+CAAA,cAAA;EACAA,uDAAA,sBAAA;AACD,CAHD,EAAYA,cAAc,KAAdA,cAAc,KAAA,CAA1B;;AAUA,SAAgBC;MAAgB;IAC9B5J,YAD8B;IAE9B4G,SAAS,GAAG8C,mBAAmB,CAACG,OAFF;IAG9BC,SAH8B;IAI9BC,YAJ8B;IAK9BC,OAL8B;IAM9BC,QAAQ,GAAG,CANmB;IAO9BC,KAAK,GAAGP,cAAc,CAACQ,SAPO;IAQ9BhQ,kBAR8B;IAS9BuG,mBAT8B;IAU9B0J,uBAV8B;IAW9B9H,KAX8B;IAY9BlC;;EAEA,MAAMiK,YAAY,GAAGC,eAAe,CAAC;IAAChI,KAAD;IAAQiI,QAAQ,EAAE,CAACP;GAApB,CAApC;EACA,MAAM,CAACQ,qBAAD,EAAwBC,uBAAxB,IAAmDC,WAAW,EAApE;EACA,MAAMC,WAAW,GAAGC,MAAM,CAAc;IAAC1U,CAAC,EAAE,CAAJ;IAAOC,CAAC,EAAE;GAAxB,CAA1B;EACA,MAAM0U,eAAe,GAAGD,MAAM,CAAkB;IAAC1U,CAAC,EAAE,CAAJ;IAAOC,CAAC,EAAE;GAA5B,CAA9B;EACA,MAAMQ,IAAI,GAAG5B,OAAO,CAAC;IACnB,QAAQ6R,SAAR;MACE,KAAK8C,mBAAmB,CAACG,OAAzB;QACE,OAAO1P,kBAAkB,GACrB;UACElD,GAAG,EAAEkD,kBAAkB,CAAChE,CAD1B;UAEEuD,MAAM,EAAES,kBAAkB,CAAChE,CAF7B;UAGEY,IAAI,EAAEoD,kBAAkB,CAACjE,CAH3B;UAIEsD,KAAK,EAAEW,kBAAkB,CAACjE;SALP,GAOrB,IAPJ;;MAQF,KAAKwT,mBAAmB,CAACoB,aAAzB;QACE,OAAOf,YAAP;;GAZc,EAcjB,CAACnD,SAAD,EAAYmD,YAAZ,EAA0B5P,kBAA1B,CAdiB,CAApB;EAeA,MAAM4Q,kBAAkB,GAAGH,MAAM,CAAiB,IAAjB,CAAjC;EACA,MAAMI,UAAU,GAAG9X,WAAW,CAAC;IAC7B,MAAM4M,eAAe,GAAGiL,kBAAkB,CAACjG,OAA3C;;IAEA,IAAI,CAAChF,eAAL,EAAsB;MACpB;;;IAGF,MAAMvB,UAAU,GAAGoM,WAAW,CAAC7F,OAAZ,CAAoB5O,CAApB,GAAwB2U,eAAe,CAAC/F,OAAhB,CAAwB5O,CAAnE;IACA,MAAMwI,SAAS,GAAGiM,WAAW,CAAC7F,OAAZ,CAAoB3O,CAApB,GAAwB0U,eAAe,CAAC/F,OAAhB,CAAwB3O,CAAlE;IAEA2J,eAAe,CAACmG,QAAhB,CAAyB1H,UAAzB,EAAqCG,SAArC;GAV4B,EAW3B,EAX2B,CAA9B;EAYA,MAAMuM,yBAAyB,GAAGlW,OAAO,CACvC,MACEmV,KAAK,KAAKP,cAAc,CAACQ,SAAzB,GACI,CAAC,GAAGzJ,mBAAJ,EAAyBwK,OAAzB,EADJ,GAEIxK,mBAJiC,EAKvC,CAACwJ,KAAD,EAAQxJ,mBAAR,CALuC,CAAzC;EAQA/N,SAAS,CACP;IACE,IAAI,CAACqX,OAAD,IAAY,CAACtJ,mBAAmB,CAAC9I,MAAjC,IAA2C,CAACjB,IAAhD,EAAsD;MACpD8T,uBAAuB;MACvB;;;IAGF,KAAK,MAAM3K,eAAX,IAA8BmL,yBAA9B,EAAyD;MACvD,IAAI,CAAAnB,SAAS,QAAT,YAAAA,SAAS,CAAGhK,eAAH,CAAT,MAAiC,KAArC,EAA4C;QAC1C;;;MAGF,MAAM9G,KAAK,GAAG0H,mBAAmB,CAAC9E,OAApB,CAA4BkE,eAA5B,CAAd;MACA,MAAMC,mBAAmB,GAAGqK,uBAAuB,CAACpR,KAAD,CAAnD;;MAEA,IAAI,CAAC+G,mBAAL,EAA0B;QACxB;;;MAGF,MAAM;QAACG,SAAD;QAAYC;UAASN,0BAA0B,CACnDC,eADmD,EAEnDC,mBAFmD,EAGnDpJ,IAHmD,EAInDqJ,YAJmD,EAKnDI,SALmD,CAArD;;MAQA,KAAK,MAAMiB,IAAX,IAAmB,CAAC,GAAD,EAAM,GAAN,CAAnB,EAAwC;QACtC,IAAI,CAACgJ,YAAY,CAAChJ,IAAD,CAAZ,CAAmBnB,SAAS,CAACmB,IAAD,CAA5B,CAAL,EAAuD;UACrDlB,KAAK,CAACkB,IAAD,CAAL,GAAc,CAAd;UACAnB,SAAS,CAACmB,IAAD,CAAT,GAAkB,CAAlB;;;;MAIJ,IAAIlB,KAAK,CAACjK,CAAN,GAAU,CAAV,IAAeiK,KAAK,CAAChK,CAAN,GAAU,CAA7B,EAAgC;QAC9BsU,uBAAuB;QAEvBM,kBAAkB,CAACjG,OAAnB,GAA6BhF,eAA7B;QACA0K,qBAAqB,CAACQ,UAAD,EAAaf,QAAb,CAArB;QAEAU,WAAW,CAAC7F,OAAZ,GAAsB3E,KAAtB;QACA0K,eAAe,CAAC/F,OAAhB,GAA0B5E,SAA1B;QAEA;;;;IAIJyK,WAAW,CAAC7F,OAAZ,GAAsB;MAAC5O,CAAC,EAAE,CAAJ;MAAOC,CAAC,EAAE;KAAhC;IACA0U,eAAe,CAAC/F,OAAhB,GAA0B;MAAC5O,CAAC,EAAE,CAAJ;MAAOC,CAAC,EAAE;KAApC;IACAsU,uBAAuB;GAjDlB;EAoDP,CACEzK,YADF,EAEEgL,UAFF,EAGElB,SAHF,EAIEW,uBAJF,EAKET,OALF,EAMEC,QANF;EAQEkB,IAAI,CAACC,SAAL,CAAezU,IAAf,CARF;EAUEwU,IAAI,CAACC,SAAL,CAAef,YAAf,CAVF,EAWEG,qBAXF,EAYE9J,mBAZF,EAaEuK,yBAbF,EAcEb,uBAdF;EAgBEe,IAAI,CAACC,SAAL,CAAehL,SAAf,CAhBF,CApDO,CAAT;AAuED;AAOD,MAAMiL,mBAAmB,GAAiB;EACxCnV,CAAC,EAAE;IAAC,CAAC0I,SAAS,CAACyB,QAAX,GAAsB,KAAvB;IAA8B,CAACzB,SAAS,CAAC2B,OAAX,GAAqB;GADd;EAExCpK,CAAC,EAAE;IAAC,CAACyI,SAAS,CAACyB,QAAX,GAAsB,KAAvB;IAA8B,CAACzB,SAAS,CAAC2B,OAAX,GAAqB;;AAFd,CAA1C;;AAKA,SAAS+J,eAAT;MAAyB;IACvBhI,KADuB;IAEvBiI;;EAKA,MAAMe,aAAa,GAAGC,WAAW,CAACjJ,KAAD,CAAjC;EAEA,OAAOkJ,WAAW,CACfC,cAAD;IACE,IAAIlB,QAAQ,IAAI,CAACe,aAAb,IAA8B,CAACG,cAAnC,EAAmD;;MAEjD,OAAOJ,mBAAP;;;IAGF,MAAMnL,SAAS,GAAG;MAChBhK,CAAC,EAAEK,IAAI,CAACmV,IAAL,CAAUpJ,KAAK,CAACpM,CAAN,GAAUoV,aAAa,CAACpV,CAAlC,CADa;MAEhBC,CAAC,EAAEI,IAAI,CAACmV,IAAL,CAAUpJ,KAAK,CAACnM,CAAN,GAAUmV,aAAa,CAACnV,CAAlC;KAFL;;IAMA,OAAO;MACLD,CAAC,EAAE;QACD,CAAC0I,SAAS,CAACyB,QAAX,GACEoL,cAAc,CAACvV,CAAf,CAAiB0I,SAAS,CAACyB,QAA3B,KAAwCH,SAAS,CAAChK,CAAV,KAAgB,CAAC,CAF1D;QAGD,CAAC0I,SAAS,CAAC2B,OAAX,GACEkL,cAAc,CAACvV,CAAf,CAAiB0I,SAAS,CAAC2B,OAA3B,KAAuCL,SAAS,CAAChK,CAAV,KAAgB;OALtD;MAOLC,CAAC,EAAE;QACD,CAACyI,SAAS,CAACyB,QAAX,GACEoL,cAAc,CAACtV,CAAf,CAAiByI,SAAS,CAACyB,QAA3B,KAAwCH,SAAS,CAAC/J,CAAV,KAAgB,CAAC,CAF1D;QAGD,CAACyI,SAAS,CAAC2B,OAAX,GACEkL,cAAc,CAACtV,CAAf,CAAiByI,SAAS,CAAC2B,OAA3B,KAAuCL,SAAS,CAAC/J,CAAV,KAAgB;;KAX7D;GAbc,EA4BhB,CAACoU,QAAD,EAAWjI,KAAX,EAAkBgJ,aAAlB,CA5BgB,CAAlB;AA8BD;;SCjOeK,cACdC,gBACA9X;EAEA,MAAM+X,aAAa,GAAG/X,EAAE,IAAI,IAAN,GAAa8X,cAAc,CAACvT,GAAf,CAAmBvE,EAAnB,CAAb,GAAsC+P,SAA5D;EACA,MAAMlH,IAAI,GAAGkP,aAAa,GAAGA,aAAa,CAAClP,IAAd,CAAmBmI,OAAtB,GAAgC,IAA1D;EAEA,OAAO0G,WAAW,CACfM,UAAD;;;IACE,IAAIhY,EAAE,IAAI,IAAV,EAAgB;MACd,OAAO,IAAP;;;;;;IAMF,eAAO6I,IAAP,WAAOA,IAAP,GAAemP,UAAf,mBAA6B,IAA7B;GATc,EAWhB,CAACnP,IAAD,EAAO7I,EAAP,CAXgB,CAAlB;AAaD;;SCjBeiY,qBACdlW,SACAmW;EAKA,OAAOjX,OAAO,CACZ,MACEc,OAAO,CAACgD,MAAR,CAAmC,CAACC,WAAD,EAAcpD,MAAd;IACjC,MAAM;MAACA,MAAM,EAAEuW;QAAUvW,MAAzB;IAEA,MAAMwW,gBAAgB,GAAGD,MAAM,CAACxF,UAAP,CAAkB0F,GAAlB,CAAuBvF,SAAD,KAAgB;MAC7D5E,SAAS,EAAE4E,SAAS,CAAC5E,SADwC;MAE7DC,OAAO,EAAE+J,mBAAmB,CAACpF,SAAS,CAAC3E,OAAX,EAAoBvM,MAApB;KAFiB,CAAtB,CAAzB;IAKA,OAAO,CAAC,GAAGoD,WAAJ,EAAiB,GAAGoT,gBAApB,CAAP;GARF,EASG,EATH,CAFU,EAYZ,CAACrW,OAAD,EAAUmW,mBAAV,CAZY,CAAd;AAcD;;IChBWI,iBAAZ;;AAAA,WAAYA;EACVA,kDAAA,WAAA;EACAA,0DAAA,mBAAA;EACAA,yDAAA,kBAAA;AACD,CAJD,EAAYA,iBAAiB,KAAjBA,iBAAiB,KAAA,CAA7B;;AAMA,IAAYC,kBAAZ;;AAAA,WAAYA;EACVA,+BAAA,cAAA;AACD,CAFD,EAAYA,kBAAkB,KAAlBA,kBAAkB,KAAA,CAA9B;;AAYA,MAAMC,YAAY,gBAAY,IAAIC,GAAJ,EAA9B;AAEA,SAAgBC,sBACdC;MACA;IAACC,QAAD;IAAWC,YAAX;IAAyBC;;EAEzB,MAAM,CAACC,KAAD,EAAQC,QAAR,IAAoB9Z,QAAQ,CAA4B,IAA5B,CAAlC;EACA,MAAM;IAAC+Z,SAAD;IAAYjM,OAAZ;IAAqBkM;MAAYJ,MAAvC;EACA,MAAMK,aAAa,GAAGrC,MAAM,CAAC6B,UAAD,CAA5B;EACA,MAAMlC,QAAQ,GAAG2C,UAAU,EAA3B;EACA,MAAMC,WAAW,GAAGC,cAAc,CAAC7C,QAAD,CAAlC;EACA,MAAM8C,0BAA0B,GAAGna,WAAW,CAC5C,UAACoa,GAAD;QAACA;MAAAA,MAA0B;;;IACzB,IAAIH,WAAW,CAACrI,OAAhB,EAAyB;MACvB;;;IAGFgI,QAAQ,CAAE1X,KAAD;MACP,IAAIA,KAAK,KAAK,IAAd,EAAoB;QAClB,OAAOkY,GAAP;;;MAGF,OAAOlY,KAAK,CAACmY,MAAN,CAAaD,GAAG,CAACxX,MAAJ,CAAYhC,EAAD,IAAQ,CAACsB,KAAK,CAACqI,QAAN,CAAe3J,EAAf,CAApB,CAAb,CAAP;KALM,CAAR;GAN0C,EAc5C,CAACqZ,WAAD,CAd4C,CAA9C;EAgBA,MAAM5F,SAAS,GAAGqD,MAAM,CAAwB,IAAxB,CAAxB;EACA,MAAM3S,cAAc,GAAGuT,WAAW,CAC/BgC,aAAD;IACE,IAAIjD,QAAQ,IAAI,CAACmC,QAAjB,EAA2B;MACzB,OAAOJ,YAAP;;;IAGF,IACE,CAACkB,aAAD,IACAA,aAAa,KAAKlB,YADlB,IAEAW,aAAa,CAACnI,OAAd,KAA0B2H,UAF1B,IAGAI,KAAK,IAAI,IAJX,EAKE;MACA,MAAMV,GAAG,GAAY,IAAII,GAAJ,EAArB;;MAEA,KAAK,IAAIlY,SAAT,IAAsBoY,UAAtB,EAAkC;QAChC,IAAI,CAACpY,SAAL,EAAgB;UACd;;;QAGF,IACEwY,KAAK,IACLA,KAAK,CAACjV,MAAN,GAAe,CADf,IAEA,CAACiV,KAAK,CAACpP,QAAN,CAAepJ,SAAS,CAACP,EAAzB,CAFD,IAGAO,SAAS,CAACsC,IAAV,CAAemO,OAJjB,EAKE;;UAEAqH,GAAG,CAACsB,GAAJ,CAAQpZ,SAAS,CAACP,EAAlB,EAAsBO,SAAS,CAACsC,IAAV,CAAemO,OAArC;UACA;;;QAGF,MAAMnI,IAAI,GAAGtI,SAAS,CAACsI,IAAV,CAAemI,OAA5B;QACA,MAAMnO,IAAI,GAAGgG,IAAI,GAAG,IAAIuE,IAAJ,CAASJ,OAAO,CAACnE,IAAD,CAAhB,EAAwBA,IAAxB,CAAH,GAAmC,IAApD;QAEAtI,SAAS,CAACsC,IAAV,CAAemO,OAAf,GAAyBnO,IAAzB;;QAEA,IAAIA,IAAJ,EAAU;UACRwV,GAAG,CAACsB,GAAJ,CAAQpZ,SAAS,CAACP,EAAlB,EAAsB6C,IAAtB;;;;MAIJ,OAAOwV,GAAP;;;IAGF,OAAOqB,aAAP;GA3C8B,EA6ChC,CAACf,UAAD,EAAaI,KAAb,EAAoBH,QAApB,EAA8BnC,QAA9B,EAAwCzJ,OAAxC,CA7CgC,CAAlC;EAgDAnO,SAAS,CAAC;IACRsa,aAAa,CAACnI,OAAd,GAAwB2H,UAAxB;GADO,EAEN,CAACA,UAAD,CAFM,CAAT;EAIA9Z,SAAS,CACP;IACE,IAAI4X,QAAJ,EAAc;MACZ;;;IAGF8C,0BAA0B;GANrB;EASP,CAACX,QAAD,EAAWnC,QAAX,CATO,CAAT;EAYA5X,SAAS,CACP;IACE,IAAIka,KAAK,IAAIA,KAAK,CAACjV,MAAN,GAAe,CAA5B,EAA+B;MAC7BkV,QAAQ,CAAC,IAAD,CAAR;;GAHG;EAOP,CAAC3B,IAAI,CAACC,SAAL,CAAeyB,KAAf,CAAD,CAPO,CAAT;EAUAla,SAAS,CACP;IACE,IACE4X,QAAQ,IACR,OAAOwC,SAAP,KAAqB,QADrB,IAEAxF,SAAS,CAACzC,OAAV,KAAsB,IAHxB,EAIE;MACA;;;IAGFyC,SAAS,CAACzC,OAAV,GAAoBJ,UAAU,CAAC;MAC7B2I,0BAA0B;MAC1B9F,SAAS,CAACzC,OAAV,GAAoB,IAApB;KAF4B,EAG3BiI,SAH2B,CAA9B;GAVK;EAgBP,CAACA,SAAD,EAAYxC,QAAZ,EAAsB8C,0BAAtB,EAAkD,GAAGV,YAArD,CAhBO,CAAT;EAmBA,OAAO;IACL1U,cADK;IAELoV,0BAFK;IAGLK,kBAAkB,EAAEb,KAAK,IAAI;GAH/B;;EAMA,SAASK,UAAT;IACE,QAAQF,QAAR;MACE,KAAKZ,iBAAiB,CAACuB,MAAvB;QACE,OAAO,KAAP;;MACF,KAAKvB,iBAAiB,CAACwB,cAAvB;QACE,OAAOlB,QAAP;;MACF;QACE,OAAO,CAACA,QAAR;;;AAGP;;SCpKemB,gBAIdzY,OACA0Y;EAEA,OAAOtC,WAAW,CACfgC,aAAD;IACE,IAAI,CAACpY,KAAL,EAAY;MACV,OAAO,IAAP;;;IAGF,IAAIoY,aAAJ,EAAmB;MACjB,OAAOA,aAAP;;;IAGF,OAAO,OAAOM,SAAP,KAAqB,UAArB,GAAkCA,SAAS,CAAC1Y,KAAD,CAA3C,GAAqDA,KAA5D;GAVc,EAYhB,CAAC0Y,SAAD,EAAY1Y,KAAZ,CAZgB,CAAlB;AAcD;;SCtBe2Y,eACdpR,MACAmE;EAEA,OAAO+M,eAAe,CAAClR,IAAD,EAAOmE,OAAP,CAAtB;AACD;;ACAD;;;;;AAIA,SAAgBkN;MAAoB;IAACC,QAAD;IAAW1D;;EAC7C,MAAM2D,eAAe,GAAGC,QAAQ,CAACF,QAAD,CAAhC;EACA,MAAMG,gBAAgB,GAAGrZ,OAAO,CAAC;IAC/B,IACEwV,QAAQ,IACR,OAAOnM,MAAP,KAAkB,WADlB,IAEA,OAAOA,MAAM,CAACiQ,gBAAd,KAAmC,WAHrC,EAIE;MACA,OAAOxK,SAAP;;;IAGF,MAAM;MAACwK;QAAoBjQ,MAA3B;IAEA,OAAO,IAAIiQ,gBAAJ,CAAqBH,eAArB,CAAP;GAX8B,EAY7B,CAACA,eAAD,EAAkB3D,QAAlB,CAZ6B,CAAhC;EAcA5X,SAAS,CAAC;IACR,OAAO,MAAMyb,gBAAN,oBAAMA,gBAAgB,CAAEE,UAAlB,EAAb;GADO,EAEN,CAACF,gBAAD,CAFM,CAAT;EAIA,OAAOA,gBAAP;AACD;;ACzBD;;;;;AAIA,SAAgBG;MAAkB;IAACN,QAAD;IAAW1D;;EAC3C,MAAMiE,YAAY,GAAGL,QAAQ,CAACF,QAAD,CAA7B;EACA,MAAMQ,cAAc,GAAG1Z,OAAO,CAC5B;IACE,IACEwV,QAAQ,IACR,OAAOnM,MAAP,KAAkB,WADlB,IAEA,OAAOA,MAAM,CAACsQ,cAAd,KAAiC,WAHnC,EAIE;MACA,OAAO7K,SAAP;;;IAGF,MAAM;MAAC6K;QAAkBtQ,MAAzB;IAEA,OAAO,IAAIsQ,cAAJ,CAAmBF,YAAnB,CAAP;GAZ0B;EAe5B,CAACjE,QAAD,CAf4B,CAA9B;EAkBA5X,SAAS,CAAC;IACR,OAAO,MAAM8b,cAAN,oBAAMA,cAAc,CAAEH,UAAhB,EAAb;GADO,EAEN,CAACG,cAAD,CAFM,CAAT;EAIA,OAAOA,cAAP;AACD;;AC5BD,SAASE,cAAT,CAAwBzS,OAAxB;EACE,OAAO,IAAIgF,IAAJ,CAASjF,aAAa,CAACC,OAAD,CAAtB,EAAiCA,OAAjC,CAAP;AACD;;AAED,SAAgB0S,QACd1S,SACA4E,SACA+N;MADA/N;IAAAA,UAAgD6N;;;EAGhD,MAAM,CAAChY,IAAD,EAAOmY,OAAP,IAAkB9b,QAAQ,CAAoB,IAApB,CAAhC;;EAEA,SAAS+b,WAAT;IACED,OAAO,CAAEE,WAAD;MACN,IAAI,CAAC9S,OAAL,EAAc;QACZ,OAAO,IAAP;;;MAGF,IAAIA,OAAO,CAAC+S,WAAR,KAAwB,KAA5B,EAAmC;QAAA;;;;QAGjC,eAAOD,WAAP,WAAOA,WAAP,GAAsBH,YAAtB,mBAAsC,IAAtC;;;MAGF,MAAMK,OAAO,GAAGpO,OAAO,CAAC5E,OAAD,CAAvB;;MAEA,IAAIiP,IAAI,CAACC,SAAL,CAAe4D,WAAf,MAAgC7D,IAAI,CAACC,SAAL,CAAe8D,OAAf,CAApC,EAA6D;QAC3D,OAAOF,WAAP;;;MAGF,OAAOE,OAAP;KAjBK,CAAP;;;EAqBF,MAAMd,gBAAgB,GAAGJ,mBAAmB,CAAC;IAC3CC,QAAQ,CAACkB,OAAD;MACN,IAAI,CAACjT,OAAL,EAAc;QACZ;;;MAGF,KAAK,MAAMkT,MAAX,IAAqBD,OAArB,EAA8B;QAC5B,MAAM;UAAC7b,IAAD;UAAOgG;YAAU8V,MAAvB;;QAEA,IACE9b,IAAI,KAAK,WAAT,IACAgG,MAAM,YAAY+V,WADlB,IAEA/V,MAAM,CAACgW,QAAP,CAAgBpT,OAAhB,CAHF,EAIE;UACA6S,WAAW;UACX;;;;;GAfoC,CAA5C;EAoBA,MAAMN,cAAc,GAAGF,iBAAiB,CAAC;IAACN,QAAQ,EAAEc;GAAZ,CAAxC;EAEAQ,yBAAyB,CAAC;IACxBR,WAAW;;IAEX,IAAI7S,OAAJ,EAAa;MACXuS,cAAc,QAAd,YAAAA,cAAc,CAAEe,OAAhB,CAAwBtT,OAAxB;MACAkS,gBAAgB,QAAhB,YAAAA,gBAAgB,CAAEoB,OAAlB,CAA0B1Q,QAAQ,CAAC2Q,IAAnC,EAAyC;QACvCC,SAAS,EAAE,IAD4B;QAEvCC,OAAO,EAAE;OAFX;KAFF,MAMO;MACLlB,cAAc,QAAd,YAAAA,cAAc,CAAEH,UAAhB;MACAF,gBAAgB,QAAhB,YAAAA,gBAAgB,CAAEE,UAAlB;;GAXqB,EAatB,CAACpS,OAAD,CAbsB,CAAzB;EAeA,OAAOvF,IAAP;AACD;;SC3EeiZ,aAAajZ;EAC3B,MAAMkZ,WAAW,GAAGhC,eAAe,CAAClX,IAAD,CAAnC;EAEA,OAAO+D,YAAY,CAAC/D,IAAD,EAAOkZ,WAAP,CAAnB;AACD;;ACJD,MAAMvD,cAAY,GAAc,EAAhC;AAEA,SAAgBwD,uBAAuBnT;EACrC,MAAMoT,YAAY,GAAGnF,MAAM,CAACjO,IAAD,CAA3B;EAEA,MAAMqT,SAAS,GAAGxE,WAAW,CAC1BgC,aAAD;IACE,IAAI,CAAC7Q,IAAL,EAAW;MACT,OAAO2P,cAAP;;;IAGF,IACEkB,aAAa,IACbA,aAAa,KAAKlB,cADlB,IAEA3P,IAFA,IAGAoT,YAAY,CAACjL,OAHb,IAIAnI,IAAI,CAACiB,UAAL,KAAoBmS,YAAY,CAACjL,OAAb,CAAqBlH,UAL3C,EAME;MACA,OAAO4P,aAAP;;;IAGF,OAAOrQ,sBAAsB,CAACR,IAAD,CAA7B;GAhByB,EAkB3B,CAACA,IAAD,CAlB2B,CAA7B;EAqBAhK,SAAS,CAAC;IACRod,YAAY,CAACjL,OAAb,GAAuBnI,IAAvB;GADO,EAEN,CAACA,IAAD,CAFM,CAAT;EAIA,OAAOqT,SAAP;AACD;;SCvBeC,iBAAiBC;EAC/B,MAAM,CACJC,iBADI,EAEJC,oBAFI,IAGFpd,QAAQ,CAA2B,IAA3B,CAHZ;EAIA,MAAMqd,YAAY,GAAGzF,MAAM,CAACsF,QAAD,CAA3B;;EAGA,MAAMI,YAAY,GAAGpd,WAAW,CAAEK,KAAD;IAC/B,MAAMiK,gBAAgB,GAAGO,oBAAoB,CAACxK,KAAK,CAAC+F,MAAP,CAA7C;;IAEA,IAAI,CAACkE,gBAAL,EAAuB;MACrB;;;IAGF4S,oBAAoB,CAAED,iBAAD;MACnB,IAAI,CAACA,iBAAL,EAAwB;QACtB,OAAO,IAAP;;;MAGFA,iBAAiB,CAAC1C,GAAlB,CACEjQ,gBADF,EAEEmB,oBAAoB,CAACnB,gBAAD,CAFtB;MAKA,OAAO,IAAI+O,GAAJ,CAAQ4D,iBAAR,CAAP;KAVkB,CAApB;GAP8B,EAmB7B,EAnB6B,CAAhC;EAqBAxd,SAAS,CAAC;IACR,MAAM4d,gBAAgB,GAAGF,YAAY,CAACvL,OAAtC;;IAEA,IAAIoL,QAAQ,KAAKK,gBAAjB,EAAmC;MACjCC,OAAO,CAACD,gBAAD,CAAP;MAEA,MAAME,OAAO,GAAGP,QAAQ,CACrB/D,GADa,CACRjQ,OAAD;QACH,MAAMwU,iBAAiB,GAAG3S,oBAAoB,CAAC7B,OAAD,CAA9C;;QAEA,IAAIwU,iBAAJ,EAAuB;UACrBA,iBAAiB,CAACxO,gBAAlB,CAAmC,QAAnC,EAA6CoO,YAA7C,EAA2D;YACzDvI,OAAO,EAAE;WADX;UAIA,OAAO,CACL2I,iBADK,EAEL/R,oBAAoB,CAAC+R,iBAAD,CAFf,CAAP;;;QAMF,OAAO,IAAP;OAfY,EAiBb5a,MAjBa,CAmBVuD,KADF,IAKKA,KAAK,IAAI,IAvBF,CAAhB;MA0BA+W,oBAAoB,CAACK,OAAO,CAAC7Y,MAAR,GAAiB,IAAI2U,GAAJ,CAAQkE,OAAR,CAAjB,GAAoC,IAArC,CAApB;MAEAJ,YAAY,CAACvL,OAAb,GAAuBoL,QAAvB;;;IAGF,OAAO;MACLM,OAAO,CAACN,QAAD,CAAP;MACAM,OAAO,CAACD,gBAAD,CAAP;KAFF;;IAKA,SAASC,OAAT,CAAiBN,QAAjB;MACEA,QAAQ,CAAC1c,OAAT,CAAkB0I,OAAD;QACf,MAAMwU,iBAAiB,GAAG3S,oBAAoB,CAAC7B,OAAD,CAA9C;QAEAwU,iBAAiB,QAAjB,YAAAA,iBAAiB,CAAE3O,mBAAnB,CAAuC,QAAvC,EAAiDuO,YAAjD;OAHF;;GA3CK,EAiDN,CAACA,YAAD,EAAeJ,QAAf,CAjDM,CAAT;EAmDA,OAAOnb,OAAO,CAAC;IACb,IAAImb,QAAQ,CAACtY,MAAb,EAAqB;MACnB,OAAOuY,iBAAiB,GACpBQ,KAAK,CAACC,IAAN,CAAWT,iBAAiB,CAACU,MAAlB,EAAX,EAAuChY,MAAvC,CACE,CAACkC,GAAD,EAAMqL,WAAN,KAAsBjT,GAAG,CAAC4H,GAAD,EAAMqL,WAAN,CAD3B,EAEErQ,kBAFF,CADoB,GAKpB0K,gBAAgB,CAACyP,QAAD,CALpB;;;IAQF,OAAOna,kBAAP;GAVY,EAWX,CAACma,QAAD,EAAWC,iBAAX,CAXW,CAAd;AAYD;;SCpGeW,sBACd1P,eACAuL;MAAAA;IAAAA,eAAsB;;;EAEtB,MAAMoE,oBAAoB,GAAGnG,MAAM,CAAqB,IAArB,CAAnC;EAEAjY,SAAS,CACP;IACEoe,oBAAoB,CAACjM,OAArB,GAA+B,IAA/B;GAFK;EAKP6H,YALO,CAAT;EAQAha,SAAS,CAAC;IACR,MAAMqe,gBAAgB,GAAG5P,aAAa,KAAKrL,kBAA3C;;IAEA,IAAIib,gBAAgB,IAAI,CAACD,oBAAoB,CAACjM,OAA9C,EAAuD;MACrDiM,oBAAoB,CAACjM,OAArB,GAA+B1D,aAA/B;;;IAGF,IAAI,CAAC4P,gBAAD,IAAqBD,oBAAoB,CAACjM,OAA9C,EAAuD;MACrDiM,oBAAoB,CAACjM,OAArB,GAA+B,IAA/B;;GARK,EAUN,CAAC1D,aAAD,CAVM,CAAT;EAYA,OAAO2P,oBAAoB,CAACjM,OAArB,GACHmM,QAAQ,CAAC7P,aAAD,EAAgB2P,oBAAoB,CAACjM,OAArC,CADL,GAEH/O,kBAFJ;AAGD;;SC7Bemb,eAAerb;EAC7BlD,SAAS,CACP;IACE,IAAI,CAACqL,SAAL,EAAgB;MACd;;;IAGF,MAAMmT,WAAW,GAAGtb,OAAO,CAACsW,GAAR,CAAY;MAAA,IAAC;QAACzW;OAAF;MAAA,OAAcA,MAAM,CAAC6T,KAArB,oBAAc7T,MAAM,CAAC6T,KAAP,EAAd;KAAZ,CAApB;IAEA,OAAO;MACL,KAAK,MAAMC,QAAX,IAAuB2H,WAAvB,EAAoC;QAClC3H,QAAQ,QAAR,YAAAA,QAAQ;;KAFZ;GARK;;EAgBP3T,OAAO,CAACsW,GAAR,CAAY;IAAA,IAAC;MAACzW;KAAF;IAAA,OAAcA,MAAd;GAAZ,CAhBO,CAAT;AAkBD;;SCXe0b,sBACdre,WACAe;EAEA,OAAOiB,OAAO,CAAC;IACb,OAAOhC,SAAS,CAAC8F,MAAV,CACL,CAACkC,GAAD;UAAM;QAACiH,SAAD;QAAYC;;;MAChBlH,GAAG,CAACiH,SAAD,CAAH,GAAkBzO,KAAD;QACf0O,OAAO,CAAC1O,KAAD,EAAQO,EAAR,CAAP;OADF;;MAIA,OAAOiH,GAAP;KANG,EAQL,EARK,CAAP;GADY,EAWX,CAAChI,SAAD,EAAYe,EAAZ,CAXW,CAAd;AAYD;;SCzBeud,cAAcnV;EAC5B,OAAOnH,OAAO,CAAC,MAAOmH,OAAO,GAAGK,mBAAmB,CAACL,OAAD,CAAtB,GAAkC,IAAjD,EAAwD,CACpEA,OADoE,CAAxD,CAAd;AAGD;;ACED,MAAMoQ,cAAY,GAAW,EAA7B;AAEA,SAAgBgF,SACdpB,UACApP;MAAAA;IAAAA,UAA4C7E;;;EAE5C,MAAM,CAACsV,YAAD,IAAiBrB,QAAvB;EACA,MAAMsB,UAAU,GAAGH,aAAa,CAC9BE,YAAY,GAAGnV,SAAS,CAACmV,YAAD,CAAZ,GAA6B,IADX,CAAhC;EAGA,MAAM,CAACE,KAAD,EAAQC,QAAR,IAAoB1e,QAAQ,CAAesZ,cAAf,CAAlC;;EAEA,SAASqF,YAAT;IACED,QAAQ,CAAC;MACP,IAAI,CAACxB,QAAQ,CAACtY,MAAd,EAAsB;QACpB,OAAO0U,cAAP;;;MAGF,OAAO4D,QAAQ,CAAC/D,GAAT,CAAcjQ,OAAD,IAClB2C,0BAA0B,CAAC3C,OAAD,CAA1B,GACKsV,UADL,GAEI,IAAItQ,IAAJ,CAASJ,OAAO,CAAC5E,OAAD,CAAhB,EAA2BA,OAA3B,CAHC,CAAP;KALM,CAAR;;;EAaF,MAAMuS,cAAc,GAAGF,iBAAiB,CAAC;IAACN,QAAQ,EAAE0D;GAAZ,CAAxC;EAEApC,yBAAyB,CAAC;IACxBd,cAAc,QAAd,YAAAA,cAAc,CAAEH,UAAhB;IACAqD,YAAY;IACZzB,QAAQ,CAAC1c,OAAT,CAAkB0I,OAAD,IAAauS,cAAb,oBAAaA,cAAc,CAAEe,OAAhB,CAAwBtT,OAAxB,CAA9B;GAHuB,EAItB,CAACgU,QAAD,CAJsB,CAAzB;EAMA,OAAOuB,KAAP;AACD;;SC3CeG,kBACdjV;EAEA,IAAI,CAACA,IAAL,EAAW;IACT,OAAO,IAAP;;;EAGF,IAAIA,IAAI,CAACkV,QAAL,CAAcja,MAAd,GAAuB,CAA3B,EAA8B;IAC5B,OAAO+E,IAAP;;;EAEF,MAAMmV,UAAU,GAAGnV,IAAI,CAACkV,QAAL,CAAc,CAAd,CAAnB;EAEA,OAAOnU,aAAa,CAACoU,UAAD,CAAb,GAA4BA,UAA5B,GAAyCnV,IAAhD;AACD;;SCHeoV;MAAwB;IACtCjR;;EAEA,MAAM,CAACnK,IAAD,EAAOmY,OAAP,IAAkB9b,QAAQ,CAAoB,IAApB,CAAhC;EACA,MAAMwb,YAAY,GAAGtb,WAAW,CAC7Bud,OAAD;IACE,KAAK,MAAM;MAACnX;KAAZ,IAAuBmX,OAAvB,EAAgC;MAC9B,IAAI/S,aAAa,CAACpE,MAAD,CAAjB,EAA2B;QACzBwV,OAAO,CAAEnY,IAAD;UACN,MAAMuY,OAAO,GAAGpO,OAAO,CAACxH,MAAD,CAAvB;UAEA,OAAO3C,IAAI,GACP,EAAC,GAAGA,IAAJ;YAAUK,KAAK,EAAEkY,OAAO,CAAClY,KAAzB;YAAgCE,MAAM,EAAEgY,OAAO,CAAChY;WADzC,GAEPgY,OAFJ;SAHK,CAAP;QAOA;;;GAXwB,EAe9B,CAACpO,OAAD,CAf8B,CAAhC;EAiBA,MAAM2N,cAAc,GAAGF,iBAAiB,CAAC;IAACN,QAAQ,EAAEO;GAAZ,CAAxC;EACA,MAAMwD,gBAAgB,GAAG9e,WAAW,CACjCgJ,OAAD;IACE,MAAMS,IAAI,GAAGiV,iBAAiB,CAAC1V,OAAD,CAA9B;IAEAuS,cAAc,QAAd,YAAAA,cAAc,CAAEH,UAAhB;;IAEA,IAAI3R,IAAJ,EAAU;MACR8R,cAAc,QAAd,YAAAA,cAAc,CAAEe,OAAhB,CAAwB7S,IAAxB;;;IAGFmS,OAAO,CAACnS,IAAI,GAAGmE,OAAO,CAACnE,IAAD,CAAV,GAAmB,IAAxB,CAAP;GAVgC,EAYlC,CAACmE,OAAD,EAAU2N,cAAV,CAZkC,CAApC;EAcA,MAAM,CAACwD,OAAD,EAAUC,MAAV,IAAoBC,UAAU,CAACH,gBAAD,CAApC;EAEA,OAAOjd,OAAO,CACZ,OAAO;IACLkd,OADK;IAELtb,IAFK;IAGLub;GAHF,CADY,EAMZ,CAACvb,IAAD,EAAOsb,OAAP,EAAgBC,MAAhB,CANY,CAAd;AAQD;;AC9CM,MAAME,cAAc,GAAG,CAC5B;EAAC1c,MAAM,EAAEsT,aAAT;EAAwBrT,OAAO,EAAE;AAAjC,CAD4B,EAE5B;EAACD,MAAM,EAAEoO,cAAT;EAAyBnO,OAAO,EAAE;AAAlC,CAF4B,CAAvB;AAKP,AAAO,MAAM0c,WAAW,GAAY;EAACvN,OAAO,EAAE;AAAV,CAA7B;AAEP,AAAO,MAAMwN,6BAA6B,GAAyC;EACjF5e,SAAS,EAAE;IACToN,OAAO,EAAExE;GAFsE;EAIjFiW,SAAS,EAAE;IACTzR,OAAO,EAAExE,8BADA;IAET0Q,QAAQ,EAAEZ,iBAAiB,CAACoG,aAFnB;IAGTzF,SAAS,EAAEV,kBAAkB,CAACoG;GAPiD;EASjFC,WAAW,EAAE;IACX5R,OAAO,EAAE7E;;AAVsE,CAA5E;;MCdM0W,+BAA+BpG;EAI1ClU,GAAG,CAACvE,EAAD;;;IACD,OAAOA,EAAE,IAAI,IAAN,iBAAa,MAAMuE,GAAN,CAAUvE,EAAV,CAAb,yBAA8B+P,SAA9B,GAA0CA,SAAjD;;;EAGF+O,OAAO;IACL,OAAOjC,KAAK,CAACC,IAAN,CAAW,KAAKC,MAAL,EAAX,CAAP;;;EAGFgC,UAAU;IACR,OAAO,KAAKD,OAAL,GAAe9c,MAAf,CAAsB;MAAA,IAAC;QAACyU;OAAF;MAAA,OAAgB,CAACA,QAAjB;KAAtB,CAAP;;;EAGFuI,UAAU,CAAChf,EAAD;;;IACR,6CAAO,KAAKuE,GAAL,CAASvE,EAAT,CAAP,qBAAO,UAAc6I,IAAd,CAAmBmI,OAA1B,oCAAqCjB,SAArC;;;;;ACfG,MAAMkP,oBAAoB,GAA4B;EAC3DC,cAAc,EAAE,IAD2C;EAE3Dnf,MAAM,EAAE,IAFmD;EAG3D+Q,UAAU,EAAE,IAH+C;EAI3DqO,cAAc,EAAE,IAJ2C;EAK3Dvb,UAAU,EAAE,IAL+C;EAM3Dwb,iBAAiB,EAAE,IANwC;EAO3DtH,cAAc,eAAE,IAAIW,GAAJ,EAP2C;EAQ3DtU,cAAc,eAAE,IAAIsU,GAAJ,EAR2C;EAS3DrU,mBAAmB,eAAE,IAAIya,sBAAJ,EATsC;EAU3D3e,IAAI,EAAE,IAVqD;EAW3D0e,WAAW,EAAE;IACXT,OAAO,EAAE;MACPnN,OAAO,EAAE;KAFA;IAIXnO,IAAI,EAAE,IAJK;IAKXub,MAAM,EAAE1c;GAhBiD;EAkB3DkL,mBAAmB,EAAE,EAlBsC;EAmB3D0J,uBAAuB,EAAE,EAnBkC;EAoB3D+I,sBAAsB,EAAEb,6BApBmC;EAqB3DjF,0BAA0B,EAAE7X,IArB+B;EAsB3Dgc,UAAU,EAAE,IAtB+C;EAuB3D9D,kBAAkB,EAAE;AAvBuC,CAAtD;AA0BP,AAAO,MAAM0F,sBAAsB,GAA8B;EAC/DJ,cAAc,EAAE,IAD+C;EAE/DvM,UAAU,EAAE,EAFmD;EAG/D5S,MAAM,EAAE,IAHuD;EAI/Dof,cAAc,EAAE,IAJ+C;EAK/DI,iBAAiB,EAAE;IACjB3f,SAAS,EAAE;GANkD;EAQ/DL,QAAQ,EAAEmC,IARqD;EAS/DoW,cAAc,eAAE,IAAIW,GAAJ,EAT+C;EAU/DvY,IAAI,EAAE,IAVyD;EAW/DqZ,0BAA0B,EAAE7X;AAXmC,CAA1D;AAcP,AAAO,MAAM8d,eAAe,gBAAGhhB,aAAa,CAC1C8gB,sBAD0C,CAArC;AAIP,AAAO,MAAMG,aAAa,gBAAGjhB,aAAa,CACxCygB,oBADwC,CAAnC;;SC/CSS;EACd,OAAO;IACL9f,SAAS,EAAE;MACTG,MAAM,EAAE,IADC;MAETyT,kBAAkB,EAAE;QAACpR,CAAC,EAAE,CAAJ;QAAOC,CAAC,EAAE;OAFrB;MAGTsd,KAAK,EAAE,IAAIlH,GAAJ,EAHE;MAITmH,SAAS,EAAE;QAACxd,CAAC,EAAE,CAAJ;QAAOC,CAAC,EAAE;;KALlB;IAOLoc,SAAS,EAAE;MACT9F,UAAU,EAAE,IAAIkG,sBAAJ;;GARhB;AAWD;AAED,SAAgBgB,QAAQC,OAAcC;EACpC,QAAQA,MAAM,CAACvgB,IAAf;IACE,KAAKiC,MAAM,CAACyS,SAAZ;MACE,OAAO,EACL,GAAG4L,KADE;QAELlgB,SAAS,EAAE,EACT,GAAGkgB,KAAK,CAAClgB,SADA;UAET4T,kBAAkB,EAAEuM,MAAM,CAACvM,kBAFlB;UAGTzT,MAAM,EAAEggB,MAAM,CAAChgB;;OALnB;;IAQF,KAAK0B,MAAM,CAACue,QAAZ;MACE,IAAIF,KAAK,CAAClgB,SAAN,CAAgBG,MAAhB,IAA0B,IAA9B,EAAoC;QAClC,OAAO+f,KAAP;;;MAGF,OAAO,EACL,GAAGA,KADE;QAELlgB,SAAS,EAAE,EACT,GAAGkgB,KAAK,CAAClgB,SADA;UAETggB,SAAS,EAAE;YACTxd,CAAC,EAAE2d,MAAM,CAACzN,WAAP,CAAmBlQ,CAAnB,GAAuB0d,KAAK,CAAClgB,SAAN,CAAgB4T,kBAAhB,CAAmCpR,CADpD;YAETC,CAAC,EAAE0d,MAAM,CAACzN,WAAP,CAAmBjQ,CAAnB,GAAuByd,KAAK,CAAClgB,SAAN,CAAgB4T,kBAAhB,CAAmCnR;;;OANnE;;IAUF,KAAKZ,MAAM,CAACwe,OAAZ;IACA,KAAKxe,MAAM,CAACye,UAAZ;MACE,OAAO,EACL,GAAGJ,KADE;QAELlgB,SAAS,EAAE,EACT,GAAGkgB,KAAK,CAAClgB,SADA;UAETG,MAAM,EAAE,IAFC;UAGTyT,kBAAkB,EAAE;YAACpR,CAAC,EAAE,CAAJ;YAAOC,CAAC,EAAE;WAHrB;UAITud,SAAS,EAAE;YAACxd,CAAC,EAAE,CAAJ;YAAOC,CAAC,EAAE;;;OANzB;;IAUF,KAAKZ,MAAM,CAAC0e,iBAAZ;MAA+B;QAC7B,MAAM;UAAC/X;YAAW2X,MAAlB;QACA,MAAM;UAAC/f;YAAMoI,OAAb;QACA,MAAMuQ,UAAU,GAAG,IAAIkG,sBAAJ,CAA2BiB,KAAK,CAACrB,SAAN,CAAgB9F,UAA3C,CAAnB;QACAA,UAAU,CAACgB,GAAX,CAAe3Z,EAAf,EAAmBoI,OAAnB;QAEA,OAAO,EACL,GAAG0X,KADE;UAELrB,SAAS,EAAE,EACT,GAAGqB,KAAK,CAACrB,SADA;YAET9F;;SAJJ;;;IASF,KAAKlX,MAAM,CAAC2e,oBAAZ;MAAkC;QAChC,MAAM;UAACpgB,EAAD;UAAK0N,GAAL;UAAU+I;YAAYsJ,MAA5B;QACA,MAAM3X,OAAO,GAAG0X,KAAK,CAACrB,SAAN,CAAgB9F,UAAhB,CAA2BpU,GAA3B,CAA+BvE,EAA/B,CAAhB;;QAEA,IAAI,CAACoI,OAAD,IAAYsF,GAAG,KAAKtF,OAAO,CAACsF,GAAhC,EAAqC;UACnC,OAAOoS,KAAP;;;QAGF,MAAMnH,UAAU,GAAG,IAAIkG,sBAAJ,CAA2BiB,KAAK,CAACrB,SAAN,CAAgB9F,UAA3C,CAAnB;QACAA,UAAU,CAACgB,GAAX,CAAe3Z,EAAf,EAAmB,EACjB,GAAGoI,OADc;UAEjBqO;SAFF;QAKA,OAAO,EACL,GAAGqJ,KADE;UAELrB,SAAS,EAAE,EACT,GAAGqB,KAAK,CAACrB,SADA;YAET9F;;SAJJ;;;IASF,KAAKlX,MAAM,CAAC4e,mBAAZ;MAAiC;QAC/B,MAAM;UAACrgB,EAAD;UAAK0N;YAAOqS,MAAlB;QACA,MAAM3X,OAAO,GAAG0X,KAAK,CAACrB,SAAN,CAAgB9F,UAAhB,CAA2BpU,GAA3B,CAA+BvE,EAA/B,CAAhB;;QAEA,IAAI,CAACoI,OAAD,IAAYsF,GAAG,KAAKtF,OAAO,CAACsF,GAAhC,EAAqC;UACnC,OAAOoS,KAAP;;;QAGF,MAAMnH,UAAU,GAAG,IAAIkG,sBAAJ,CAA2BiB,KAAK,CAACrB,SAAN,CAAgB9F,UAA3C,CAAnB;QACAA,UAAU,CAACrZ,MAAX,CAAkBU,EAAlB;QAEA,OAAO,EACL,GAAG8f,KADE;UAELrB,SAAS,EAAE,EACT,GAAGqB,KAAK,CAACrB,SADA;YAET9F;;SAJJ;;;IASF;MAAS;QACP,OAAOmH,KAAP;;;AAGL;;SCzGeQ;MAAa;IAAC7J;;EAC5B,MAAM;IAAC1W,MAAD;IAASmf,cAAT;IAAyBpH;MAAkBlZ,UAAU,CAAC4gB,eAAD,CAA3D;EACA,MAAMe,sBAAsB,GAAG9I,WAAW,CAACyH,cAAD,CAA1C;EACA,MAAMsB,gBAAgB,GAAG/I,WAAW,CAAC1X,MAAD,oBAACA,MAAM,CAAEC,EAAT,CAApC;;EAGAnB,SAAS,CAAC;IACR,IAAI4X,QAAJ,EAAc;MACZ;;;IAGF,IAAI,CAACyI,cAAD,IAAmBqB,sBAAnB,IAA6CC,gBAAgB,IAAI,IAArE,EAA2E;MACzE,IAAI,CAACvP,eAAe,CAACsP,sBAAD,CAApB,EAA8C;QAC5C;;;MAGF,IAAIvV,QAAQ,CAACyV,aAAT,KAA2BF,sBAAsB,CAAC/a,MAAtD,EAA8D;;QAE5D;;;MAGF,MAAMuS,aAAa,GAAGD,cAAc,CAACvT,GAAf,CAAmBic,gBAAnB,CAAtB;;MAEA,IAAI,CAACzI,aAAL,EAAoB;QAClB;;;MAGF,MAAM;QAAChF,aAAD;QAAgBlK;UAAQkP,aAA9B;;MAEA,IAAI,CAAChF,aAAa,CAAC/B,OAAf,IAA0B,CAACnI,IAAI,CAACmI,OAApC,EAA6C;QAC3C;;;MAGF0P,qBAAqB,CAAC;QACpB,KAAK,MAAMtY,OAAX,IAAsB,CAAC2K,aAAa,CAAC/B,OAAf,EAAwBnI,IAAI,CAACmI,OAA7B,CAAtB,EAA6D;UAC3D,IAAI,CAAC5I,OAAL,EAAc;YACZ;;;UAGF,MAAMuY,aAAa,GAAGC,sBAAsB,CAACxY,OAAD,CAA5C;;UAEA,IAAIuY,aAAJ,EAAmB;YACjBA,aAAa,CAACE,KAAd;YACA;;;OAVe,CAArB;;GA3BK,EA0CN,CACD3B,cADC,EAEDzI,QAFC,EAGDqB,cAHC,EAID0I,gBAJC,EAKDD,sBALC,CA1CM,CAAT;EAkDA,OAAO,IAAP;AACD;;SClEeO,eACdC;MACA;IAACxa,SAAD;IAAY,GAAGya;;EAEf,OAAOD,SAAS,QAAT,IAAAA,SAAS,CAAEjd,MAAX,GACHid,SAAS,CAAChc,MAAV,CAA4B,CAACC,WAAD,EAAc8B,QAAd;IAC1B,OAAOA,QAAQ,CAAC;MACdP,SAAS,EAAEvB,WADG;MAEd,GAAGgc;KAFU,CAAf;GADF,EAKGza,SALH,CADG,GAOHA,SAPJ;AAQD;;SCVe0a,0BACdnI;EAEA,OAAO7X,OAAO,CACZ,OAAO;IACLrB,SAAS,EAAE,EACT,GAAG4e,6BAA6B,CAAC5e,SADxB;MAET,IAAGkZ,MAAH,oBAAGA,MAAM,CAAElZ,SAAX;KAHG;IAKL6e,SAAS,EAAE,EACT,GAAGD,6BAA6B,CAACC,SADxB;MAET,IAAG3F,MAAH,oBAAGA,MAAM,CAAE2F,SAAX;KAPG;IASLG,WAAW,EAAE,EACX,GAAGJ,6BAA6B,CAACI,WADtB;MAEX,IAAG9F,MAAH,oBAAGA,MAAM,CAAE8F,WAAX;;GAXJ,CADY;EAgBZ,CAAC9F,MAAD,oBAACA,MAAM,CAAElZ,SAAT,EAAoBkZ,MAApB,oBAAoBA,MAAM,CAAE2F,SAA5B,EAAuC3F,MAAvC,oBAAuCA,MAAM,CAAE8F,WAA/C,CAhBY,CAAd;AAkBD;;SCXesC;MAAiC;IAC/CpQ,UAD+C;IAE/C9D,OAF+C;IAG/C+O,WAH+C;IAI/CjD,MAAM,GAAG;;EAET,MAAMqI,WAAW,GAAGrK,MAAM,CAAC,KAAD,CAA1B;EACA,MAAM;IAAC1U,CAAD;IAAIC;MAAK,OAAOyW,MAAP,KAAkB,SAAlB,GAA8B;IAAC1W,CAAC,EAAE0W,MAAJ;IAAYzW,CAAC,EAAEyW;GAA7C,GAAuDA,MAAtE;EAEA2C,yBAAyB,CAAC;IACxB,MAAMhF,QAAQ,GAAG,CAACrU,CAAD,IAAM,CAACC,CAAxB;;IAEA,IAAIoU,QAAQ,IAAI,CAAC3F,UAAjB,EAA6B;MAC3BqQ,WAAW,CAACnQ,OAAZ,GAAsB,KAAtB;MACA;;;IAGF,IAAImQ,WAAW,CAACnQ,OAAZ,IAAuB,CAAC+K,WAA5B,EAAyC;;;MAGvC;;;;IAIF,MAAMlT,IAAI,GAAGiI,UAAH,oBAAGA,UAAU,CAAEjI,IAAZ,CAAiBmI,OAA9B;;IAEA,IAAI,CAACnI,IAAD,IAASA,IAAI,CAACsS,WAAL,KAAqB,KAAlC,EAAyC;;;MAGvC;;;IAGF,MAAMtY,IAAI,GAAGmK,OAAO,CAACnE,IAAD,CAApB;IACA,MAAMuY,SAAS,GAAGxa,YAAY,CAAC/D,IAAD,EAAOkZ,WAAP,CAA9B;;IAEA,IAAI,CAAC3Z,CAAL,EAAQ;MACNgf,SAAS,CAAChf,CAAV,GAAc,CAAd;;;IAGF,IAAI,CAACC,CAAL,EAAQ;MACN+e,SAAS,CAAC/e,CAAV,GAAc,CAAd;;;;IAIF8e,WAAW,CAACnQ,OAAZ,GAAsB,IAAtB;;IAEA,IAAIvO,IAAI,CAAC+J,GAAL,CAAS4U,SAAS,CAAChf,CAAnB,IAAwB,CAAxB,IAA6BK,IAAI,CAAC+J,GAAL,CAAS4U,SAAS,CAAC/e,CAAnB,IAAwB,CAAzD,EAA4D;MAC1D,MAAM2H,uBAAuB,GAAGD,0BAA0B,CAAClB,IAAD,CAA1D;;MAEA,IAAImB,uBAAJ,EAA6B;QAC3BA,uBAAuB,CAACmI,QAAxB,CAAiC;UAC/BhP,GAAG,EAAEie,SAAS,CAAC/e,CADgB;UAE/BY,IAAI,EAAEme,SAAS,CAAChf;SAFlB;;;GAzCmB,EA+CtB,CAAC0O,UAAD,EAAa1O,CAAb,EAAgBC,CAAhB,EAAmB0Z,WAAnB,EAAgC/O,OAAhC,CA/CsB,CAAzB;AAgDD;;ACoDM,MAAMqU,sBAAsB,gBAAG7iB,aAAa,CAAY,EAC7D,GAAGyD,kBAD0D;EAE7DyE,MAAM,EAAE,CAFqD;EAG7DC,MAAM,EAAE;AAHqD,CAAZ,CAA5C;AAMP,IAAK2a,MAAL;;AAAA,WAAKA;EACHA,mCAAA,kBAAA;EACAA,kCAAA,iBAAA;EACAA,iCAAA,gBAAA;AACD,CAJD,EAAKA,MAAM,KAANA,MAAM,KAAA,CAAX;;AAMA,MAAaC,UAAU,gBAAGC,IAAI,CAAC,SAASD,UAAT;;;MAAoB;IACjDvhB,EADiD;IAEjDyhB,aAFiD;IAGjDvK,UAAU,GAAG,IAHoC;IAIjD6G,QAJiD;IAKjDhc,OAAO,GAAGuc,cALuC;IAMjDoD,kBAAkB,GAAGzb,gBAN4B;IAOjD0b,SAPiD;IAQjDZ,SARiD;IASjD,GAAG9Q;;EAEH,MAAM2R,KAAK,GAAGC,UAAU,CAAChC,OAAD,EAAU9P,SAAV,EAAqB2P,eAArB,CAAxB;EACA,MAAM,CAACI,KAAD,EAAQvgB,QAAR,IAAoBqiB,KAA1B;EACA,MAAM,CAACE,oBAAD,EAAuBC,uBAAvB,IACJ/iB,qBAAqB,EADvB;EAEA,MAAM,CAACgjB,MAAD,EAASC,SAAT,IAAsB/iB,QAAQ,CAASoiB,MAAM,CAACY,aAAhB,CAApC;EACA,MAAMC,aAAa,GAAGH,MAAM,KAAKV,MAAM,CAACc,WAAxC;EACA,MAAM;IACJxiB,SAAS,EAAE;MAACG,MAAM,EAAEsiB,QAAT;MAAmB1C,KAAK,EAAE7H,cAA1B;MAA0C8H;KADjD;IAEJnB,SAAS,EAAE;MAAC9F,UAAU,EAAEvU;;MACtB0b,KAHJ;EAIA,MAAMjX,IAAI,GAAGwZ,QAAQ,IAAI,IAAZ,GAAmBvK,cAAc,CAACvT,GAAf,CAAmB8d,QAAnB,CAAnB,GAAkD,IAA/D;EACA,MAAMC,WAAW,GAAGxL,MAAM,CAA4B;IACpDyL,OAAO,EAAE,IAD2C;IAEpDC,UAAU,EAAE;GAFY,CAA1B;EAIA,MAAMziB,MAAM,GAAGkB,OAAO,CACpB;IAAA;;IAAA,OACEohB,QAAQ,IAAI,IAAZ,GACI;MACEriB,EAAE,EAAEqiB,QADN;;MAGE/e,IAAI,gBAAEuF,IAAF,oBAAEA,IAAI,CAAEvF,IAAR,yBAAgBib,WAHtB;MAIE1b,IAAI,EAAEyf;KALZ,GAOI,IARN;GADoB,EAUpB,CAACD,QAAD,EAAWxZ,IAAX,CAVoB,CAAtB;EAYA,MAAM4Z,SAAS,GAAG3L,MAAM,CAA0B,IAA1B,CAAxB;EACA,MAAM,CAAC4L,YAAD,EAAeC,eAAf,IAAkCzjB,QAAQ,CAAwB,IAAxB,CAAhD;EACA,MAAM,CAACggB,cAAD,EAAiB0D,iBAAjB,IAAsC1jB,QAAQ,CAAe,IAAf,CAApD;EACA,MAAM2jB,WAAW,GAAGvJ,cAAc,CAACrJ,KAAD,EAAQ/N,MAAM,CAAC6a,MAAP,CAAc9M,KAAd,CAAR,CAAlC;EACA,MAAM6S,sBAAsB,GAAGhiB,WAAW,mBAAmBd,EAAnB,CAA1C;EACA,MAAM+iB,0BAA0B,GAAG9hB,OAAO,CACxC,MAAMmD,mBAAmB,CAAC2a,UAApB,EADkC,EAExC,CAAC3a,mBAAD,CAFwC,CAA1C;EAIA,MAAMib,sBAAsB,GAAG4B,yBAAyB,CAACU,SAAD,CAAxD;EACA,MAAM;IAACxd,cAAD;IAAiBoV,0BAAjB;IAA6CK;MACjDlB,qBAAqB,CAACqK,0BAAD,EAA6B;IAChDnK,QAAQ,EAAEuJ,aADsC;IAEhDtJ,YAAY,EAAE,CAAC+G,SAAS,CAACxd,CAAX,EAAcwd,SAAS,CAACvd,CAAxB,CAFkC;IAGhDyW,MAAM,EAAEuG,sBAAsB,CAACZ;GAHZ,CADvB;EAMA,MAAM3N,UAAU,GAAG+G,aAAa,CAACC,cAAD,EAAiBuK,QAAjB,CAAhC;EACA,MAAMW,qBAAqB,GAAG/hB,OAAO,CACnC,MAAOie,cAAc,GAAGnc,mBAAmB,CAACmc,cAAD,CAAtB,GAAyC,IAD3B,EAEnC,CAACA,cAAD,CAFmC,CAArC;EAIA,MAAM+D,iBAAiB,GAAGC,sBAAsB,EAAhD;EACA,MAAMC,qBAAqB,GAAGlJ,cAAc,CAC1CnJ,UAD0C,EAE1CuO,sBAAsB,CAACzf,SAAvB,CAAiCoN,OAFS,CAA5C;EAKAkU,gCAAgC,CAAC;IAC/BpQ,UAAU,EAAEuR,QAAQ,IAAI,IAAZ,GAAmBvK,cAAc,CAACvT,GAAf,CAAmB8d,QAAnB,CAAnB,GAAkD,IAD/B;IAE/BvJ,MAAM,EAAEmK,iBAAiB,CAACG,uBAFK;IAG/BrH,WAAW,EAAEoH,qBAHkB;IAI/BnW,OAAO,EAAEqS,sBAAsB,CAACzf,SAAvB,CAAiCoN;GAJZ,CAAhC;EAOA,MAAMmS,cAAc,GAAGrE,OAAO,CAC5BhK,UAD4B,EAE5BuO,sBAAsB,CAACzf,SAAvB,CAAiCoN,OAFL,EAG5BmW,qBAH4B,CAA9B;EAKA,MAAM/D,iBAAiB,GAAGtE,OAAO,CAC/BhK,UAAU,GAAGA,UAAU,CAACuS,aAAd,GAA8B,IADT,CAAjC;EAGA,MAAMC,aAAa,GAAGxM,MAAM,CAAgB;IAC1CoI,cAAc,EAAE,IAD0B;IAE1Cnf,MAAM,EAAE,IAFkC;IAG1C+Q,UAH0C;IAI1C5M,aAAa,EAAE,IAJ2B;IAK1CN,UAAU,EAAE,IAL8B;IAM1CO,cAN0C;IAO1C2T,cAP0C;IAQ1CyL,YAAY,EAAE,IAR4B;IAS1CC,gBAAgB,EAAE,IATwB;IAU1Cpf,mBAV0C;IAW1ClE,IAAI,EAAE,IAXoC;IAY1C0M,mBAAmB,EAAE,EAZqB;IAa1C6W,uBAAuB,EAAE;GAbC,CAA5B;EAeA,MAAMC,QAAQ,GAAGtf,mBAAmB,CAAC4a,UAApB,0BACfsE,aAAa,CAACtS,OAAd,CAAsB9Q,IADP,qBACf,sBAA4BF,EADb,CAAjB;EAGA,MAAM4e,WAAW,GAAGX,uBAAuB,CAAC;IAC1CjR,OAAO,EAAEqS,sBAAsB,CAACT,WAAvB,CAAmC5R;GADH,CAA3C;;EAKA,MAAMuW,YAAY,4BAAG3E,WAAW,CAACT,OAAZ,CAAoBnN,OAAvB,oCAAkCF,UAApD;EACA,MAAM0S,gBAAgB,GAAGrB,aAAa,wBAClCvD,WAAW,CAAC/b,IADsB,gCACdsc,cADc,GAElC,IAFJ;EAGA,MAAMwE,eAAe,GAAGzQ,OAAO,CAC7B0L,WAAW,CAACT,OAAZ,CAAoBnN,OAApB,IAA+B4N,WAAW,CAAC/b,IADd,CAA/B;;;EAKA,MAAM+gB,aAAa,GAAG9H,YAAY,CAAC6H,eAAe,GAAG,IAAH,GAAUxE,cAA1B,CAAlC;;EAGA,MAAMzB,UAAU,GAAGH,aAAa,CAC9BgG,YAAY,GAAGjb,SAAS,CAACib,YAAD,CAAZ,GAA6B,IADX,CAAhC;;EAKA,MAAM3W,mBAAmB,GAAGoP,sBAAsB,CAChDmG,aAAa,GAAGuB,QAAH,WAAGA,QAAH,GAAe5S,UAAf,GAA4B,IADO,CAAlD;EAGA,MAAMwF,uBAAuB,GAAGkH,QAAQ,CAAC5Q,mBAAD,CAAxC;;EAGA,MAAMiX,iBAAiB,GAAG/C,cAAc,CAACC,SAAD,EAAY;IAClDxa,SAAS,EAAE;MACTnE,CAAC,EAAEwd,SAAS,CAACxd,CAAV,GAAcwhB,aAAa,CAACxhB,CADtB;MAETC,CAAC,EAAEud,SAAS,CAACvd,CAAV,GAAcuhB,aAAa,CAACvhB,CAFtB;MAGTqE,MAAM,EAAE,CAHC;MAITC,MAAM,EAAE;KALwC;IAOlDuY,cAPkD;IAQlDnf,MARkD;IASlDof,cATkD;IAUlDC,iBAVkD;IAWlDoE,gBAXkD;IAYlDtjB,IAAI,EAAEojB,aAAa,CAACtS,OAAd,CAAsB9Q,IAZsB;IAalD4jB,eAAe,EAAElF,WAAW,CAAC/b,IAbqB;IAclD+J,mBAdkD;IAelD0J,uBAfkD;IAgBlDoH;GAhBsC,CAAxC;EAmBA,MAAMrX,kBAAkB,GAAG2c,qBAAqB,GAC5C3jB,GAAG,CAAC2jB,qBAAD,EAAwBpD,SAAxB,CADyC,GAE5C,IAFJ;EAIA,MAAMtS,aAAa,GAAG6O,gBAAgB,CAACvP,mBAAD,CAAtC;;EAEA,MAAMmX,gBAAgB,GAAG/G,qBAAqB,CAAC1P,aAAD,CAA9C;;EAEA,MAAM0W,qBAAqB,GAAGhH,qBAAqB,CAAC1P,aAAD,EAAgB,CACjE6R,cADiE,CAAhB,CAAnD;EAIA,MAAMsE,uBAAuB,GAAGpkB,GAAG,CAACwkB,iBAAD,EAAoBE,gBAApB,CAAnC;EAEA,MAAM7f,aAAa,GAAGsf,gBAAgB,GAClCrc,eAAe,CAACqc,gBAAD,EAAmBK,iBAAnB,CADmB,GAElC,IAFJ;EAIA,MAAMjgB,UAAU,GACd7D,MAAM,IAAImE,aAAV,GACIwd,kBAAkB,CAAC;IACjB3hB,MADiB;IAEjBmE,aAFiB;IAGjBC,cAHiB;IAIjBC,mBAAmB,EAAE2e,0BAJJ;IAKjB1c;GALgB,CADtB,GAQI,IATN;EAUA,MAAM4d,MAAM,GAAGtgB,iBAAiB,CAACC,UAAD,EAAa,IAAb,CAAhC;EACA,MAAM,CAAC1D,IAAD,EAAOgkB,OAAP,IAAkBhlB,QAAQ,CAAc,IAAd,CAAhC;;;EAIA,MAAMilB,gBAAgB,GAAGR,eAAe,GACpCE,iBADoC,GAEpCxkB,GAAG,CAACwkB,iBAAD,EAAoBG,qBAApB,CAFP;EAIA,MAAMzd,SAAS,GAAGD,WAAW,CAC3B6d,gBAD2B,gBAE3BjkB,IAF2B,oBAE3BA,IAAI,CAAE2C,IAFqB,yBAEb,IAFa,EAG3Bsc,cAH2B,CAA7B;EAMA,MAAMiF,eAAe,GAAGtN,MAAM,CAAwB,IAAxB,CAA9B;EACA,MAAMuN,iBAAiB,GAAGjlB,WAAW,CACnC,CACEK,KADF;QAEE;MAACmC,MAAM,EAAEuW,MAAT;MAAiBtW;;;IAEjB,IAAI4gB,SAAS,CAACzR,OAAV,IAAqB,IAAzB,EAA+B;MAC7B;;;IAGF,MAAMF,UAAU,GAAGgH,cAAc,CAACvT,GAAf,CAAmBke,SAAS,CAACzR,OAA7B,CAAnB;;IAEA,IAAI,CAACF,UAAL,EAAiB;MACf;;;IAGF,MAAMoO,cAAc,GAAGzf,KAAK,CAACoT,WAA7B;IAEA,MAAMyR,cAAc,GAAG,IAAInM,MAAJ,CAAW;MAChCpY,MAAM,EAAE0iB,SAAS,CAACzR,OADc;MAEhCF,UAFgC;MAGhCrR,KAAK,EAAEyf,cAHyB;MAIhCrd,OAJgC;;;MAOhCqP,OAAO,EAAEoS,aAPuB;;MAQhCvO,OAAO,CAAC/U,EAAD;QACL,MAAM+X,aAAa,GAAGD,cAAc,CAACvT,GAAf,CAAmBvE,EAAnB,CAAtB;;QAEA,IAAI,CAAC+X,aAAL,EAAoB;UAClB;;;QAGF,MAAM;UAACwM;YAAe1B,WAAW,CAAC7R,OAAlC;QACA,MAAMvR,KAAK,GAAmB;UAACO;SAA/B;QACAukB,WAAW,QAAX,YAAAA,WAAW,CAAG9kB,KAAH,CAAX;QACAqiB,oBAAoB,CAAC;UAACtiB,IAAI,EAAE,aAAP;UAAsBC;SAAvB,CAApB;OAlB8B;;MAoBhC+U,SAAS,CAACxU,EAAD,EAAKiT,UAAL,EAAiBO,kBAAjB,EAAqCe,MAArC;QACP,MAAMwD,aAAa,GAAGD,cAAc,CAACvT,GAAf,CAAmBvE,EAAnB,CAAtB;;QAEA,IAAI,CAAC+X,aAAL,EAAoB;UAClB;;;QAGF,MAAM;UAACyM;YAAiB3B,WAAW,CAAC7R,OAApC;QACA,MAAMvR,KAAK,GAAqB;UAC9BO,EAD8B;UAE9BiT,UAF8B;UAG9BO,kBAH8B;UAI9Be;SAJF;QAOAiQ,aAAa,QAAb,YAAAA,aAAa,CAAG/kB,KAAH,CAAb;QACAqiB,oBAAoB,CAAC;UAACtiB,IAAI,EAAE,eAAP;UAAwBC;SAAzB,CAApB;OApC8B;;MAsChCsR,OAAO,CAACyC,kBAAD;QACL,MAAMxT,EAAE,GAAGyiB,SAAS,CAACzR,OAArB;;QAEA,IAAIhR,EAAE,IAAI,IAAV,EAAgB;UACd;;;QAGF,MAAM+X,aAAa,GAAGD,cAAc,CAACvT,GAAf,CAAmBvE,EAAnB,CAAtB;;QAEA,IAAI,CAAC+X,aAAL,EAAoB;UAClB;;;QAGF,MAAM;UAACjY;YAAe+iB,WAAW,CAAC7R,OAAlC;QACA,MAAMvR,KAAK,GAAmB;UAC5Byf,cAD4B;UAE5Bnf,MAAM,EAAE;YAACC,EAAD;YAAKsD,IAAI,EAAEyU,aAAa,CAACzU,IAAzB;YAA+BT,IAAI,EAAEyf;;SAF/C;QAKAmC,uBAAuB,CAAC;UACtB3kB,WAAW,QAAX,YAAAA,WAAW,CAAGL,KAAH,CAAX;UACAwiB,SAAS,CAACX,MAAM,CAACoD,YAAR,CAAT;UACAnlB,QAAQ,CAAC;YACPC,IAAI,EAAEiC,MAAM,CAACyS,SADN;YAEPV,kBAFO;YAGPzT,MAAM,EAAEC;WAHF,CAAR;UAKA8hB,oBAAoB,CAAC;YAACtiB,IAAI,EAAE,aAAP;YAAsBC;WAAvB,CAApB;UACAkjB,eAAe,CAACyB,eAAe,CAACpT,OAAjB,CAAf;UACA4R,iBAAiB,CAAC1D,cAAD,CAAjB;SAVqB,CAAvB;OAzD8B;;MAsEhC3M,MAAM,CAACD,WAAD;QACJ/S,QAAQ,CAAC;UACPC,IAAI,EAAEiC,MAAM,CAACue,QADN;UAEP1N;SAFM,CAAR;OAvE8B;;MA4EhCE,KAAK,EAAEmS,aAAa,CAACljB,MAAM,CAACwe,OAAR,CA5EY;MA6EhCvN,QAAQ,EAAEiS,aAAa,CAACljB,MAAM,CAACye,UAAR;KA7EF,CAAvB;IAgFAkE,eAAe,CAACpT,OAAhB,GAA0BsT,cAA1B;;IAEA,SAASK,aAAT,CAAuBnlB,IAAvB;MACE,OAAO,eAAe2O,OAAf;QACL,MAAM;UAACpO,MAAD;UAAS6D,UAAT;UAAqB1D,IAArB;UAA2BujB;YAC/BH,aAAa,CAACtS,OADhB;QAEA,IAAIvR,KAAK,GAAwB,IAAjC;;QAEA,IAAIM,MAAM,IAAI0jB,uBAAd,EAAuC;UACrC,MAAM;YAACmB;cAAc/B,WAAW,CAAC7R,OAAjC;UAEAvR,KAAK,GAAG;YACNyf,cADM;YAENnf,MAAM,EAAEA,MAFF;YAGN6D,UAHM;YAIN4K,KAAK,EAAEiV,uBAJD;YAKNvjB;WALF;;UAQA,IAAIV,IAAI,KAAKiC,MAAM,CAACwe,OAAhB,IAA2B,OAAO2E,UAAP,KAAsB,UAArD,EAAiE;YAC/D,MAAMC,YAAY,GAAG,MAAMC,OAAO,CAACC,OAAR,CAAgBH,UAAU,CAACnlB,KAAD,CAA1B,CAA3B;;YAEA,IAAIolB,YAAJ,EAAkB;cAChBrlB,IAAI,GAAGiC,MAAM,CAACye,UAAd;;;;;QAKNuC,SAAS,CAACzR,OAAV,GAAoB,IAApB;QAEAyT,uBAAuB,CAAC;UACtBllB,QAAQ,CAAC;YAACC;WAAF,CAAR;UACAyiB,SAAS,CAACX,MAAM,CAACY,aAAR,CAAT;UACAgC,OAAO,CAAC,IAAD,CAAP;UACAvB,eAAe,CAAC,IAAD,CAAf;UACAC,iBAAiB,CAAC,IAAD,CAAjB;UACAwB,eAAe,CAACpT,OAAhB,GAA0B,IAA1B;UAEA,MAAM9C,SAAS,GACb1O,IAAI,KAAKiC,MAAM,CAACwe,OAAhB,GAA0B,WAA1B,GAAwC,cAD1C;;UAGA,IAAIxgB,KAAJ,EAAW;YACT,MAAM0O,OAAO,GAAG0U,WAAW,CAAC7R,OAAZ,CAAoB9C,SAApB,CAAhB;YAEAC,OAAO,QAAP,YAAAA,OAAO,CAAG1O,KAAH,CAAP;YACAqiB,oBAAoB,CAAC;cAACtiB,IAAI,EAAE0O,SAAP;cAAkBzO;aAAnB,CAApB;;SAfmB,CAAvB;OA3BF;;GApG+B;EAqJnC,CAACqY,cAAD,CArJmC,CAArC;EAwJA,MAAMkN,iCAAiC,GAAG5lB,WAAW,CACnD,CACE+O,OADF,EAEEvM,MAFF;IAIE,OAAO,CAACnC,KAAD,EAAQM,MAAR;MACL,MAAM8S,WAAW,GAAGpT,KAAK,CAACoT,WAA1B;MACA,MAAMoS,mBAAmB,GAAGnN,cAAc,CAACvT,GAAf,CAAmBxE,MAAnB,CAA5B;;MAEA;MAEE0iB,SAAS,CAACzR,OAAV,KAAsB,IAAtB;MAEA,CAACiU,mBAFD;MAIApS,WAAW,CAACqS,MAJZ,IAKArS,WAAW,CAACsS,gBAPd,EAQE;QACA;;;MAGF,MAAMC,iBAAiB,GAAG;QACxBrlB,MAAM,EAAEklB;OADV;MAGA,MAAMI,cAAc,GAAGlX,OAAO,CAC5B1O,KAD4B,EAE5BmC,MAAM,CAACC,OAFqB,EAG5BujB,iBAH4B,CAA9B;;MAMA,IAAIC,cAAc,KAAK,IAAvB,EAA6B;QAC3BxS,WAAW,CAACqS,MAAZ,GAAqB;UACnBI,UAAU,EAAE1jB,MAAM,CAACA;SADrB;QAIA6gB,SAAS,CAACzR,OAAV,GAAoBjR,MAApB;QACAskB,iBAAiB,CAAC5kB,KAAD,EAAQmC,MAAR,CAAjB;;KA/BJ;GALiD,EAwCnD,CAACkW,cAAD,EAAiBuM,iBAAjB,CAxCmD,CAArD;EA2CA,MAAM1R,UAAU,GAAGsF,oBAAoB,CACrClW,OADqC,EAErCijB,iCAFqC,CAAvC;EAKA5H,cAAc,CAACrb,OAAD,CAAd;EAEA0Z,yBAAyB,CAAC;IACxB,IAAI0D,cAAc,IAAI6C,MAAM,KAAKV,MAAM,CAACoD,YAAxC,EAAsD;MACpDzC,SAAS,CAACX,MAAM,CAACc,WAAR,CAAT;;GAFqB,EAItB,CAACjD,cAAD,EAAiB6C,MAAjB,CAJsB,CAAzB;EAMAnjB,SAAS,CACP;IACE,MAAM;MAACqC;QAAc2hB,WAAW,CAAC7R,OAAjC;IACA,MAAM;MAACjR,MAAD;MAASmf,cAAT;MAAyBtb,UAAzB;MAAqC1D;QAAQojB,aAAa,CAACtS,OAAjE;;IAEA,IAAI,CAACjR,MAAD,IAAW,CAACmf,cAAhB,EAAgC;MAC9B;;;IAGF,MAAMzf,KAAK,GAAkB;MAC3BM,MAD2B;MAE3Bmf,cAF2B;MAG3Btb,UAH2B;MAI3B4K,KAAK,EAAE;QACLpM,CAAC,EAAEqhB,uBAAuB,CAACrhB,CADtB;QAELC,CAAC,EAAEohB,uBAAuB,CAACphB;OANF;MAQ3BnC;KARF;IAWAukB,uBAAuB,CAAC;MACtBvjB,UAAU,QAAV,YAAAA,UAAU,CAAGzB,KAAH,CAAV;MACAqiB,oBAAoB,CAAC;QAACtiB,IAAI,EAAE,YAAP;QAAqBC;OAAtB,CAApB;KAFqB,CAAvB;GApBK;EA0BP,CAACgkB,uBAAuB,CAACrhB,CAAzB,EAA4BqhB,uBAAuB,CAACphB,CAApD,CA1BO,CAAT;EA6BAxD,SAAS,CACP;IACE,MAAM;MACJkB,MADI;MAEJmf,cAFI;MAGJtb,UAHI;MAIJQ,mBAJI;MAKJqf;QACEH,aAAa,CAACtS,OANlB;;IAQA,IACE,CAACjR,MAAD,IACA0iB,SAAS,CAACzR,OAAV,IAAqB,IADrB,IAEA,CAACkO,cAFD,IAGA,CAACuE,uBAJH,EAKE;MACA;;;IAGF,MAAM;MAACxjB;QAAc4iB,WAAW,CAAC7R,OAAjC;IACA,MAAMuU,aAAa,GAAGnhB,mBAAmB,CAACG,GAApB,CAAwB0f,MAAxB,CAAtB;IACA,MAAM/jB,IAAI,GACRqlB,aAAa,IAAIA,aAAa,CAAC1iB,IAAd,CAAmBmO,OAApC,GACI;MACEhR,EAAE,EAAEulB,aAAa,CAACvlB,EADpB;MAEE6C,IAAI,EAAE0iB,aAAa,CAAC1iB,IAAd,CAAmBmO,OAF3B;MAGE1N,IAAI,EAAEiiB,aAAa,CAACjiB,IAHtB;MAIEmT,QAAQ,EAAE8O,aAAa,CAAC9O;KAL9B,GAOI,IARN;IASA,MAAMhX,KAAK,GAAkB;MAC3BM,MAD2B;MAE3Bmf,cAF2B;MAG3Btb,UAH2B;MAI3B4K,KAAK,EAAE;QACLpM,CAAC,EAAEqhB,uBAAuB,CAACrhB,CADtB;QAELC,CAAC,EAAEohB,uBAAuB,CAACphB;OANF;MAQ3BnC;KARF;IAWAukB,uBAAuB,CAAC;MACtBP,OAAO,CAAChkB,IAAD,CAAP;MACAD,UAAU,QAAV,YAAAA,UAAU,CAAGR,KAAH,CAAV;MACAqiB,oBAAoB,CAAC;QAACtiB,IAAI,EAAE,YAAP;QAAqBC;OAAtB,CAApB;KAHqB,CAAvB;GAzCK;EAgDP,CAACwkB,MAAD,CAhDO,CAAT;EAmDAxI,yBAAyB,CAAC;IACxB6H,aAAa,CAACtS,OAAd,GAAwB;MACtBkO,cADsB;MAEtBnf,MAFsB;MAGtB+Q,UAHsB;MAItB5M,aAJsB;MAKtBN,UALsB;MAMtBO,cANsB;MAOtB2T,cAPsB;MAQtByL,YARsB;MAStBC,gBATsB;MAUtBpf,mBAVsB;MAWtBlE,IAXsB;MAYtB0M,mBAZsB;MAatB6W;KAbF;IAgBAnB,WAAW,CAACtR,OAAZ,GAAsB;MACpBuR,OAAO,EAAEiB,gBADW;MAEpBhB,UAAU,EAAEte;KAFd;GAjBuB,EAqBtB,CACDnE,MADC,EAED+Q,UAFC,EAGDlN,UAHC,EAIDM,aAJC,EAKD4T,cALC,EAMDyL,YANC,EAODC,gBAPC,EAQDrf,cARC,EASDC,mBATC,EAUDlE,IAVC,EAWD0M,mBAXC,EAYD6W,uBAZC,CArBsB,CAAzB;EAoCA3N,eAAe,CAAC,EACd,GAAGmN,iBADW;IAEdzU,KAAK,EAAEoR,SAFO;IAGd3J,YAAY,EAAE/R,aAHA;IAIdmC,kBAJc;IAKduG,mBALc;IAMd0J;GANa,CAAf;EASA,MAAMkP,aAAa,GAAGvkB,OAAO,CAAC;IAC5B,MAAMiQ,OAAO,GAA4B;MACvCnR,MADuC;MAEvC+Q,UAFuC;MAGvCqO,cAHuC;MAIvCD,cAJuC;MAKvCtb,UALuC;MAMvCwb,iBANuC;MAOvCR,WAPuC;MAQvC9G,cARuC;MASvC1T,mBATuC;MAUvCD,cAVuC;MAWvCjE,IAXuC;MAYvCqZ,0BAZuC;MAavC3M,mBAbuC;MAcvC0J,uBAduC;MAevC+I,sBAfuC;MAgBvCzF,kBAhBuC;MAiBvC8D;KAjBF;IAoBA,OAAOxM,OAAP;GArB2B,EAsB1B,CACDnR,MADC,EAED+Q,UAFC,EAGDqO,cAHC,EAIDD,cAJC,EAKDtb,UALC,EAMDwb,iBANC,EAODR,WAPC,EAQD9G,cARC,EASD1T,mBATC,EAUDD,cAVC,EAWDjE,IAXC,EAYDqZ,0BAZC,EAaD3M,mBAbC,EAcD0J,uBAdC,EAeD+I,sBAfC,EAgBDzF,kBAhBC,EAiBD8D,UAjBC,CAtB0B,CAA7B;EA0CA,MAAM+H,eAAe,GAAGxkB,OAAO,CAAC;IAC9B,MAAMiQ,OAAO,GAA8B;MACzCgO,cADyC;MAEzCvM,UAFyC;MAGzC5S,MAHyC;MAIzCof,cAJyC;MAKzCI,iBAAiB,EAAE;QACjB3f,SAAS,EAAEkjB;OAN4B;MAQzCvjB,QARyC;MASzCuY,cATyC;MAUzC5X,IAVyC;MAWzCqZ;KAXF;IAcA,OAAOrI,OAAP;GAf6B,EAgB5B,CACDgO,cADC,EAEDvM,UAFC,EAGD5S,MAHC,EAIDof,cAJC,EAKD5f,QALC,EAMDujB,sBANC,EAODhL,cAPC,EAQD5X,IARC,EASDqZ,0BATC,CAhB4B,CAA/B;EA4BA,OACEnY,mBAAA,CAAC7C,iBAAiB,CAACmnB,QAAnB;IAA4BpkB,KAAK,EAAEygB;GAAnC,EACE3gB,mBAAA,CAACoe,eAAe,CAACkG,QAAjB;IAA0BpkB,KAAK,EAAEmkB;GAAjC,EACErkB,mBAAA,CAACqe,aAAa,CAACiG,QAAf;IAAwBpkB,KAAK,EAAEkkB;GAA/B,EACEpkB,mBAAA,CAACigB,sBAAsB,CAACqE,QAAxB;IAAiCpkB,KAAK,EAAEiF;GAAxC,EACGwX,QADH,CADF,CADF,EAME3c,mBAAA,CAACkf,YAAD;IAAc7J,QAAQ,EAAE,CAAAgL,aAAa,QAAb,YAAAA,aAAa,CAAEkE,YAAf,MAAgC;GAAxD,CANF,CADF,EASEvkB,mBAAA,CAACf,aAAD,OACMohB;IACJjhB,uBAAuB,EAAEsiB;GAF3B,CATF,CADF;;EAiBA,SAASI,sBAAT;IACE,MAAM0C,8BAA8B,GAClC,CAAAlD,YAAY,QAAZ,YAAAA,YAAY,CAAExS,iBAAd,MAAoC,KADtC;IAEA,MAAM2V,0BAA0B,GAC9B,OAAO3O,UAAP,KAAsB,QAAtB,GACIA,UAAU,CAAChB,OAAX,KAAuB,KAD3B,GAEIgB,UAAU,KAAK,KAHrB;IAIA,MAAMhB,OAAO,GACXiM,aAAa,IACb,CAACyD,8BADD,IAEA,CAACC,0BAHH;;IAKA,IAAI,OAAO3O,UAAP,KAAsB,QAA1B,EAAoC;MAClC,OAAO,EACL,GAAGA,UADE;QAELhB;OAFF;;;IAMF,OAAO;MAACA;KAAR;;AAEH,CAtnB6B,CAAvB;;ACrGP,MAAM4P,WAAW,gBAAGtnB,aAAa,CAAM,IAAN,CAAjC;AAEA,MAAMunB,WAAW,GAAG,QAApB;AAEA,MAAMC,SAAS,GAAG,WAAlB;AAEA,SAAgBC;MAAa;IAC3BjmB,EAD2B;IAE3BsD,IAF2B;IAG3BmT,QAAQ,GAAG,KAHgB;IAI3ByP;;EAEA,MAAMxY,GAAG,GAAG5M,WAAW,CAACklB,SAAD,CAAvB;EACA,MAAM;IACJrT,UADI;IAEJuM,cAFI;IAGJnf,MAHI;IAIJof,cAJI;IAKJI,iBALI;IAMJzH,cANI;IAOJ5X;MACEtB,UAAU,CAAC4gB,eAAD,CARd;EASA,MAAM;IACJ2G,IAAI,GAAGJ,WADH;IAEJK,eAAe,GAAG,WAFd;IAGJC,QAAQ,GAAG;MACTH,UAJE,WAIFA,UAJE,GAIY,EAJlB;EAKA,MAAMI,UAAU,GAAG,CAAAvmB,MAAM,QAAN,YAAAA,MAAM,CAAEC,EAAR,MAAeA,EAAlC;EACA,MAAMuG,SAAS,GAAqB3H,UAAU,CAC5C0nB,UAAU,GAAGjF,sBAAH,GAA4ByE,WADM,CAA9C;EAGA,MAAM,CAACjd,IAAD,EAAO0d,UAAP,IAAqBlI,UAAU,EAArC;EACA,MAAM,CAACtL,aAAD,EAAgByT,mBAAhB,IAAuCnI,UAAU,EAAvD;EACA,MAAMpf,SAAS,GAAGqe,qBAAqB,CAAC3K,UAAD,EAAa3S,EAAb,CAAvC;EACA,MAAMymB,OAAO,GAAGnN,cAAc,CAAChW,IAAD,CAA9B;EAEAmY,yBAAyB,CACvB;IACE3D,cAAc,CAAC6B,GAAf,CAAmB3Z,EAAnB,EAAuB;MAACA,EAAD;MAAK0N,GAAL;MAAU7E,IAAV;MAAgBkK,aAAhB;MAA+BzP,IAAI,EAAEmjB;KAA5D;IAEA,OAAO;MACL,MAAM5d,IAAI,GAAGiP,cAAc,CAACvT,GAAf,CAAmBvE,EAAnB,CAAb;;MAEA,IAAI6I,IAAI,IAAIA,IAAI,CAAC6E,GAAL,KAAaA,GAAzB,EAA8B;QAC5BoK,cAAc,CAACxY,MAAf,CAAsBU,EAAtB;;KAJJ;GAJqB;EAavB,CAAC8X,cAAD,EAAiB9X,EAAjB,CAbuB,CAAzB;EAgBA,MAAM0mB,kBAAkB,GAAwBzlB,OAAO,CACrD,OAAO;IACLklB,IADK;IAELE,QAFK;IAGL,iBAAiB5P,QAHZ;IAIL,gBAAgB6P,UAAU,IAAIH,IAAI,KAAKJ,WAAvB,GAAqC,IAArC,GAA4ChW,SAJvD;IAKL,wBAAwBqW,eALnB;IAML,oBAAoB7G,iBAAiB,CAAC3f;GANxC,CADqD,EASrD,CACE6W,QADF,EAEE0P,IAFF,EAGEE,QAHF,EAIEC,UAJF,EAKEF,eALF,EAME7G,iBAAiB,CAAC3f,SANpB,CATqD,CAAvD;EAmBA,OAAO;IACLG,MADK;IAELmf,cAFK;IAGLC,cAHK;IAIL+G,UAAU,EAAEQ,kBAJP;IAKLJ,UALK;IAMLrnB,SAAS,EAAEwX,QAAQ,GAAG1G,SAAH,GAAe9Q,SAN7B;IAOL4J,IAPK;IAQL3I,IARK;IASLqmB,UATK;IAULC,mBAVK;IAWLjgB;GAXF;AAaD;;SCrHeogB;EACd,OAAO/nB,UAAU,CAAC6gB,aAAD,CAAjB;AACD;;ACsBD,MAAMuG,WAAS,GAAG,WAAlB;AAEA,MAAMY,2BAA2B,GAAG;EAClCC,OAAO,EAAE;AADyB,CAApC;AAIA,SAAgBC;MAAa;IAC3BxjB,IAD2B;IAE3BmT,QAAQ,GAAG,KAFgB;IAG3BzW,EAH2B;IAI3B+mB;;EAEA,MAAMrZ,GAAG,GAAG5M,WAAW,CAACklB,WAAD,CAAvB;EACA,MAAM;IAACjmB,MAAD;IAASR,QAAT;IAAmBW,IAAnB;IAAyBqZ;MAC7B3a,UAAU,CAAC4gB,eAAD,CADZ;EAEA,MAAMwH,QAAQ,GAAGlQ,MAAM,CAAC;IAACL;GAAF,CAAvB;EACA,MAAMwQ,uBAAuB,GAAGnQ,MAAM,CAAC,KAAD,CAAtC;EACA,MAAMjU,IAAI,GAAGiU,MAAM,CAAoB,IAApB,CAAnB;EACA,MAAMoQ,UAAU,GAAGpQ,MAAM,CAAwB,IAAxB,CAAzB;EACA,MAAM;IACJL,QAAQ,EAAE0Q,sBADN;IAEJC,qBAFI;IAGJP,OAAO,EAAEQ;MACP,EACF,GAAGT,2BADD;IAEF,GAAGG;GANL;EAQA,MAAMvN,GAAG,GAAGF,cAAc,CAAC8N,qBAAD,WAACA,qBAAD,GAA0BpnB,EAA1B,CAA1B;EACA,MAAM0a,YAAY,GAAGtb,WAAW,CAC9B;IACE,IAAI,CAAC6nB,uBAAuB,CAACjW,OAA7B,EAAsC;;;MAGpCiW,uBAAuB,CAACjW,OAAxB,GAAkC,IAAlC;MACA;;;IAGF,IAAIkW,UAAU,CAAClW,OAAX,IAAsB,IAA1B,EAAgC;MAC9BsD,YAAY,CAAC4S,UAAU,CAAClW,OAAZ,CAAZ;;;IAGFkW,UAAU,CAAClW,OAAX,GAAqBJ,UAAU,CAAC;MAC9B2I,0BAA0B,CACxBsD,KAAK,CAACyK,OAAN,CAAc9N,GAAG,CAACxI,OAAlB,IAA6BwI,GAAG,CAACxI,OAAjC,GAA2C,CAACwI,GAAG,CAACxI,OAAL,CADnB,CAA1B;MAGAkW,UAAU,CAAClW,OAAX,GAAqB,IAArB;KAJ6B,EAK5BqW,qBAL4B,CAA/B;GAb4B;EAqB9B,CAACA,qBAAD,CArB8B,CAAhC;EAuBA,MAAM1M,cAAc,GAAGF,iBAAiB,CAAC;IACvCN,QAAQ,EAAEO,YAD6B;IAEvCjE,QAAQ,EAAE0Q,sBAAsB,IAAI,CAACpnB;GAFC,CAAxC;EAIA,MAAMme,gBAAgB,GAAG9e,WAAW,CAClC,CAACmoB,UAAD,EAAiCC,eAAjC;IACE,IAAI,CAAC7M,cAAL,EAAqB;MACnB;;;IAGF,IAAI6M,eAAJ,EAAqB;MACnB7M,cAAc,CAAC8M,SAAf,CAAyBD,eAAzB;MACAP,uBAAuB,CAACjW,OAAxB,GAAkC,KAAlC;;;IAGF,IAAIuW,UAAJ,EAAgB;MACd5M,cAAc,CAACe,OAAf,CAAuB6L,UAAvB;;GAZ8B,EAelC,CAAC5M,cAAD,CAfkC,CAApC;EAiBA,MAAM,CAACwD,OAAD,EAAUoI,UAAV,IAAwBlI,UAAU,CAACH,gBAAD,CAAxC;EACA,MAAMuI,OAAO,GAAGnN,cAAc,CAAChW,IAAD,CAA9B;EAEAzE,SAAS,CAAC;IACR,IAAI,CAAC8b,cAAD,IAAmB,CAACwD,OAAO,CAACnN,OAAhC,EAAyC;MACvC;;;IAGF2J,cAAc,CAACH,UAAf;IACAyM,uBAAuB,CAACjW,OAAxB,GAAkC,KAAlC;IACA2J,cAAc,CAACe,OAAf,CAAuByC,OAAO,CAACnN,OAA/B;GAPO,EAQN,CAACmN,OAAD,EAAUxD,cAAV,CARM,CAAT;EAUA9b,SAAS,CACP;IACEU,QAAQ,CAAC;MACPC,IAAI,EAAEiC,MAAM,CAAC0e,iBADN;MAEP/X,OAAO,EAAE;QACPpI,EADO;QAEP0N,GAFO;QAGP+I,QAHO;QAIP5N,IAAI,EAAEsV,OAJC;QAKPtb,IALO;QAMPS,IAAI,EAAEmjB;;KARF,CAAR;IAYA,OAAO,MACLlnB,QAAQ,CAAC;MACPC,IAAI,EAAEiC,MAAM,CAAC4e,mBADN;MAEP3S,GAFO;MAGP1N;KAHM,CADV;GAdK;EAsBP,CAACA,EAAD,CAtBO,CAAT;EAyBAnB,SAAS,CAAC;IACR,IAAI4X,QAAQ,KAAKuQ,QAAQ,CAAChW,OAAT,CAAiByF,QAAlC,EAA4C;MAC1ClX,QAAQ,CAAC;QACPC,IAAI,EAAEiC,MAAM,CAAC2e,oBADN;QAEPpgB,EAFO;QAGP0N,GAHO;QAIP+I;OAJM,CAAR;MAOAuQ,QAAQ,CAAChW,OAAT,CAAiByF,QAAjB,GAA4BA,QAA5B;;GATK,EAWN,CAACzW,EAAD,EAAK0N,GAAL,EAAU+I,QAAV,EAAoBlX,QAApB,CAXM,CAAT;EAaA,OAAO;IACLQ,MADK;IAEL8C,IAFK;IAGL6kB,MAAM,EAAE,CAAAxnB,IAAI,QAAJ,YAAAA,IAAI,CAAEF,EAAN,MAAaA,EAHhB;IAIL6I,IAAI,EAAEsV,OAJD;IAKLje,IALK;IAMLqmB;GANF;AAQD;;SC/IeoB;MAAiB;IAACC,SAAD;IAAY7J;;EAC3C,MAAM,CACJ8J,cADI,EAEJC,iBAFI,IAGF5oB,QAAQ,CAA4B,IAA5B,CAHZ;EAIA,MAAM,CAACkJ,OAAD,EAAU2f,UAAV,IAAwB7oB,QAAQ,CAAqB,IAArB,CAAtC;EACA,MAAM8oB,gBAAgB,GAAGvQ,WAAW,CAACsG,QAAD,CAApC;;EAEA,IAAI,CAACA,QAAD,IAAa,CAAC8J,cAAd,IAAgCG,gBAApC,EAAsD;IACpDF,iBAAiB,CAACE,gBAAD,CAAjB;;;EAGFvM,yBAAyB,CAAC;IACxB,IAAI,CAACrT,OAAL,EAAc;MACZ;;;IAGF,MAAMsF,GAAG,GAAGma,cAAH,oBAAGA,cAAc,CAAEna,GAA5B;IACA,MAAM1N,EAAE,GAAG6nB,cAAH,oBAAGA,cAAc,CAAE5X,KAAhB,CAAsBjQ,EAAjC;;IAEA,IAAI0N,GAAG,IAAI,IAAP,IAAe1N,EAAE,IAAI,IAAzB,EAA+B;MAC7B8nB,iBAAiB,CAAC,IAAD,CAAjB;MACA;;;IAGFhD,OAAO,CAACC,OAAR,CAAgB6C,SAAS,CAAC5nB,EAAD,EAAKoI,OAAL,CAAzB,EAAwC6f,IAAxC,CAA6C;MAC3CH,iBAAiB,CAAC,IAAD,CAAjB;KADF;GAbuB,EAgBtB,CAACF,SAAD,EAAYC,cAAZ,EAA4Bzf,OAA5B,CAhBsB,CAAzB;EAkBA,OACEhH,mBAAA,eAAA,MAAA,EACG2c,QADH,EAEG8J,cAAc,GAAGK,YAAY,CAACL,cAAD,EAAiB;IAACM,GAAG,EAAEJ;GAAvB,CAAf,GAAqD,IAFtE,CADF;AAMD;;ACzCD,MAAMK,gBAAgB,GAAc;EAClChmB,CAAC,EAAE,CAD+B;EAElCC,CAAC,EAAE,CAF+B;EAGlCqE,MAAM,EAAE,CAH0B;EAIlCC,MAAM,EAAE;AAJ0B,CAApC;AAOA,SAAgB0hB;MAAyB;IAACtK;;EACxC,OACE3c,mBAAA,CAACoe,eAAe,CAACkG,QAAjB;IAA0BpkB,KAAK,EAAEge;GAAjC,EACEle,mBAAA,CAACigB,sBAAsB,CAACqE,QAAxB;IAAiCpkB,KAAK,EAAE8mB;GAAxC,EACGrK,QADH,CADF,CADF;AAOD;;ACAD,MAAMuK,UAAU,GAAwB;EACtCvf,QAAQ,EAAE,OAD4B;EAEtCwf,WAAW,EAAE;AAFyB,CAAxC;;AAKA,MAAMC,iBAAiB,GAAsBtJ,cAAD;EAC1C,MAAMuJ,mBAAmB,GAAGxX,eAAe,CAACiO,cAAD,CAA3C;EAEA,OAAOuJ,mBAAmB,GAAG,sBAAH,GAA4B1Y,SAAtD;AACD,CAJD;;AAMA,AAAO,MAAM2Y,iBAAiB,gBAAGC,UAAU,CACzC,OAYER,GAZF;MACE;IACES,EADF;IAEE1J,cAFF;IAGE5Y,WAHF;IAIEyX,QAJF;IAKE8K,SALF;IAMEhmB,IANF;IAOEimB,KAPF;IAQEviB,SARF;IASEwiB,UAAU,GAAGP;;;EAIf,IAAI,CAAC3lB,IAAL,EAAW;IACT,OAAO,IAAP;;;EAGF,MAAMmmB,sBAAsB,GAAG1iB,WAAW,GACtCC,SADsC,GAEtC,EACE,GAAGA,SADL;IAEEG,MAAM,EAAE,CAFV;IAGEC,MAAM,EAAE;GALd;EAOA,MAAMsiB,MAAM,GAAoC,EAC9C,GAAGX,UAD2C;IAE9CplB,KAAK,EAAEL,IAAI,CAACK,KAFkC;IAG9CE,MAAM,EAAEP,IAAI,CAACO,MAHiC;IAI9CD,GAAG,EAAEN,IAAI,CAACM,GAJoC;IAK9CF,IAAI,EAAEJ,IAAI,CAACI,IALmC;IAM9CsD,SAAS,EAAE2iB,GAAG,CAACC,SAAJ,CAAcC,QAAd,CAAuBJ,sBAAvB,CANmC;IAO9ChmB,eAAe,EACbsD,WAAW,IAAI4Y,cAAf,GACItc,0BAA0B,CACxBsc,cADwB,EAExBrc,IAFwB,CAD9B,GAKIkN,SAbwC;IAc9CgZ,UAAU,EACR,OAAOA,UAAP,KAAsB,UAAtB,GACIA,UAAU,CAAC7J,cAAD,CADd,GAEI6J,UAjBwC;IAkB9C,GAAGD;GAlBL;EAqBA,OAAO1nB,KAAK,CAACioB,aAAN,CACLT,EADK,EAEL;IACEC,SADF;IAEEC,KAAK,EAAEG,MAFT;IAGEd;GALG,EAOLpK,QAPK,CAAP;AASD,CAxDwC,CAApC;;MCwDMuL,+BAA+B,GAC1CznB,OAD6C,IAEhB;MAAC;IAAC9B,MAAD;IAAS6e;;EACvC,MAAM2K,cAAc,GAA2B,EAA/C;EACA,MAAM;IAACN,MAAD;IAASJ;MAAahnB,OAA5B;;EAEA,IAAIonB,MAAJ,YAAIA,MAAM,CAAElpB,MAAZ,EAAoB;IAClB,KAAK,MAAM,CAAC2N,GAAD,EAAMpM,KAAN,CAAX,IAA2BY,MAAM,CAACya,OAAP,CAAesM,MAAM,CAAClpB,MAAtB,CAA3B,EAA0D;MACxD,IAAIuB,KAAK,KAAKyO,SAAd,EAAyB;QACvB;;;MAGFwZ,cAAc,CAAC7b,GAAD,CAAd,GAAsB3N,MAAM,CAAC8I,IAAP,CAAYigB,KAAZ,CAAkBU,gBAAlB,CAAmC9b,GAAnC,CAAtB;MACA3N,MAAM,CAAC8I,IAAP,CAAYigB,KAAZ,CAAkBW,WAAlB,CAA8B/b,GAA9B,EAAmCpM,KAAnC;;;;EAIJ,IAAI2nB,MAAJ,YAAIA,MAAM,CAAErK,WAAZ,EAAyB;IACvB,KAAK,MAAM,CAAClR,GAAD,EAAMpM,KAAN,CAAX,IAA2BY,MAAM,CAACya,OAAP,CAAesM,MAAM,CAACrK,WAAtB,CAA3B,EAA+D;MAC7D,IAAItd,KAAK,KAAKyO,SAAd,EAAyB;QACvB;;;MAGF6O,WAAW,CAAC/V,IAAZ,CAAiBigB,KAAjB,CAAuBW,WAAvB,CAAmC/b,GAAnC,EAAwCpM,KAAxC;;;;EAIJ,IAAIunB,SAAJ,YAAIA,SAAS,CAAE9oB,MAAf,EAAuB;IACrBA,MAAM,CAAC8I,IAAP,CAAY6gB,SAAZ,CAAsBrqB,GAAtB,CAA0BwpB,SAAS,CAAC9oB,MAApC;;;EAGF,IAAI8oB,SAAJ,YAAIA,SAAS,CAAEjK,WAAf,EAA4B;IAC1BA,WAAW,CAAC/V,IAAZ,CAAiB6gB,SAAjB,CAA2BrqB,GAA3B,CAA+BwpB,SAAS,CAACjK,WAAzC;;;EAGF,OAAO,SAASlC,OAAT;IACL,KAAK,MAAM,CAAChP,GAAD,EAAMpM,KAAN,CAAX,IAA2BY,MAAM,CAACya,OAAP,CAAe4M,cAAf,CAA3B,EAA2D;MACzDxpB,MAAM,CAAC8I,IAAP,CAAYigB,KAAZ,CAAkBW,WAAlB,CAA8B/b,GAA9B,EAAmCpM,KAAnC;;;IAGF,IAAIunB,SAAJ,YAAIA,SAAS,CAAE9oB,MAAf,EAAuB;MACrBA,MAAM,CAAC8I,IAAP,CAAY6gB,SAAZ,CAAsBC,MAAtB,CAA6Bd,SAAS,CAAC9oB,MAAvC;;GANJ;AASD,CA5CM;;AA8CP,MAAM6pB,uBAAuB,GAAqB;EAAA,IAAC;IACjDrjB,SAAS,EAAE;MAACgc,OAAD;MAAUsH;;GAD2B;EAAA,OAE5C,CACJ;IACEtjB,SAAS,EAAE2iB,GAAG,CAACC,SAAJ,CAAcC,QAAd,CAAuB7G,OAAvB;GAFT,EAIJ;IACEhc,SAAS,EAAE2iB,GAAG,CAACC,SAAJ,CAAcC,QAAd,CAAuBS,KAAvB;GALT,CAF4C;AAAA,CAAlD;;AAWA,MAAaC,iCAAiC,GAAmC;EAC/EC,QAAQ,EAAE,GADqE;EAE/EC,MAAM,EAAE,MAFuE;EAG/EC,SAAS,EAAEL,uBAHoE;EAI/EM,WAAW,eAAEZ,+BAA+B,CAAC;IAC3CL,MAAM,EAAE;MACNlpB,MAAM,EAAE;QACNoqB,OAAO,EAAE;;;GAH6B;AAJmC,CAA1E;AAaP,SAAgBC;MAAiB;IAC/BtR,MAD+B;IAE/BhB,cAF+B;IAG/B1T,mBAH+B;IAI/Bib;;EAEA,OAAOhF,QAAQ,CAAY,CAACra,EAAD,EAAK6I,IAAL;IACzB,IAAIiQ,MAAM,KAAK,IAAf,EAAqB;MACnB;;;IAGF,MAAMuR,eAAe,GAA8BvS,cAAc,CAACvT,GAAf,CAAmBvE,EAAnB,CAAnD;;IAEA,IAAI,CAACqqB,eAAL,EAAsB;MACpB;;;IAGF,MAAMvZ,UAAU,GAAGuZ,eAAe,CAACxhB,IAAhB,CAAqBmI,OAAxC;;IAEA,IAAI,CAACF,UAAL,EAAiB;MACf;;;IAGF,MAAMwZ,cAAc,GAAGxM,iBAAiB,CAACjV,IAAD,CAAxC;;IAEA,IAAI,CAACyhB,cAAL,EAAqB;MACnB;;;IAEF,MAAM;MAAC/jB;QAAa+B,SAAS,CAACO,IAAD,CAAT,CAAgBN,gBAAhB,CAAiCM,IAAjC,CAApB;IACA,MAAMnB,eAAe,GAAGN,cAAc,CAACb,SAAD,CAAtC;;IAEA,IAAI,CAACmB,eAAL,EAAsB;MACpB;;;IAGF,MAAMkgB,SAAS,GACb,OAAO9O,MAAP,KAAkB,UAAlB,GACIA,MADJ,GAEIyR,0BAA0B,CAACzR,MAAD,CAHhC;IAKA/L,sBAAsB,CACpB+D,UADoB,EAEpBuO,sBAAsB,CAACzf,SAAvB,CAAiCoN,OAFb,CAAtB;IAKA,OAAO4a,SAAS,CAAC;MACf7nB,MAAM,EAAE;QACNC,EADM;QAENsD,IAAI,EAAE+mB,eAAe,CAAC/mB,IAFhB;QAGNuF,IAAI,EAAEiI,UAHA;QAINjO,IAAI,EAAEwc,sBAAsB,CAACzf,SAAvB,CAAiCoN,OAAjC,CAAyC8D,UAAzC;OALO;MAOfgH,cAPe;MAQf8G,WAAW,EAAE;QACX/V,IADW;QAEXhG,IAAI,EAAEwc,sBAAsB,CAACT,WAAvB,CAAmC5R,OAAnC,CAA2Csd,cAA3C;OAVO;MAYflmB,mBAZe;MAafib,sBAbe;MAcf9Y,SAAS,EAAEmB;KAdG,CAAhB;GAvCa,CAAf;AAwDD;;AAED,SAAS6iB,0BAAT,CACE1oB,OADF;EAGE,MAAM;IAACkoB,QAAD;IAAWC,MAAX;IAAmBE,WAAnB;IAAgCD;MAAa,EACjD,GAAGH,iCAD8C;IAEjD,GAAGjoB;GAFL;EAKA,OAAO;QAAC;MAAC9B,MAAD;MAAS6e,WAAT;MAAsBrY,SAAtB;MAAiC,GAAGikB;;;IAC1C,IAAI,CAACT,QAAL,EAAe;;MAEb;;;IAGF,MAAMvb,KAAK,GAAG;MACZpM,CAAC,EAAEwc,WAAW,CAAC/b,IAAZ,CAAiBI,IAAjB,GAAwBlD,MAAM,CAAC8C,IAAP,CAAYI,IAD3B;MAEZZ,CAAC,EAAEuc,WAAW,CAAC/b,IAAZ,CAAiBM,GAAjB,GAAuBpD,MAAM,CAAC8C,IAAP,CAAYM;KAFxC;IAKA,MAAMsnB,KAAK,GAAG;MACZ/jB,MAAM,EACJH,SAAS,CAACG,MAAV,KAAqB,CAArB,GACK3G,MAAM,CAAC8C,IAAP,CAAYK,KAAZ,GAAoBqD,SAAS,CAACG,MAA/B,GAAyCkY,WAAW,CAAC/b,IAAZ,CAAiBK,KAD9D,GAEI,CAJM;MAKZyD,MAAM,EACJJ,SAAS,CAACI,MAAV,KAAqB,CAArB,GACK5G,MAAM,CAAC8C,IAAP,CAAYO,MAAZ,GAAqBmD,SAAS,CAACI,MAAhC,GAA0CiY,WAAW,CAAC/b,IAAZ,CAAiBO,MAD/D,GAEI;KARR;IAUA,MAAMsnB,cAAc,GAAG;MACrBtoB,CAAC,EAAEmE,SAAS,CAACnE,CAAV,GAAcoM,KAAK,CAACpM,CADF;MAErBC,CAAC,EAAEkE,SAAS,CAAClE,CAAV,GAAcmM,KAAK,CAACnM,CAFF;MAGrB,GAAGooB;KAHL;IAMA,MAAME,kBAAkB,GAAGV,SAAS,CAAC,EACnC,GAAGO,IADgC;MAEnCzqB,MAFmC;MAGnC6e,WAHmC;MAInCrY,SAAS,EAAE;QAACgc,OAAO,EAAEhc,SAAV;QAAqBsjB,KAAK,EAAEa;;KAJL,CAApC;IAOA,MAAM,CAACE,aAAD,IAAkBD,kBAAxB;IACA,MAAME,YAAY,GAAGF,kBAAkB,CAACA,kBAAkB,CAAC7mB,MAAnB,GAA4B,CAA7B,CAAvC;;IAEA,IAAIuT,IAAI,CAACC,SAAL,CAAesT,aAAf,MAAkCvT,IAAI,CAACC,SAAL,CAAeuT,YAAf,CAAtC,EAAoE;;MAElE;;;IAGF,MAAMnO,OAAO,GAAGwN,WAAH,oBAAGA,WAAW,CAAG;MAACnqB,MAAD;MAAS6e,WAAT;MAAsB,GAAG4L;KAA5B,CAA3B;IACA,MAAM5C,SAAS,GAAGhJ,WAAW,CAAC/V,IAAZ,CAAiBiiB,OAAjB,CAAyBH,kBAAzB,EAA6C;MAC7DZ,QAD6D;MAE7DC,MAF6D;MAG7De,IAAI,EAAE;KAHU,CAAlB;IAMA,OAAO,IAAIjG,OAAJ,CAAaC,OAAD;MACjB6C,SAAS,CAACoD,QAAV,GAAqB;QACnBtO,OAAO,QAAP,YAAAA,OAAO;QACPqI,OAAO;OAFT;KADK,CAAP;GAjDF;AAwDD;;AC9RD,IAAIrX,GAAG,GAAG,CAAV;AAEA,SAAgBud,OAAOjrB;EACrB,OAAOiB,OAAO,CAAC;IACb,IAAIjB,EAAE,IAAI,IAAV,EAAgB;MACd;;;IAGF0N,GAAG;IACH,OAAOA,GAAP;GANY,EAOX,CAAC1N,EAAD,CAPW,CAAd;AAQD;;MCaYkrB,WAAW,gBAAG9pB,KAAK,CAACogB,IAAN,CACzB;MAAC;IACClb,WAAW,GAAG,KADf;IAECyX,QAFD;IAGCoN,aAAa,EAAEC,mBAHhB;IAICtC,KAJD;IAKCC,UALD;IAMChI,SAND;IAOCsK,cAAc,GAAG,KAPlB;IAQCxC,SARD;IASCyC,MAAM,GAAG;;EAET,MAAM;IACJpM,cADI;IAEJnf,MAFI;IAGJof,cAHI;IAIJC,iBAJI;IAKJtH,cALI;IAMJ1T,mBANI;IAOJwa,WAPI;IAQJ1e,IARI;IASJmf,sBATI;IAUJzS,mBAVI;IAWJ0J,uBAXI;IAYJoH;MACEiJ,aAAa,EAbjB;EAcA,MAAMpgB,SAAS,GAAG3H,UAAU,CAACyiB,sBAAD,CAA5B;EACA,MAAM3T,GAAG,GAAGud,MAAM,CAAClrB,MAAD,oBAACA,MAAM,CAAEC,EAAT,CAAlB;EACA,MAAMurB,iBAAiB,GAAGzK,cAAc,CAACC,SAAD,EAAY;IAClD7B,cADkD;IAElDnf,MAFkD;IAGlDof,cAHkD;IAIlDC,iBAJkD;IAKlDoE,gBAAgB,EAAE5E,WAAW,CAAC/b,IALoB;IAMlD3C,IANkD;IAOlD4jB,eAAe,EAAElF,WAAW,CAAC/b,IAPqB;IAQlD+J,mBARkD;IASlD0J,uBATkD;IAUlD/P,SAVkD;IAWlDmX;GAXsC,CAAxC;EAaA,MAAM3B,WAAW,GAAGhC,eAAe,CAACoF,cAAD,CAAnC;EACA,MAAMgM,aAAa,GAAGf,gBAAgB,CAAC;IACrCtR,MAAM,EAAEsS,mBAD6B;IAErCtT,cAFqC;IAGrC1T,mBAHqC;IAIrCib;GAJoC,CAAtC;;;EAQA,MAAM8I,GAAG,GAAGpM,WAAW,GAAG6C,WAAW,CAACR,MAAf,GAAwBrO,SAA/C;EAEA,OACE3O,mBAAA,CAACinB,wBAAD,MAAA,EACEjnB,mBAAA,CAACumB,gBAAD;IAAkBC,SAAS,EAAEuD;GAA7B,EACGprB,MAAM,IAAI2N,GAAV,GACCtM,mBAAA,CAACsnB,iBAAD;IACEhb,GAAG,EAAEA;IACL1N,EAAE,EAAED,MAAM,CAACC;IACXmoB,GAAG,EAAEA;IACLS,EAAE,EAAEyC;IACJnM,cAAc,EAAEA;IAChB5Y,WAAW,EAAEA;IACbuiB,SAAS,EAAEA;IACXE,UAAU,EAAEA;IACZlmB,IAAI,EAAEkZ;IACN+M,KAAK,EAAE;MACLwC,MADK;MAEL,GAAGxC;;IAELviB,SAAS,EAAEglB;GAdb,EAgBGxN,QAhBH,CADD,GAmBG,IApBN,CADF,CADF;AA0BD,CA9EwB,CAApB;;;;"}