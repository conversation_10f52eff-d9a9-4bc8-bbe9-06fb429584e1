# NFT Generator Pro V2.1

Professional NFT Generator with advanced features, built with Clean Architecture and modern technologies.

## 🚀 Features

- **Clean Architecture** - Maintainable and scalable code structure
- **Layer Management** - Import, organize, and manage NFT layers with drag & drop
- **Trait Management** - Advanced rarity control and distribution algorithms
- **Rules Engine** - Complex IF-THEN logic for trait combinations
- **Preview System** - Real-time preview with zoom/pan controls
- **Content Analysis** - Smart import suggestions and relationship detection
- **Batch Generation** - Generate thousands of unique NFTs efficiently
- **Export System** - PNG/JPG/WebP export with metadata generation

## 🛠️ Tech Stack

- **Frontend**: React 19 + TypeScript + Vite
- **UI Library**: Material-UI (MUI) v6
- **State Management**: Zustand
- **Storage**: IndexedDB (Dexie) + LocalStorage
- **Canvas**: Fabric.js
- **Testing**: Vitest + Testing Library
- **Code Quality**: ESLint + Prettier + Husky

## 📁 Project Structure

```
src/
├── app/           # Application layer (stores, hooks, services)
├── domain/        # Domain entities, repositories, use cases
├── infrastructure/ # External dependencies (storage, API, utils)
├── presentation/  # UI components, pages, layouts
├── shared/        # Shared utilities, types, constants
└── test/          # Test setup and utilities
```

## 🚀 Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd nft-v2.1

# Install dependencies
npm install

# Start development server
npm run dev

# Run tests
npm run test

# Build for production
npm run build
```

### Development Scripts

```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build
npm run test         # Run tests
npm run test:ui      # Run tests with UI
npm run test:coverage # Run tests with coverage
npm run lint         # Run ESLint
npm run lint:fix     # Fix ESLint errors
npm run type-check   # TypeScript type checking
```

## 🏗️ Architecture

This project follows Clean Architecture principles:

- **Domain Layer**: Business logic, entities, and interfaces
- **Application Layer**: Use cases, stores, and application services
- **Infrastructure Layer**: External dependencies and implementations
- **Presentation Layer**: UI components and user interactions

## 📊 Development Progress

### ✅ Completed (Foundation Phase)
- [x] Project setup with Vite + TypeScript
- [x] Clean Architecture folder structure
- [x] Domain entities (Project, Layer, Trait)
- [x] Repository interfaces
- [x] Basic use cases
- [x] Zustand store setup
- [x] Material-UI theme and layout
- [x] Test framework setup
- [x] Path aliases configuration

### 🚧 In Progress (Core Features Phase)
- [ ] Project CRUD operations
- [ ] Layer import system
- [ ] Trait management UI
- [ ] Preview canvas implementation
- [ ] Rarity calculation engine

### 📋 Planned (Advanced Features Phase)
- [ ] Rules engine implementation
- [ ] Content analysis system
- [ ] Generation algorithm
- [ ] Export system
- [ ] Performance optimization

## 🧪 Testing

The project uses Vitest for testing with comprehensive coverage:

```bash
# Run all tests
npm run test

# Run tests with UI
npm run test:ui

# Generate coverage report
npm run test:coverage
```

Current test coverage: **95%+ for domain entities**

## 📝 Contributing

1. Follow the established Clean Architecture patterns
2. Write tests for new features
3. Use TypeScript strict mode
4. Follow the existing code style (ESLint + Prettier)
5. Update documentation for significant changes

## 📄 License

This project is licensed under the MIT License.

## 🔗 Related Documentation

- [Project Checklist](./PROJECT-CHECKLIST.md) - Detailed development checklist
- [REWRITE-SPECS](../Docs/REWRITE-SPECS/) - Complete rewrite specifications
