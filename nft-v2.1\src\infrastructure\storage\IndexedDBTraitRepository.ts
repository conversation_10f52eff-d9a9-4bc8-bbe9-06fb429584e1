import Dexie, { Table } from 'dexie';
import { Trait } from '@/domain/entities/Trait';
import { ITraitRepository, TraitQuery, TraitListOptions, TraitStats } from '@/domain/repositories/ITraitRepository';

interface TraitRecord {
  id: string;
  layerId: string;
  projectId: string;
  data: any; // Serialized trait data
  createdAt: Date;
  updatedAt: Date;
}

class TraitDatabase extends Dexie {
  traits!: Table<TraitRecord>;

  constructor() {
    super('NFTGeneratorTraits');
    
    this.version(1).stores({
      traits: 'id, layerId, projectId, createdAt, updatedAt',
    });
  }
}

export class IndexedDBTraitRepository implements ITraitRepository {
  private db: TraitDatabase;

  constructor() {
    this.db = new TraitDatabase();
  }

  async create(trait: Trait): Promise<Trait> {
    const record: TraitRecord = {
      id: trait.id,
      layerId: trait.layerId,
      projectId: trait.projectId,
      data: trait.toJSON(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    await this.db.traits.add(record);
    return trait;
  }

  async getById(id: string): Promise<Trait | null> {
    const record = await this.db.traits.get(id);
    if (!record) return null;

    return Trait.fromJSON(record.data);
  }

  async getAll(query?: TraitQuery, options?: TraitListOptions): Promise<Trait[]> {
    let collection = this.db.traits.orderBy('updatedAt');

    // Apply filters
    if (query?.layerId) {
      collection = collection.filter(record => record.layerId === query.layerId);
    }
    if (query?.projectId) {
      collection = collection.filter(record => record.projectId === query.projectId);
    }

    // Apply sorting
    if (options?.sortBy) {
      switch (options.sortBy) {
        case 'name':
          collection = collection.orderBy('data.metadata.name');
          break;
        case 'rarity':
          collection = collection.orderBy('data.rarity.value');
          break;
        case 'importedAt':
          collection = collection.orderBy('createdAt');
          break;
        case 'lastModified':
          collection = collection.orderBy('updatedAt');
          break;
      }

      if (options.sortOrder === 'desc') {
        collection = collection.reverse();
      }
    }

    // Apply pagination
    if (options?.offset) {
      collection = collection.offset(options.offset);
    }
    if (options?.limit) {
      collection = collection.limit(options.limit);
    }

    const records = await collection.toArray();
    let traits = records.map(record => Trait.fromJSON(record.data));

    // Apply additional filters that can't be done at DB level
    if (query?.name) {
      const searchTerm = query.name.toLowerCase();
      traits = traits.filter(trait => 
        trait.metadata.name.toLowerCase().includes(searchTerm)
      );
    }

    if (query?.category) {
      traits = traits.filter(trait => trait.metadata.category === query.category);
    }

    if (query?.tags && query.tags.length > 0) {
      traits = traits.filter(trait => 
        query.tags!.some(tag => trait.metadata.tags.includes(tag))
      );
    }

    if (query?.isEnabled !== undefined) {
      traits = traits.filter(trait => trait.isEnabled === query.isEnabled);
    }

    if (query?.rarityMin !== undefined) {
      traits = traits.filter(trait => trait.rarity.value >= query.rarityMin!);
    }

    if (query?.rarityMax !== undefined) {
      traits = traits.filter(trait => trait.rarity.value <= query.rarityMax!);
    }

    return traits;
  }

  async getByLayerId(layerId: string, options?: TraitListOptions): Promise<Trait[]> {
    return this.getAll({ layerId }, options);
  }

  async getByProjectId(projectId: string, options?: TraitListOptions): Promise<Trait[]> {
    return this.getAll({ projectId }, options);
  }

  async update(trait: Trait): Promise<Trait> {
    const record: Partial<TraitRecord> = {
      data: trait.toJSON(),
      updatedAt: new Date(),
    };

    await this.db.traits.update(trait.id, record);
    return trait;
  }

  async delete(id: string): Promise<boolean> {
    const deleteCount = await this.db.traits.delete(id);
    return deleteCount > 0;
  }

  async deleteByLayerId(layerId: string): Promise<number> {
    return await this.db.traits.where('layerId').equals(layerId).delete();
  }

  async deleteByProjectId(projectId: string): Promise<number> {
    return await this.db.traits.where('projectId').equals(projectId).delete();
  }

  async exists(id: string): Promise<boolean> {
    const record = await this.db.traits.get(id);
    return !!record;
  }

  async count(query?: TraitQuery): Promise<number> {
    const traits = await this.getAll(query);
    return traits.length;
  }

  async duplicate(id: string, newLayerId?: string): Promise<Trait> {
    const originalTrait = await this.getById(id);
    if (!originalTrait) {
      throw new Error('Trait not found');
    }

    const duplicatedTrait = originalTrait.clone(newLayerId);
    return await this.create(duplicatedTrait);
  }

  async updateRarity(id: string, rarity: number): Promise<Trait> {
    const trait = await this.getById(id);
    if (!trait) {
      throw new Error('Trait not found');
    }

    trait.setRarityValue(rarity);
    return await this.update(trait);
  }

  async updateEnabled(id: string, isEnabled: boolean): Promise<Trait> {
    const trait = await this.getById(id);
    if (!trait) {
      throw new Error('Trait not found');
    }

    trait.setEnabled(isEnabled);
    return await this.update(trait);
  }

  async batchUpdate(traits: Trait[]): Promise<Trait[]> {
    const updatedTraits: Trait[] = [];

    await this.db.transaction('rw', this.db.traits, async () => {
      for (const trait of traits) {
        await this.update(trait);
        updatedTraits.push(trait);
      }
    });

    return updatedTraits;
  }

  async batchUpdateRarity(layerId: string, rarities: { id: string; rarity: number }[]): Promise<Trait[]> {
    const traits = await this.getByLayerId(layerId);
    const updatedTraits: Trait[] = [];

    await this.db.transaction('rw', this.db.traits, async () => {
      for (const { id, rarity } of rarities) {
        const trait = traits.find(t => t.id === id);
        if (trait) {
          trait.setRarityValue(rarity);
          await this.update(trait);
          updatedTraits.push(trait);
        }
      }
    });

    return updatedTraits;
  }

  async search(query: string, projectId?: string): Promise<Trait[]> {
    const searchQuery: TraitQuery = { projectId };
    const traits = await this.getAll(searchQuery);
    const lowerQuery = query.toLowerCase();

    return traits.filter(trait =>
      trait.metadata.name.toLowerCase().includes(lowerQuery) ||
      trait.metadata.category?.toLowerCase().includes(lowerQuery) ||
      trait.metadata.tags.some(tag => tag.toLowerCase().includes(lowerQuery))
    );
  }

  async getLayerStats(layerId: string): Promise<TraitStats> {
    const traits = await this.getByLayerId(layerId);
    
    return this.calculateStats(traits);
  }

  async getProjectStats(projectId: string): Promise<TraitStats> {
    const traits = await this.getByProjectId(projectId);
    
    return this.calculateStats(traits);
  }

  private calculateStats(traits: Trait[]): TraitStats {
    const enabledTraits = traits.filter(t => t.isEnabled);
    const totalRarity = enabledTraits.reduce((sum, t) => sum + t.rarity.value, 0);
    const averageRarity = enabledTraits.length > 0 ? totalRarity / enabledTraits.length : 0;
    const totalFileSize = traits.reduce((sum, t) => sum + t.file.size, 0);
    
    const categories = [...new Set(traits.map(t => t.metadata.category).filter(Boolean))];
    const tags = [...new Set(traits.flatMap(t => t.metadata.tags))];

    return {
      totalTraits: traits.length,
      enabledTraits: enabledTraits.length,
      averageRarity,
      totalFileSize,
      categories,
      tags,
    };
  }

  async getByCategory(category: string, projectId?: string): Promise<Trait[]> {
    return this.getAll({ category, projectId });
  }

  async getByTags(tags: string[], projectId?: string): Promise<Trait[]> {
    return this.getAll({ tags, projectId });
  }

  async getCategories(projectId: string): Promise<string[]> {
    const traits = await this.getByProjectId(projectId);
    return [...new Set(traits.map(t => t.metadata.category).filter(Boolean))];
  }

  async getTags(projectId: string): Promise<string[]> {
    const traits = await this.getByProjectId(projectId);
    return [...new Set(traits.flatMap(t => t.metadata.tags))];
  }

  async validateRarityDistribution(layerId: string): Promise<{
    isValid: boolean;
    totalRarity: number;
    errors: string[];
  }> {
    const traits = await this.getByLayerId(layerId);
    const enabledTraits = traits.filter(t => t.isEnabled);
    
    const totalRarity = enabledTraits.reduce((sum, t) => sum + t.rarity.value, 0);
    const errors: string[] = [];

    if (Math.abs(totalRarity - 100) > 0.01) {
      errors.push(`Total rarity is ${totalRarity.toFixed(2)}%, should be 100%`);
    }

    if (enabledTraits.some(t => t.rarity.value <= 0)) {
      errors.push('Some traits have zero or negative rarity');
    }

    return {
      isValid: errors.length === 0,
      totalRarity,
      errors,
    };
  }

  async autoDistributeRarity(layerId: string): Promise<Trait[]> {
    const traits = await this.getByLayerId(layerId);
    const enabledTraits = traits.filter(t => t.isEnabled);
    
    if (enabledTraits.length === 0) {
      return traits;
    }

    const rarityPerTrait = 100 / enabledTraits.length;
    
    const updatedTraits: Trait[] = [];
    
    await this.db.transaction('rw', this.db.traits, async () => {
      for (const trait of enabledTraits) {
        if (!trait.rarity.isLocked) {
          trait.setRarityValue(rarityPerTrait);
          await this.update(trait);
          updatedTraits.push(trait);
        }
      }
    });

    return updatedTraits;
  }

  async getRandomTrait(layerId: string): Promise<Trait | null> {
    const traits = await this.getByLayerId(layerId);
    const enabledTraits = traits.filter(t => t.isEnabled);
    
    if (enabledTraits.length === 0) {
      return null;
    }

    // Weighted random selection based on rarity
    const totalWeight = enabledTraits.reduce((sum, t) => sum + t.rarity.weight, 0);
    const random = Math.random() * totalWeight;
    
    let currentWeight = 0;
    for (const trait of enabledTraits) {
      currentWeight += trait.rarity.weight;
      if (random <= currentWeight) {
        return trait;
      }
    }

    // Fallback to last trait
    return enabledTraits[enabledTraits.length - 1];
  }
}

export default IndexedDBTraitRepository;
