import React from 'react';
import {
  Box,
  CssB<PERSON>line,
  ThemeProvider,
  createTheme,
  AppBar,
  Too<PERSON><PERSON>,
  Typography,
  Button,
  IconButton,
} from '@mui/material';
import {
  Settings as SettingsIcon,
  Rule as RuleIcon,
  PlayArrow as GenerateIcon,
} from '@mui/icons-material';

const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
    background: {
      default: '#f5f5f5',
      paper: '#ffffff',
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    h4: {
      fontWeight: 600,
    },
    h6: {
      fontWeight: 500,
    },
  },
  components: {
    MuiAppBar: {
      styleOverrides: {
        root: {
          backgroundColor: '#ffffff',
          color: '#333333',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          borderRadius: 8,
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        },
      },
    },
  },
});

interface AppLayoutProps {
  children: React.ReactNode;
}

export const AppLayout: React.FC<AppLayoutProps> = ({ children }) => {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
        {/* Header */}
        <AppBar position="static" elevation={0}>
          <Toolbar>
            <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
              NFT Generator Pro V2
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
              <IconButton color="inherit" size="small">
                <SettingsIcon />
              </IconButton>
              <IconButton color="inherit" size="small">
                <RuleIcon />
              </IconButton>
              <Button
                variant="contained"
                startIcon={<GenerateIcon />}
                size="small"
                sx={{
                  backgroundColor: 'success.main',
                  '&:hover': { backgroundColor: 'success.dark' }
                }}
              >
                Generate
              </Button>
            </Box>
          </Toolbar>
        </AppBar>

        {/* Main Content */}
        <Box
          component="main"
          sx={{
            flexGrow: 1,
            height: 'calc(100vh - 64px)', // Subtract header height
            overflow: 'hidden',
            backgroundColor: 'background.default',
          }}
        >
          {children}
        </Box>


      </Box>
    </ThemeProvider>
  );
};

export default AppLayout;
