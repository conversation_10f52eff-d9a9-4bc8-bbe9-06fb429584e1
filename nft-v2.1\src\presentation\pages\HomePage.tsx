import React from 'react';
import {
  Box,
  Grid,
  <PERSON>,
  Typo<PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  CardActions,
  Chip,
} from '@mui/material';
import {
  Add as AddIcon,
  Folder as FolderIcon,
  Settings as SettingsIcon,
  PlayArrow as PlayIcon,
} from '@mui/icons-material';
import { useProjectStore } from '../../app/stores/useProjectStore';
import PanelContainer from '../components/layout/PanelContainer';

export const HomePage: React.FC = () => {
  const { projects, currentProject, setCurrentProject, addLayer, addTrait } = useProjectStore();

  const handleCreateProject = () => {
    // Create a clean project
    const newProject = {
      id: `project-${Date.now()}`,
      settings: {
        name: 'New NFT Collection',
        description: 'A new NFT collection',
        outputSize: { width: 1000, height: 1000 },
        outputFormat: 'PNG' as const,
        outputQuality: 100,
        generateMetadata: true,
      },
      stats: {
        totalLayers: 0,
        totalTraits: 0,
        totalCombinations: 0,
        estimatedRarity: 0,
        lastModified: new Date(),
        created: new Date(),
      },
      layerIds: [],
      ruleIds: [],
      isActive: true,
    };

    setCurrentProject(newProject);
  };

  const handleOpenProject = (project: any) => {
    setCurrentProject(project);
  };

  const handleImportProject = () => {
    // TODO: Open import dialog
    console.log('Import project');
  };

  // If there's a current project, show the main workspace
  if (currentProject) {
    return <PanelContainer />;
  }

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      {/* Header Section */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Welcome to NFT Generator Pro V2
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
          Create professional NFT collections with advanced features and clean architecture.
        </Typography>
        
        {/* Quick Actions */}
        <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleCreateProject}
            size="large"
          >
            New Project
          </Button>
          <Button
            variant="outlined"
            startIcon={<FolderIcon />}
            onClick={handleImportProject}
            size="large"
          >
            Import Project
          </Button>
          <Button
            variant="outlined"
            startIcon={<SettingsIcon />}
            size="large"
          >
            Settings
          </Button>
        </Box>
      </Box>

      {/* Current Project Section */}
      {currentProject && (
        <Box sx={{ mb: 4 }}>
          <Typography variant="h5" component="h2" gutterBottom>
            Current Project
          </Typography>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'start', mb: 2 }}>
                <Box>
                  <Typography variant="h6" component="h3">
                    {currentProject.settings.name}
                  </Typography>
                  {currentProject.settings.description && (
                    <Typography variant="body2" color="text.secondary">
                      {currentProject.settings.description}
                    </Typography>
                  )}
                </Box>
                <Chip label="Active" color="primary" size="small" />
              </Box>
              
              <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                <Typography variant="body2">
                  <strong>Layers:</strong> {currentProject.stats.totalLayers}
                </Typography>
                <Typography variant="body2">
                  <strong>Traits:</strong> {currentProject.stats.totalTraits}
                </Typography>
                <Typography variant="body2">
                  <strong>Combinations:</strong> {currentProject.stats.totalCombinations.toLocaleString()}
                </Typography>
              </Box>
              
              <Typography variant="body2" color="text.secondary">
                Last modified: {currentProject.stats.lastModified.toLocaleDateString()}
              </Typography>
            </CardContent>
            <CardActions>
              <Button startIcon={<PlayIcon />} variant="contained">
                Continue Working
              </Button>
              <Button variant="outlined">
                Project Settings
              </Button>
            </CardActions>
          </Card>
        </Box>
      )}

      {/* Recent Projects Section */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h5" component="h2" gutterBottom>
          Recent Projects
        </Typography>
        
        {projects.length === 0 ? (
          <Paper sx={{ p: 4, textAlign: 'center' }}>
            <Typography variant="h6" color="text.secondary" gutterBottom>
              No projects yet
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Create your first NFT project to get started
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleCreateProject}
            >
              Create First Project
            </Button>
          </Paper>
        ) : (
          <Grid container spacing={3}>
            {projects.slice(0, 6).map((project) => (
              <Grid item xs={12} sm={6} md={4} key={project.id}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" component="h3" gutterBottom>
                      {project.settings.name}
                    </Typography>
                    {project.settings.description && (
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                        {project.settings.description}
                      </Typography>
                    )}
                    
                    <Box sx={{ display: 'flex', gap: 1, mb: 2, flexWrap: 'wrap' }}>
                      <Chip label={`${project.stats.totalLayers} layers`} size="small" />
                      <Chip label={`${project.stats.totalTraits} traits`} size="small" />
                    </Box>
                    
                    <Typography variant="body2" color="text.secondary">
                      {project.stats.lastModified.toLocaleDateString()}
                    </Typography>
                  </CardContent>
                  <CardActions>
                    <Button
                      size="small"
                      onClick={() => handleOpenProject(project)}
                    >
                      Open
                    </Button>
                    <Button size="small" color="secondary">
                      Duplicate
                    </Button>
                  </CardActions>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}
      </Box>

      {/* Features Overview */}
      <Box>
        <Typography variant="h5" component="h2" gutterBottom>
          Features
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6} md={3}>
            <Paper sx={{ p: 3, textAlign: 'center' }}>
              <Typography variant="h6" gutterBottom>
                Layer Management
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Import, organize, and manage your NFT layers with drag & drop
              </Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Paper sx={{ p: 3, textAlign: 'center' }}>
              <Typography variant="h6" gutterBottom>
                Rarity Control
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Fine-tune trait rarities with advanced distribution algorithms
              </Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Paper sx={{ p: 3, textAlign: 'center' }}>
              <Typography variant="h6" gutterBottom>
                Rules Engine
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Create complex rules for trait combinations and conflicts
              </Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Paper sx={{ p: 3, textAlign: 'center' }}>
              <Typography variant="h6" gutterBottom>
                Batch Generation
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Generate thousands of unique NFTs with metadata export
              </Typography>
            </Paper>
          </Grid>
        </Grid>
      </Box>
    </Box>
  );
};

export default HomePage;
