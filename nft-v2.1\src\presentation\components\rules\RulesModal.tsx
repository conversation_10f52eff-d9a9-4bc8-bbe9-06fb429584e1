import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>T<PERSON>le,
  <PERSON>alogContent,
  <PERSON>alog<PERSON><PERSON>,
  <PERSON>ton,
  <PERSON>,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  IconButton,
  Chip,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Switch,
  FormControlLabel,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Rule as RuleIcon,
  Link as ChainIcon,
  LinkOff as UnchainIcon,
} from '@mui/icons-material';

export interface Rule {
  id: string;
  name: string;
  description: string;
  isEnabled: boolean;
  conditions: RuleCondition[];
  actions: RuleAction[];
  createdAt: Date;
  lastModified: Date;
}

export interface RuleCondition {
  id: string;
  layerId: string;
  traitId: string;
  operator: 'MUST_HAVE' | 'CANNOT_HAVE';
  isChained?: boolean;
  chainOperator?: 'AND' | 'OR';
}

export interface RuleAction {
  id: string;
  layerId: string;
  traitId: string;
  action: 'FORCE_INCLUDE' | 'FORCE_EXCLUDE' | 'INCREASE_RARITY' | 'DECREASE_RARITY';
  value?: number;
}

interface RulesModalProps {
  open: boolean;
  onClose: () => void;
  rules: Rule[];
  onSaveRule: (rule: Rule) => void;
  onDeleteRule: (ruleId: string) => void;
  layers: any[];
  traits: any[];
}

export const RulesModal: React.FC<RulesModalProps> = ({
  open,
  onClose,
  rules,
  onSaveRule,
  onDeleteRule,
  layers,
  traits,
}) => {
  const [editingRule, setEditingRule] = useState<Rule | null>(null);
  const [isCreating, setIsCreating] = useState(false);

  const createNewRule = (): Rule => ({
    id: `rule-${Date.now()}`,
    name: 'New Rule',
    description: '',
    isEnabled: true,
    conditions: [{
      id: `condition-${Date.now()}`,
      layerId: '',
      traitId: '',
      operator: 'MUST_HAVE',
      isChained: false,
    }],
    actions: [{
      id: `action-${Date.now()}`,
      layerId: '',
      traitId: '',
      action: 'FORCE_INCLUDE',
    }],
    createdAt: new Date(),
    lastModified: new Date(),
  });

  const handleCreateRule = () => {
    const newRule = createNewRule();
    setEditingRule(newRule);
    setIsCreating(true);
  };

  const handleEditRule = (rule: Rule) => {
    setEditingRule({ ...rule });
    setIsCreating(false);
  };

  const handleSaveRule = () => {
    if (editingRule) {
      onSaveRule({
        ...editingRule,
        lastModified: new Date(),
      });
      setEditingRule(null);
      setIsCreating(false);
    }
  };

  const handleCancelEdit = () => {
    setEditingRule(null);
    setIsCreating(false);
  };

  const addCondition = () => {
    if (editingRule) {
      const newCondition: RuleCondition = {
        id: `condition-${Date.now()}`,
        layerId: '',
        traitId: '',
        operator: 'MUST_HAVE',
        isChained: true,
        chainOperator: 'AND',
      };
      setEditingRule({
        ...editingRule,
        conditions: [...editingRule.conditions, newCondition],
      });
    }
  };

  const removeCondition = (conditionId: string) => {
    if (editingRule) {
      setEditingRule({
        ...editingRule,
        conditions: editingRule.conditions.filter(c => c.id !== conditionId),
      });
    }
  };

  const updateCondition = (conditionId: string, updates: Partial<RuleCondition>) => {
    if (editingRule) {
      setEditingRule({
        ...editingRule,
        conditions: editingRule.conditions.map(c =>
          c.id === conditionId ? { ...c, ...updates } : c
        ),
      });
    }
  };

  const toggleChain = (conditionId: string) => {
    if (editingRule) {
      const condition = editingRule.conditions.find(c => c.id === conditionId);
      if (condition) {
        updateCondition(conditionId, {
          isChained: !condition.isChained,
          chainOperator: condition.isChained ? undefined : 'AND',
        });
      }
    }
  };

  const getLayerTraits = (layerId: string) => {
    return traits.filter(trait => trait.layerId === layerId);
  };

  if (editingRule) {
    return (
      <Dialog open={open} onClose={handleCancelEdit} maxWidth="md" fullWidth>
        <DialogTitle>
          {isCreating ? 'Create New Rule' : 'Edit Rule'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mb: 3 }}>
            <TextField
              fullWidth
              label="Rule Name"
              value={editingRule.name}
              onChange={(e) => setEditingRule({ ...editingRule, name: e.target.value })}
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="Description"
              multiline
              rows={2}
              value={editingRule.description}
              onChange={(e) => setEditingRule({ ...editingRule, description: e.target.value })}
              sx={{ mb: 2 }}
            />
            <FormControlLabel
              control={
                <Switch
                  checked={editingRule.isEnabled}
                  onChange={(e) => setEditingRule({ ...editingRule, isEnabled: e.target.checked })}
                />
              }
              label="Enable Rule"
            />
          </Box>

          <Typography variant="h6" gutterBottom>
            IF Conditions
          </Typography>
          
          {editingRule.conditions.map((condition, index) => (
            <Box key={condition.id} sx={{ mb: 2, p: 2, border: 1, borderColor: 'divider', borderRadius: 1 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                {index > 0 && (
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <IconButton
                      size="small"
                      onClick={() => toggleChain(condition.id)}
                      color={condition.isChained ? 'primary' : 'default'}
                    >
                      {condition.isChained ? <ChainIcon /> : <UnchainIcon />}
                    </IconButton>
                    {condition.isChained && (
                      <FormControl size="small" sx={{ minWidth: 80 }}>
                        <Select
                          value={condition.chainOperator || 'AND'}
                          onChange={(e) => updateCondition(condition.id, { chainOperator: e.target.value as 'AND' | 'OR' })}
                        >
                          <MenuItem value="AND">AND</MenuItem>
                          <MenuItem value="OR">OR</MenuItem>
                        </Select>
                      </FormControl>
                    )}
                  </Box>
                )}
                
                <FormControl sx={{ minWidth: 120 }}>
                  <InputLabel>Operator</InputLabel>
                  <Select
                    value={condition.operator}
                    label="Operator"
                    onChange={(e) => updateCondition(condition.id, { operator: e.target.value as any })}
                  >
                    <MenuItem value="MUST_HAVE">MUST HAVE</MenuItem>
                    <MenuItem value="CANNOT_HAVE">CANNOT HAVE</MenuItem>
                  </Select>
                </FormControl>

                <FormControl sx={{ minWidth: 200 }}>
                  <InputLabel>Layer</InputLabel>
                  <Select
                    value={condition.layerId}
                    label="Layer"
                    onChange={(e) => updateCondition(condition.id, { layerId: e.target.value, traitId: '' })}
                  >
                    {layers.map(layer => (
                      <MenuItem key={layer.id} value={layer.id}>
                        {layer.settings.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>

                <FormControl sx={{ minWidth: 200 }}>
                  <InputLabel>Trait</InputLabel>
                  <Select
                    value={condition.traitId}
                    label="Trait"
                    onChange={(e) => updateCondition(condition.id, { traitId: e.target.value })}
                    disabled={!condition.layerId}
                  >
                    {getLayerTraits(condition.layerId).map(trait => (
                      <MenuItem key={trait.id} value={trait.id}>
                        {trait.metadata.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>

                <IconButton
                  color="error"
                  onClick={() => removeCondition(condition.id)}
                  disabled={editingRule.conditions.length === 1}
                >
                  <DeleteIcon />
                </IconButton>
              </Box>
            </Box>
          ))}

          <Button
            startIcon={<AddIcon />}
            onClick={addCondition}
            variant="outlined"
            sx={{ mb: 3 }}
          >
            Add Condition
          </Button>

          <Typography variant="h6" gutterBottom>
            THEN Actions
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Actions will be implemented in the next phase.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCancelEdit}>Cancel</Button>
          <Button onClick={handleSaveRule} variant="contained">
            Save Rule
          </Button>
        </DialogActions>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Typography variant="h6">Rules Management</Typography>
          <Button
            startIcon={<AddIcon />}
            onClick={handleCreateRule}
            variant="contained"
          >
            Create Rule
          </Button>
        </Box>
      </DialogTitle>
      <DialogContent>
        {rules.length === 0 ? (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <RuleIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" color="text.secondary" gutterBottom>
              No Rules Created
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Create your first rule to define trait relationships and constraints.
            </Typography>
          </Box>
        ) : (
          <List>
            {rules.map((rule, index) => (
              <React.Fragment key={rule.id}>
                <ListItem>
                  <ListItemIcon>
                    <RuleIcon color={rule.isEnabled ? 'primary' : 'disabled'} />
                  </ListItemIcon>
                  <ListItemText
                    primary={rule.name}
                    secondary={
                      <Box>
                        <Typography variant="body2" color="text.secondary">
                          {rule.description || 'No description'}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {rule.conditions.length} conditions • {rule.actions.length} actions
                        </Typography>
                      </Box>
                    }
                  />
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Chip
                      label={rule.isEnabled ? 'Enabled' : 'Disabled'}
                      size="small"
                      color={rule.isEnabled ? 'success' : 'default'}
                    />
                    <IconButton
                      size="small"
                      onClick={() => handleEditRule(rule)}
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton
                      size="small"
                      color="error"
                      onClick={() => onDeleteRule(rule.id)}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Box>
                </ListItem>
                {index < rules.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </List>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Close</Button>
      </DialogActions>
    </Dialog>
  );
};

export default RulesModal;
