import { useState, useCallback } from 'react';
import { ImportLayers, ImportLayersRequest, ImportLayersResponse, ImportProgress } from '@/domain/usecases/ImportLayers';
import { IndexedDBLayerRepository } from '@/infrastructure/storage/IndexedDBLayerRepository';
import { IndexedDBTraitRepository } from '@/infrastructure/storage/IndexedDBTraitRepository';
import { useProjectStore } from '@/app/stores/useProjectStore';

export interface UseLayerImportResult {
  isImporting: boolean;
  progress: ImportProgress | null;
  error: string | null;
  importFromDirectory: () => Promise<void>;
  importFromFiles: (files: File[]) => Promise<void>;
  clearError: () => void;
}

export const useLayerImport = (): UseLayerImportResult => {
  const [isImporting, setIsImporting] = useState(false);
  const [progress, setProgress] = useState<ImportProgress | null>(null);
  const [error, setError] = useState<string | null>(null);

  const { currentProject, addLayer, addTrait } = useProjectStore();

  // Initialize repositories
  const layerRepository = new IndexedDBLayerRepository();
  const traitRepository = new IndexedDBTraitRepository();
  const importLayers = new ImportLayers(layerRepository, traitRepository);

  const importFromDirectory = useCallback(async () => {
    if (!currentProject) {
      setError('No project selected');
      return;
    }

    // Check if File System Access API is supported
    if (!('showDirectoryPicker' in window)) {
      setError('File System Access API is not supported in this browser. Please use Chrome or Edge.');
      return;
    }

    try {
      setIsImporting(true);
      setError(null);
      setProgress(null);

      // Show directory picker
      const directoryHandle = await (window as any).showDirectoryPicker({
        mode: 'read',
      });

      const request: ImportLayersRequest = {
        projectId: currentProject.id,
        directoryHandle,
      };

      const response = await importLayers.execute(request, (progressInfo) => {
        setProgress(progressInfo);
      });

      if (response.success) {
        // Add layers to store
        response.layers.forEach(layer => {
          addLayer(layer);
        });

        // Show warnings if any
        if (response.warnings.length > 0) {
          console.warn('Import warnings:', response.warnings);
        }

        setProgress({
          currentLayer: 'Complete',
          processedFiles: 100,
          totalFiles: 100,
          percentage: 100,
        });

        // Clear progress after a short delay
        setTimeout(() => {
          setProgress(null);
        }, 2000);
      } else {
        setError(response.errors.join('\n'));
      }
    } catch (err) {
      if (err instanceof Error) {
        if (err.name === 'AbortError') {
          // User cancelled the picker
          setError(null);
        } else {
          setError(`Import failed: ${err.message}`);
        }
      } else {
        setError('Import failed: Unknown error');
      }
    } finally {
      setIsImporting(false);
    }
  }, [currentProject, importLayers, addLayer]);

  const importFromFiles = useCallback(async (files: File[]) => {
    if (!currentProject) {
      setError('No project selected');
      return;
    }

    if (files.length === 0) {
      setError('No files selected');
      return;
    }

    try {
      setIsImporting(true);
      setError(null);
      setProgress(null);

      const request: ImportLayersRequest = {
        projectId: currentProject.id,
        files,
      };

      const response = await importLayers.execute(request, (progressInfo) => {
        setProgress(progressInfo);
      });

      if (response.success) {
        // Add layers to store
        response.layers.forEach(layer => {
          addLayer(layer);
        });

        // Show warnings if any
        if (response.warnings.length > 0) {
          console.warn('Import warnings:', response.warnings);
        }

        setProgress({
          currentLayer: 'Complete',
          processedFiles: 100,
          totalFiles: 100,
          percentage: 100,
        });

        // Clear progress after a short delay
        setTimeout(() => {
          setProgress(null);
        }, 2000);
      } else {
        setError(response.errors.join('\n'));
      }
    } catch (err) {
      if (err instanceof Error) {
        setError(`Import failed: ${err.message}`);
      } else {
        setError('Import failed: Unknown error');
      }
    } finally {
      setIsImporting(false);
    }
  }, [currentProject, importLayers, addLayer]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    isImporting,
    progress,
    error,
    importFromDirectory,
    importFromFiles,
    clearError,
  };
};

export default useLayerImport;
