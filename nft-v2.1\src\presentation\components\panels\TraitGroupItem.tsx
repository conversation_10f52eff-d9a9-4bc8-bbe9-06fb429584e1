import React from 'react';
import {
  Box,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  IconButton,
  Collapse,
  Chip,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Image as ImageIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Folder as FolderIcon,
  FolderOpen as FolderOpenIcon,
} from '@mui/icons-material';
import { TraitGroup } from '../../../domain/entities/Layer';

interface TraitGroupItemProps {
  group: TraitGroup;
  traits: any[];
  selectedTraitIds: string[];
  onToggleCollapse: (groupId: string) => void;
  onSelectTrait: (traitId: string) => void;
  onToggleTraitVisibility: (traitId: string, e: React.MouseEvent) => void;
  onEditGroup: (groupId: string) => void;
  onDeleteGroup: (groupId: string) => void;
  level?: number;
}

export const TraitGroupItem: React.FC<TraitGroupItemProps> = ({
  group,
  traits,
  selectedTraitIds,
  onToggleCollapse,
  onSelectTrait,
  onToggleTraitVisibility,
  onEditGroup,
  onDeleteGroup,
  level = 0,
}) => {
  const groupTraits = traits.filter(trait => group.traitIds.includes(trait.id));
  const indentLevel = level * 20;

  return (
    <Box>
      {/* Group Header */}
      <ListItem
        sx={{
          pl: 2 + indentLevel / 8,
          py: 1,
          backgroundColor: 'action.hover',
          borderLeft: level > 0 ? '2px solid' : 'none',
          borderLeftColor: 'primary.main',
        }}
      >
        <ListItemIcon sx={{ minWidth: 36 }}>
          <IconButton
            size="small"
            onClick={() => onToggleCollapse(group.id)}
          >
            {group.isCollapsed ? <FolderIcon /> : <FolderOpenIcon />}
          </IconButton>
        </ListItemIcon>

        <ListItemText
          primary={
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Typography variant="body2" fontWeight={600}>
                {group.name}
              </Typography>
              <Chip
                label={`${groupTraits.length} traits`}
                size="small"
                variant="outlined"
                sx={{ height: 20, fontSize: '0.7rem' }}
              />
            </Box>
          }
        />

        <Box sx={{ display: 'flex', gap: 0.5 }}>
          <IconButton
            size="small"
            onClick={() => onEditGroup(group.id)}
            color="primary"
          >
            <EditIcon />
          </IconButton>

          <IconButton
            size="small"
            onClick={() => onDeleteGroup(group.id)}
            color="error"
          >
            <DeleteIcon />
          </IconButton>

          <IconButton
            size="small"
            onClick={() => onToggleCollapse(group.id)}
          >
            {group.isCollapsed ? <ExpandMoreIcon /> : <ExpandLessIcon />}
          </IconButton>
        </Box>
      </ListItem>

      {/* Group Traits */}
      <Collapse in={!group.isCollapsed} timeout="auto" unmountOnExit>
        <List sx={{ pl: 2 + indentLevel / 8 }}>
          {groupTraits.map((trait) => (
            <ListItem
              key={trait.id}
              component="div"
              selected={selectedTraitIds.includes(trait.id)}
              onClick={() => onSelectTrait(trait.id)}
              sx={{
                py: 0.5,
                pl: 4,
                cursor: 'pointer',
                borderLeft: '1px solid',
                borderLeftColor: 'divider',
                '&.Mui-selected': {
                  backgroundColor: 'primary.light',
                  '&:hover': {
                    backgroundColor: 'primary.light',
                  },
                },
                '&:hover': {
                  backgroundColor: 'action.hover',
                },
              }}
            >
              <ListItemIcon sx={{ minWidth: 32 }}>
                <ImageIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
              </ListItemIcon>

              <ListItemText
                primary={trait.metadata.name}
                secondary={`${trait.metadata.rarity.toFixed(2)}%`}
                primaryTypographyProps={{
                  variant: 'body2',
                  fontSize: '0.85rem',
                }}
                secondaryTypographyProps={{
                  variant: 'caption',
                  fontSize: '0.75rem',
                }}
              />

              <Box sx={{ display: 'flex', gap: 0.5 }}>
                <IconButton
                  size="small"
                  onClick={(e) => onToggleTraitVisibility(trait.id, e)}
                  color={trait.metadata.isVisible ? 'primary' : 'default'}
                >
                  {trait.metadata.isVisible ? (
                    <VisibilityIcon sx={{ fontSize: 16 }} />
                  ) : (
                    <VisibilityOffIcon sx={{ fontSize: 16 }} />
                  )}
                </IconButton>
              </Box>
            </ListItem>
          ))}
        </List>
      </Collapse>
    </Box>
  );
};

export default TraitGroupItem;
