import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  IconButton,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  Divider,
  Slider,
} from '@mui/material';
import {
  Shuffle as ShuffleIcon,
  ZoomIn as ZoomInIcon,
  ZoomOut as ZoomOutIcon,
  CenterFocusStrong as CenterIcon,
  Download as DownloadIcon,
  Visibility as VisibilityIcon,
  Layers as LayersIcon,
} from '@mui/icons-material';
import { useProjectStore } from '../../../app/stores/useProjectStore';

export const PreviewPanel: React.FC = () => {
  const {
    currentProject,
    layers,
    traits,
    selectedTraitIds,
    selectMultipleTraits
  } = useProjectStore();

  const [zoom, setZoom] = useState(100);
  const [previewTraits, setPreviewTraits] = useState<any[]>([]);

  // Get visible layers and their selected traits
  const visibleLayers = layers.filter(layer => layer.settings.isVisible);
  const currentPreview = visibleLayers.map(layer => {
    const layerTraits = traits.filter(t => t.layerId === layer.id);
    const selectedTrait = layerTraits.find(t => selectedTraitIds.includes(t.id));
    return {
      layer,
      trait: selectedTrait || null,
    };
  }).filter(item => item.trait);

  const handleRandomize = () => {
    const randomTraitIds: string[] = [];

    // For each visible layer, select a random trait
    const visibleLayers = layers.filter(layer => layer.settings.isVisible);

    for (const layer of visibleLayers) {
      const layerTraits = traits.filter(t => t.layerId === layer.id && t.isEnabled);

      if (layerTraits.length > 0) {
        // Weighted random selection based on rarity
        const totalWeight = layerTraits.reduce((sum, trait) => sum + trait.rarity.weight, 0);
        const random = Math.random() * totalWeight;

        let currentWeight = 0;
        for (const trait of layerTraits) {
          currentWeight += trait.rarity.weight;
          if (random <= currentWeight) {
            randomTraitIds.push(trait.id);
            break;
          }
        }
      }
    }

    selectMultipleTraits(randomTraitIds);
  };

  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev + 25, 300));
  };

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev - 25, 25));
  };

  const handleResetZoom = () => {
    setZoom(100);
  };

  const handleExportPreview = async () => {
    if (currentPreview.length === 0) {
      alert('No traits selected for preview');
      return;
    }

    try {
      // Create a canvas for export
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      if (!ctx || !currentProject) return;

      canvas.width = currentProject.settings.outputSize.width;
      canvas.height = currentProject.settings.outputSize.height;

      // Fill background if specified
      if (currentProject.settings.backgroundColor) {
        ctx.fillStyle = currentProject.settings.backgroundColor;
        ctx.fillRect(0, 0, canvas.width, canvas.height);
      }

      // Draw layers in order (bottom to top based on panel order)
      const sortedPreview = currentPreview.sort((a, b) => b.layer.settings.zIndex - a.layer.settings.zIndex);

      for (const item of sortedPreview) {
        if (item.trait?.file?.dataUrl) {
          const img = new Image();
          await new Promise((resolve, reject) => {
            img.onload = resolve;
            img.onerror = reject;
            img.src = item.trait!.file!.dataUrl!;
          });

          // Apply layer opacity
          ctx.globalAlpha = item.layer.settings.opacity / 100;
          ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
          ctx.globalAlpha = 1;
        }
      }

      // Download the image
      canvas.toBlob((blob) => {
        if (blob) {
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `${currentProject.settings.name}-preview.${currentProject.settings.outputFormat.toLowerCase()}`;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        }
      }, `image/${currentProject.settings.outputFormat.toLowerCase()}`, currentProject.settings.outputQuality / 100);

    } catch (error) {
      console.error('Export failed:', error);
      alert('Failed to export preview');
    }
  };

  const calculateRarityScore = () => {
    if (currentPreview.length === 0) return 0;
    
    const totalRarity = currentPreview.reduce((sum, item) => {
      return sum + (item.trait?.rarity.value || 0);
    }, 0);
    
    return totalRarity / currentPreview.length;
  };

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Panel Header */}
      <Box
        sx={{
          p: 2,
          borderBottom: 1,
          borderColor: 'divider',
          backgroundColor: 'background.paper',
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Typography variant="h6" component="h2">
            Preview
          </Typography>
          <Chip
            label={`${currentPreview.length} traits`}
            size="small"
            color="primary"
            variant="outlined"
          />
        </Box>

        <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
          <Button
            variant="contained"
            startIcon={<ShuffleIcon />}
            onClick={handleRandomize}
            size="small"
            fullWidth
          >
            Randomize
          </Button>
          <Button
            variant="outlined"
            startIcon={<DownloadIcon />}
            onClick={handleExportPreview}
            size="small"
          >
            Export
          </Button>
        </Box>

        {/* Zoom Controls */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <IconButton size="small" onClick={handleZoomOut}>
            <ZoomOutIcon />
          </IconButton>
          <Slider
            value={zoom}
            onChange={(_, value) => setZoom(value as number)}
            min={25}
            max={300}
            step={25}
            sx={{ flex: 1 }}
            size="small"
          />
          <IconButton size="small" onClick={handleZoomIn}>
            <ZoomInIcon />
          </IconButton>
          <IconButton size="small" onClick={handleResetZoom}>
            <CenterIcon />
          </IconButton>
          <Typography variant="caption" sx={{ minWidth: 40 }}>
            {zoom}%
          </Typography>
        </Box>
      </Box>

      {/* Preview Canvas */}
      <Box
        sx={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden',
        }}
      >
        {/* Canvas Area */}
        <Box
          sx={{
            flex: 1,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'grey.50',
            overflow: 'auto',
            p: 2,
          }}
        >
          <Card
            sx={{
              width: currentProject ? 
                `${(currentProject.settings.outputSize.width * zoom) / 100}px` : 
                `${(400 * zoom) / 100}px`,
              height: currentProject ? 
                `${(currentProject.settings.outputSize.height * zoom) / 100}px` : 
                `${(400 * zoom) / 100}px`,
              backgroundColor: currentProject?.settings.backgroundColor || '#ffffff',
              position: 'relative',
              overflow: 'hidden',
              border: 1,
              borderColor: 'divider',
            }}
          >
            {currentPreview.length === 0 ? (
              <Box
                sx={{
                  height: '100%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  flexDirection: 'column',
                  color: 'text.secondary',
                }}
              >
                <VisibilityIcon sx={{ fontSize: 48, mb: 2, opacity: 0.5 }} />
                <Typography variant="body2" align="center">
                  No traits selected
                </Typography>
                <Typography variant="caption" align="center">
                  Select traits to see preview
                </Typography>
              </Box>
            ) : (
              // Layer stack (bottom to top based on panel order)
              currentPreview
                .sort((a, b) => b.layer.settings.zIndex - a.layer.settings.zIndex)
                .map((item, index) => (
                  <Box
                    key={`${item.layer.id}-${item.trait?.id}`}
                    sx={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      width: '100%',
                      height: '100%',
                      opacity: item.layer.settings.opacity / 100,
                      zIndex: index,
                    }}
                  >
                    {item.trait?.file.dataUrl && (
                      <img
                        src={item.trait.file.dataUrl}
                        alt={item.trait.metadata.name}
                        style={{
                          width: '100%',
                          height: '100%',
                          objectFit: 'contain',
                        }}
                      />
                    )}
                  </Box>
                ))
            )}
          </Card>
        </Box>

        {/* Trait Info */}
        <Box sx={{ maxHeight: '40%', overflow: 'auto' }}>
          <Divider />
          
          {/* Rarity Score */}
          <Box sx={{ p: 2, backgroundColor: 'background.paper' }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="body2" fontWeight={600}>
                Rarity Score
              </Typography>
              <Chip
                label={`${calculateRarityScore().toFixed(2)}%`}
                size="small"
                color={calculateRarityScore() < 10 ? 'error' : calculateRarityScore() < 30 ? 'warning' : 'success'}
              />
            </Box>
          </Box>

          <Divider />

          {/* Current Traits */}
          <Box sx={{ p: 1 }}>
            <Typography variant="body2" fontWeight={600} sx={{ p: 1 }}>
              Current Traits ({currentPreview.length})
            </Typography>
            
            {currentPreview.length === 0 ? (
              <Box sx={{ p: 2, textAlign: 'center', color: 'text.secondary' }}>
                <Typography variant="caption">
                  No traits selected
                </Typography>
              </Box>
            ) : (
              <List dense sx={{ p: 0 }}>
                {currentPreview
                  .sort((a, b) => b.layer.settings.zIndex - a.layer.settings.zIndex) // Top to bottom for display
                  .map((item) => (
                    <ListItem key={`${item.layer.id}-${item.trait?.id}`} sx={{ py: 0.5 }}>
                      <ListItemIcon sx={{ minWidth: 32 }}>
                        <LayersIcon sx={{ fontSize: 16 }} />
                      </ListItemIcon>
                      <ListItemText
                        primary={item.trait?.metadata.name}
                        secondary={item.layer.settings.name}
                        primaryTypographyProps={{ variant: 'body2' }}
                        secondaryTypographyProps={{ variant: 'caption' }}
                      />
                      <Chip
                        label={`${item.trait?.rarity.value.toFixed(1)}%`}
                        size="small"
                        variant="outlined"
                        sx={{ fontSize: '0.7rem', height: 20 }}
                      />
                    </ListItem>
                  ))}
              </List>
            )}
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default PreviewPanel;
