import React, { useRef } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  IconButton,
  Chip,
  Divider,
  LinearProgress,
  Alert,
  Menu,
  MenuItem,
} from '@mui/material';
import {
  Add as AddIcon,
  Folder as FolderIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Lock as LockIcon,
  LockOpen as LockOpenIcon,
  DragIndicator as DragIcon,
  Delete as DeleteIcon,
  ExpandMore as ExpandMoreIcon,
  Upload as UploadIcon,
} from '@mui/icons-material';
import { useProjectStore } from '@/app/stores/useProjectStore';
import { useSimpleLayerImport } from '@/app/hooks/useSimpleLayerImport';
import ImportWizard, { LayerStructure, TraitRelation } from '@/presentation/components/import/ImportWizard';

export const LayersPanel: React.FC = () => {
  const { layers, currentProject, selectLayer, selectedLayerId, loadLayerTraits, updateLayer, removeLayer } = useProjectStore();
  const { isImporting, error, importFromFiles, clearError } = useSimpleLayerImport();

  const fileInputRef = useRef<HTMLInputElement>(null);
  const [importMenuAnchor, setImportMenuAnchor] = React.useState<null | HTMLElement>(null);
  const [wizardOpen, setWizardOpen] = React.useState(false);
  const [wizardFiles, setWizardFiles] = React.useState<File[]>([]);

  const handleImportMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setImportMenuAnchor(event.currentTarget);
  };

  const handleImportMenuClose = () => {
    setImportMenuAnchor(null);
  };

  const handleImportFromFolder = async () => {
    handleImportMenuClose();
    // For now, just trigger file input with directory support
    if (fileInputRef.current) {
      fileInputRef.current.setAttribute('webkitdirectory', '');
      fileInputRef.current.click();
    }
  };

  const handleImportFromFiles = () => {
    handleImportMenuClose();
    // Remove webkitdirectory for regular file selection
    if (fileInputRef.current) {
      fileInputRef.current.removeAttribute('webkitdirectory');
      fileInputRef.current.click();
    }
  };

  const handleFileInputChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    if (files.length > 0) {
      // Check if this is a complex folder structure that needs wizard
      const hasComplexStructure = files.some(file =>
        file.webkitRelativePath && file.webkitRelativePath.split('/').length > 3
      );

      if (hasComplexStructure || files.length > 20) {
        // Use wizard for complex imports
        setWizardFiles(files);
        setWizardOpen(true);
      } else {
        // Use simple import for basic structures
        await importFromFiles(files);
      }
    }
    // Reset input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleWizardImport = async (layerStructures: LayerStructure[], relations: TraitRelation[]) => {
    const { addLayer, addTrait } = useProjectStore.getState();

    try {
      // Import layers with wizard configuration
      for (const layerStructure of layerStructures) {
        if (!layerStructure.isEnabled) continue;

        const layer = {
          id: `layer-${Date.now()}-${layerStructure.order}`,
          projectId: currentProject!.id,
          settings: {
            name: layerStructure.name,
            description: `Imported via wizard: ${layerStructure.name}`,
            isVisible: true,
            isLocked: false,
            opacity: 100,
            blendMode: 'normal' as const,
            zIndex: layerStructure.order,
          },
          constraints: {
            maxRarity: 100,
            minTraits: 0,
            maxTraits: 1,
            isRequired: false,
          },
          traitIds: [] as string[],
          importedAt: new Date(),
          lastModified: new Date(),
          relations: layerStructure.relations,
        };

        // Create traits for this layer
        const traitIds: string[] = [];
        const rarityPerTrait = layerStructure.files.length > 0 ? 100 / layerStructure.files.length : 0;

        for (let i = 0; i < layerStructure.files.length; i++) {
          const fileData = layerStructure.files[i];
          const traitId = `trait-${Date.now()}-${layerStructure.order}-${i}`;

          const trait = {
            id: traitId,
            layerId: layer.id,
            projectId: currentProject!.id,
            metadata: {
              name: fileData.suggestedName,
              description: `Imported from: ${fileData.path}`,
              category: layerStructure.name,
              tags: fileData.subfolderPath ? fileData.subfolderPath.split('/') : [],
              attributes: {},
            },
            file: {
              name: fileData.file.name,
              path: fileData.path,
              size: fileData.file.size,
              type: fileData.file.type,
              lastModified: new Date(fileData.file.lastModified),
              blob: fileData.file,
              dataUrl: await fileToDataUrl(fileData.file),
            },
            rarity: {
              value: rarityPerTrait,
              isLocked: false,
              weight: 1,
            },
            isEnabled: true,
            importedAt: new Date(),
            lastModified: new Date(),
            relations: [], // Will be populated from trait relations
          };

          traitIds.push(traitId);
          addTrait(trait);
        }

        layer.traitIds = traitIds;
        addLayer(layer);
      }

      // TODO: Process trait relations
      console.log('Trait relations to process:', relations);

    } catch (error) {
      console.error('Wizard import failed:', error);
    }
  };

  const fileToDataUrl = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  };

  const handleLayerSelect = async (layerId: string) => {
    selectLayer(layerId);
    await loadLayerTraits(layerId);
  };

  const handleToggleVisibility = (layerId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    const layer = layers.find(l => l.id === layerId);
    if (layer) {
      const updatedLayer = {
        ...layer,
        settings: {
          ...layer.settings,
          isVisible: !layer.settings.isVisible,
        },
        lastModified: new Date(),
      };
      updateLayer(updatedLayer);
    }
  };

  const handleToggleLock = (layerId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    const layer = layers.find(l => l.id === layerId);
    if (layer) {
      const updatedLayer = {
        ...layer,
        settings: {
          ...layer.settings,
          isLocked: !layer.settings.isLocked,
        },
        lastModified: new Date(),
      };
      updateLayer(updatedLayer);
    }
  };

  const handleDeleteLayer = (layerId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    if (window.confirm('Are you sure you want to delete this layer?')) {
      removeLayer(layerId);
      // If this was the selected layer, clear selection
      if (selectedLayerId === layerId) {
        selectLayer(null);
      }
    }
  };

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Panel Header */}
      <Box
        sx={{
          p: 2,
          borderBottom: 1,
          borderColor: 'divider',
          backgroundColor: 'background.paper',
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Typography variant="h6" component="h2">
            Layers
          </Typography>
          <Chip
            label={layers.length}
            size="small"
            color="primary"
            variant="outlined"
          />
        </Box>

        <Button
          variant="contained"
          startIcon={<FolderIcon />}
          endIcon={<ExpandMoreIcon />}
          onClick={handleImportMenuOpen}
          fullWidth
          sx={{ mb: 1 }}
          disabled={isImporting || !currentProject}
        >
          {isImporting ? 'Importing...' : 'Import Layers'}
        </Button>

        {/* Import Menu */}
        <Menu
          anchorEl={importMenuAnchor}
          open={Boolean(importMenuAnchor)}
          onClose={handleImportMenuClose}
        >
          <MenuItem onClick={handleImportFromFolder}>
            <FolderIcon sx={{ mr: 1 }} />
            From Folder
          </MenuItem>
          <MenuItem onClick={handleImportFromFiles}>
            <UploadIcon sx={{ mr: 1 }} />
            From Files
          </MenuItem>
        </Menu>

        {/* Hidden file input */}
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*"
          style={{ display: 'none' }}
          onChange={handleFileInputChange}
        />

        <Button
          variant="outlined"
          startIcon={<AddIcon />}
          fullWidth
          size="small"
          disabled={isImporting}
        >
          Add Layer
        </Button>

        {/* Import Progress */}
        {isImporting && (
          <Box sx={{ mt: 2 }}>
            <Typography variant="caption" color="text.secondary" gutterBottom>
              Importing files...
            </Typography>
            <LinearProgress sx={{ height: 6, borderRadius: 3 }} />
          </Box>
        )}

        {/* Import Error */}
        {error && (
          <Alert
            severity="error"
            onClose={clearError}
            sx={{ mt: 2 }}
          >
            {error}
          </Alert>
        )}
      </Box>

      {/* Layers List */}
      <Box sx={{ flex: 1, overflow: 'auto' }}>
        {layers.length === 0 ? (
          <Box
            sx={{
              p: 3,
              textAlign: 'center',
              color: 'text.secondary',
            }}
          >
            <FolderIcon sx={{ fontSize: 48, mb: 2, opacity: 0.5 }} />
            <Typography variant="body2" gutterBottom>
              No layers imported yet
            </Typography>
            <Typography variant="caption">
              Click "Import Layers" to get started
            </Typography>
          </Box>
        ) : (
          <List sx={{ p: 0 }}>
            {layers.map((layer, index) => (
              <React.Fragment key={layer.id}>
                <ListItem
                  component="div"
                  selected={selectedLayerId === layer.id}
                  onClick={() => handleLayerSelect(layer.id)}
                  sx={{
                    py: 1.5,
                    px: 2,
                    cursor: 'pointer',
                    '&.Mui-selected': {
                      backgroundColor: 'primary.light',
                      '&:hover': {
                        backgroundColor: 'primary.light',
                      },
                    },
                    '&:hover': {
                      backgroundColor: 'action.hover',
                    },
                  }}
                >
                  <ListItemIcon sx={{ minWidth: 36 }}>
                    <DragIcon sx={{ color: 'text.secondary', cursor: 'grab' }} />
                  </ListItemIcon>

                  <ListItemText
                    primary={layer.settings.name}
                    secondary={`${layer.traitIds.length} traits • Z-Index: ${layer.settings.zIndex}`}
                    primaryTypographyProps={{
                      variant: 'body2',
                      fontWeight: selectedLayerId === layer.id ? 600 : 400,
                    }}
                    secondaryTypographyProps={{
                      variant: 'caption',
                    }}
                  />

                  <Box sx={{ display: 'flex', gap: 0.5 }}>
                    <IconButton
                      size="small"
                      onClick={(e) => handleToggleVisibility(layer.id, e)}
                      color={layer.settings.isVisible ? 'primary' : 'default'}
                    >
                      {layer.settings.isVisible ? <VisibilityIcon /> : <VisibilityOffIcon />}
                    </IconButton>

                    <IconButton
                      size="small"
                      onClick={(e) => handleToggleLock(layer.id, e)}
                      color={layer.settings.isLocked ? 'warning' : 'default'}
                    >
                      {layer.settings.isLocked ? <LockIcon /> : <LockOpenIcon />}
                    </IconButton>

                    <IconButton
                      size="small"
                      onClick={(e) => handleDeleteLayer(layer.id, e)}
                      color="error"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Box>
                </ListItem>
                {index < layers.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </List>
        )}
      </Box>

      {/* Panel Footer */}
      {layers.length > 0 && (
        <Box
          sx={{
            p: 2,
            borderTop: 1,
            borderColor: 'divider',
            backgroundColor: 'background.paper',
          }}
        >
          <Typography variant="caption" color="text.secondary">
            Drag layers to reorder • Click to select
          </Typography>
        </Box>
      )}

      {/* Import Wizard */}
      <ImportWizard
        open={wizardOpen}
        onClose={() => setWizardOpen(false)}
        files={wizardFiles}
        onImport={handleWizardImport}
      />
    </Box>
  );
};

export default LayersPanel;
