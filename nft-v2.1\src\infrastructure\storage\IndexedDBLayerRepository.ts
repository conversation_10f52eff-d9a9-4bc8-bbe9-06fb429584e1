import Dexie, { Table } from 'dexie';
import { Layer } from '@/domain/entities/Layer';
import { ILayerRepository, LayerQuery, LayerListOptions } from '@/domain/repositories/ILayerRepository';

interface LayerRecord {
  id: string;
  projectId: string;
  data: any; // Serialized layer data
  createdAt: Date;
  updatedAt: Date;
}

class LayerDatabase extends Dexie {
  layers!: Table<LayerRecord>;

  constructor() {
    super('NFTGeneratorLayers');
    
    this.version(1).stores({
      layers: 'id, projectId, createdAt, updatedAt',
    });
  }
}

export class IndexedDBLayerRepository implements ILayerRepository {
  private db: LayerDatabase;

  constructor() {
    this.db = new LayerDatabase();
  }

  async create(layer: Layer): Promise<Layer> {
    const record: LayerRecord = {
      id: layer.id,
      projectId: layer.projectId,
      data: layer.toJSON(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    await this.db.layers.add(record);
    return layer;
  }

  async getById(id: string): Promise<Layer | null> {
    const record = await this.db.layers.get(id);
    if (!record) return null;

    return Layer.fromJSON(record.data);
  }

  async getAll(query?: LayerQuery, options?: LayerListOptions): Promise<Layer[]> {
    let collection = this.db.layers.orderBy('updatedAt');

    // Apply filters
    if (query?.projectId) {
      collection = collection.filter(record => record.projectId === query.projectId);
    }

    // Apply sorting
    if (options?.sortBy) {
      switch (options.sortBy) {
        case 'name':
          collection = collection.orderBy('data.settings.name');
          break;
        case 'zIndex':
          collection = collection.orderBy('data.settings.zIndex');
          break;
        case 'importedAt':
          collection = collection.orderBy('createdAt');
          break;
        case 'lastModified':
          collection = collection.orderBy('updatedAt');
          break;
      }

      if (options.sortOrder === 'desc') {
        collection = collection.reverse();
      }
    }

    // Apply pagination
    if (options?.offset) {
      collection = collection.offset(options.offset);
    }
    if (options?.limit) {
      collection = collection.limit(options.limit);
    }

    const records = await collection.toArray();
    return records.map(record => Layer.fromJSON(record.data));
  }

  async getByProjectId(projectId: string, options?: LayerListOptions): Promise<Layer[]> {
    return this.getAll({ projectId }, options);
  }

  async update(layer: Layer): Promise<Layer> {
    const record: Partial<LayerRecord> = {
      data: layer.toJSON(),
      updatedAt: new Date(),
    };

    await this.db.layers.update(layer.id, record);
    return layer;
  }

  async delete(id: string): Promise<boolean> {
    const deleteCount = await this.db.layers.delete(id);
    return deleteCount > 0;
  }

  async deleteByProjectId(projectId: string): Promise<number> {
    return await this.db.layers.where('projectId').equals(projectId).delete();
  }

  async exists(id: string): Promise<boolean> {
    const record = await this.db.layers.get(id);
    return !!record;
  }

  async count(query?: LayerQuery): Promise<number> {
    let collection = this.db.layers.toCollection();

    if (query?.projectId) {
      collection = collection.filter(record => record.projectId === query.projectId);
    }

    return await collection.count();
  }

  async reorder(projectId: string, layerIds: string[]): Promise<Layer[]> {
    const layers = await this.getByProjectId(projectId);
    const reorderedLayers: Layer[] = [];

    // Update z-index based on new order
    for (let i = 0; i < layerIds.length; i++) {
      const layer = layers.find(l => l.id === layerIds[i]);
      if (layer) {
        layer.setZIndex(i);
        await this.update(layer);
        reorderedLayers.push(layer);
      }
    }

    return reorderedLayers;
  }

  async duplicate(id: string, newProjectId?: string): Promise<Layer> {
    const originalLayer = await this.getById(id);
    if (!originalLayer) {
      throw new Error('Layer not found');
    }

    const duplicatedLayer = originalLayer.clone(newProjectId);
    return await this.create(duplicatedLayer);
  }

  async getOrderedByZIndex(projectId: string): Promise<Layer[]> {
    return this.getByProjectId(projectId, {
      sortBy: 'zIndex',
      sortOrder: 'asc',
    });
  }

  async updateVisibility(id: string, isVisible: boolean): Promise<Layer> {
    const layer = await this.getById(id);
    if (!layer) {
      throw new Error('Layer not found');
    }

    layer.setVisibility(isVisible);
    return await this.update(layer);
  }

  async updateLockStatus(id: string, isLocked: boolean): Promise<Layer> {
    const layer = await this.getById(id);
    if (!layer) {
      throw new Error('Layer not found');
    }

    layer.setLocked(isLocked);
    return await this.update(layer);
  }

  async batchUpdate(layers: Layer[]): Promise<Layer[]> {
    const updatedLayers: Layer[] = [];

    await this.db.transaction('rw', this.db.layers, async () => {
      for (const layer of layers) {
        await this.update(layer);
        updatedLayers.push(layer);
      }
    });

    return updatedLayers;
  }

  async search(projectId: string, searchTerm: string): Promise<Layer[]> {
    const layers = await this.getByProjectId(projectId);
    const lowerSearchTerm = searchTerm.toLowerCase();

    return layers.filter(layer =>
      layer.settings.name.toLowerCase().includes(lowerSearchTerm) ||
      layer.settings.description?.toLowerCase().includes(lowerSearchTerm)
    );
  }

  async getStats(projectId: string): Promise<{
    totalLayers: number;
    visibleLayers: number;
    lockedLayers: number;
    totalTraits: number;
  }> {
    const layers = await this.getByProjectId(projectId);

    return {
      totalLayers: layers.length,
      visibleLayers: layers.filter(l => l.settings.isVisible).length,
      lockedLayers: layers.filter(l => l.settings.isLocked).length,
      totalTraits: layers.reduce((sum, l) => sum + l.traitIds.length, 0),
    };
  }
}

export default IndexedDBLayerRepository;
