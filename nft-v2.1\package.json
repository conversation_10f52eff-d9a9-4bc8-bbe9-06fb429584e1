{"name": "nft-generator-pro-v2", "private": true, "version": "2.1.0", "type": "module", "description": "Professional NFT Generator with advanced features", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "lint:fix": "eslint . --fix", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "type-check": "tsc --noEmit"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.3.0", "@mui/material": "^6.3.0", "dexie": "^4.0.9", "fabric": "^6.5.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.1.1", "uuid": "^11.0.3", "zustand": "^5.0.2"}, "devDependencies": {"@eslint/js": "^9.25.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.4.1", "@vitest/coverage-v8": "^2.1.8", "@vitest/ui": "^2.1.8", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "husky": "^9.1.7", "jsdom": "^26.1.0", "lint-staged": "^15.3.0", "prettier": "^3.4.2", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vitest": "^2.1.8"}, "lint-staged": {"*.{ts,tsx}": ["eslint --fix", "prettier --write"]}}