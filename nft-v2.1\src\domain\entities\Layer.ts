import { v4 as uuidv4 } from 'uuid';

export interface LayerSettings {
  name: string;
  description?: string;
  isVisible: boolean;
  isLocked: boolean;
  opacity: number; // 0-100
  blendMode: 'normal' | 'multiply' | 'screen' | 'overlay';
  zIndex: number;
}

export interface LayerConstraints {
  maxRarity: number; // Maximum total rarity for this layer (0-100)
  minTraits: number; // Minimum number of traits that must be selected
  maxTraits: number; // Maximum number of traits that can be selected
  isRequired: boolean; // Whether this layer must have at least one trait
}

export interface TraitGroup {
  id: string;
  name: string;
  traitIds: string[];
  isCollapsed: boolean;
  parentGroupId?: string; // For nested groups
  order: number;
}

export class Layer {
  public readonly id: string;
  public readonly projectId: string;
  public settings: LayerSettings;
  public constraints: LayerConstraints;
  public traitIds: string[];
  public traitGroups: TraitGroup[];
  public folderPath?: string;
  public importedAt: Date;
  public lastModified: Date;

  constructor(
    projectId: string,
    settings: LayerSettings,
    id?: string
  ) {
    this.id = id || uuidv4();
    this.projectId = projectId;
    this.settings = settings;
    this.traitIds = [];
    this.traitGroups = [];
    
    const now = new Date();
    this.importedAt = now;
    this.lastModified = now;

    // Default constraints
    this.constraints = {
      maxRarity: 100,
      minTraits: 0,
      maxTraits: 1,
      isRequired: false,
    };
  }

  public updateSettings(newSettings: Partial<LayerSettings>): void {
    this.settings = { ...this.settings, ...newSettings };
    this.updateLastModified();
  }

  public updateConstraints(newConstraints: Partial<LayerConstraints>): void {
    this.constraints = { ...this.constraints, ...newConstraints };
    this.updateLastModified();
  }

  public addTrait(traitId: string): void {
    if (!this.traitIds.includes(traitId)) {
      this.traitIds.push(traitId);
      this.updateLastModified();
    }
  }

  public removeTrait(traitId: string): void {
    const index = this.traitIds.indexOf(traitId);
    if (index > -1) {
      this.traitIds.splice(index, 1);
      this.updateLastModified();
    }
  }

  public reorderTraits(newOrder: string[]): void {
    if (newOrder.length === this.traitIds.length) {
      this.traitIds = [...newOrder];
      this.updateLastModified();
    }
  }

  public setVisibility(isVisible: boolean): void {
    this.settings.isVisible = isVisible;
    this.updateLastModified();
  }

  public setLocked(isLocked: boolean): void {
    this.settings.isLocked = isLocked;
    this.updateLastModified();
  }

  public setOpacity(opacity: number): void {
    this.settings.opacity = Math.max(0, Math.min(100, opacity));
    this.updateLastModified();
  }

  public setZIndex(zIndex: number): void {
    this.settings.zIndex = zIndex;
    this.updateLastModified();
  }

  // Trait Group Management
  public addTraitGroup(group: Omit<TraitGroup, 'id'>): TraitGroup {
    const newGroup: TraitGroup = {
      ...group,
      id: uuidv4(),
    };
    this.traitGroups.push(newGroup);
    this.updateLastModified();
    return newGroup;
  }

  public removeTraitGroup(groupId: string): void {
    this.traitGroups = this.traitGroups.filter(g => g.id !== groupId);
    this.updateLastModified();
  }

  public updateTraitGroup(groupId: string, updates: Partial<TraitGroup>): void {
    const groupIndex = this.traitGroups.findIndex(g => g.id === groupId);
    if (groupIndex > -1) {
      this.traitGroups[groupIndex] = { ...this.traitGroups[groupIndex], ...updates };
      this.updateLastModified();
    }
  }

  public toggleGroupCollapse(groupId: string): void {
    const group = this.traitGroups.find(g => g.id === groupId);
    if (group) {
      group.isCollapsed = !group.isCollapsed;
      this.updateLastModified();
    }
  }

  public getGroupedTraits(): { ungrouped: string[], groups: TraitGroup[] } {
    const groupedTraitIds = new Set(
      this.traitGroups.flatMap(group => group.traitIds)
    );

    const ungrouped = this.traitIds.filter(traitId => !groupedTraitIds.has(traitId));

    return {
      ungrouped,
      groups: this.traitGroups.sort((a, b) => a.order - b.order)
    };
  }

  private updateLastModified(): void {
    this.lastModified = new Date();
  }

  public validate(): string[] {
    const errors: string[] = [];

    if (!this.settings.name.trim()) {
      errors.push('Layer name is required');
    }

    if (this.settings.opacity < 0 || this.settings.opacity > 100) {
      errors.push('Opacity must be between 0 and 100');
    }

    if (this.constraints.maxRarity < 0 || this.constraints.maxRarity > 100) {
      errors.push('Max rarity must be between 0 and 100');
    }

    if (this.constraints.minTraits < 0) {
      errors.push('Min traits cannot be negative');
    }

    if (this.constraints.maxTraits < this.constraints.minTraits) {
      errors.push('Max traits cannot be less than min traits');
    }

    return errors;
  }

  public clone(newProjectId?: string): Layer {
    const clonedSettings = {
      ...this.settings,
      name: `${this.settings.name} (Copy)`,
    };

    const cloned = new Layer(newProjectId || this.projectId, clonedSettings);
    cloned.constraints = { ...this.constraints };
    cloned.traitIds = [...this.traitIds];
    cloned.traitGroups = this.traitGroups.map(group => ({ ...group }));
    cloned.folderPath = this.folderPath;
    
    return cloned;
  }

  public toJSON(): any {
    return {
      id: this.id,
      projectId: this.projectId,
      settings: this.settings,
      constraints: this.constraints,
      traitIds: this.traitIds,
      traitGroups: this.traitGroups,
      folderPath: this.folderPath,
      importedAt: this.importedAt.toISOString(),
      lastModified: this.lastModified.toISOString(),
    };
  }

  public static fromJSON(data: any): Layer {
    const layer = new Layer(data.projectId, data.settings, data.id);
    layer.constraints = data.constraints;
    layer.traitIds = data.traitIds || [];
    layer.traitGroups = data.traitGroups || [];
    layer.folderPath = data.folderPath;
    layer.importedAt = new Date(data.importedAt);
    layer.lastModified = new Date(data.lastModified);

    return layer;
  }
}

export default Layer;
