import { useState, useCallback } from 'react';
import { useProjectStore } from '../stores/useProjectStore';

export interface UseSimpleLayerImportResult {
  isImporting: boolean;
  error: string | null;
  importFromFiles: (files: File[]) => Promise<void>;
  clearError: () => void;
}

export const useSimpleLayerImport = (): UseSimpleLayerImportResult => {
  const [isImporting, setIsImporting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { currentProject, addLayer, addTrait } = useProjectStore();

  const importFromFiles = useCallback(async (files: File[]) => {
    if (!currentProject) {
      setError('No project selected');
      return;
    }

    if (files.length === 0) {
      setError('No files selected');
      return;
    }

    try {
      setIsImporting(true);
      setError(null);

      // Group files by folder structure
      const layerGroups: Record<string, File[]> = {};

      for (const file of files) {
        if (!isImageFile(file)) continue;

        // Parse file path to extract layer name
        const pathParts = file.webkitRelativePath?.split('/') || [file.name];

        if (pathParts.length < 2) {
          // File is in root, create default layer
          const layerName = 'Default Layer';
          if (!layerGroups[layerName]) {
            layerGroups[layerName] = [];
          }
          layerGroups[layerName].push(file);
        } else {
          // Skip the root folder name, use the first subfolder as layer name
          // For example: "MyProject/Background/sky.png" -> layer name is "Background"
          let layerName: string;

          if (pathParts.length === 2) {
            // Direct files in root folder: "MyProject/file.png" -> "Default Layer"
            layerName = 'Default Layer';
          } else {
            // Files in subfolders: "MyProject/Background/sky.png" -> "Background"
            layerName = pathParts[1];
          }

          if (!layerGroups[layerName]) {
            layerGroups[layerName] = [];
          }
          layerGroups[layerName].push(file);
        }
      }

      // Create layers and traits
      let layerIndex = 0;
      for (const [layerName, layerFiles] of Object.entries(layerGroups)) {
        // Extract order from layer name if it follows pattern "1-Background"
        const orderMatch = layerName.match(/^(\d+)-(.+)$/);
        const actualOrder = orderMatch ? parseInt(orderMatch[1], 10) : layerIndex;
        const cleanName = orderMatch ? orderMatch[2] : layerName;

        // Create layer
        const layer = {
          id: `layer-${Date.now()}-${layerIndex}`,
          projectId: currentProject.id,
          settings: {
            name: cleanName,
            description: `Imported from folder: ${layerName}`,
            isVisible: true,
            isLocked: false,
            opacity: 100,
            blendMode: 'normal' as const,
            zIndex: actualOrder,
          },
          constraints: {
            maxRarity: 100,
            minTraits: 0,
            maxTraits: 1,
            isRequired: false,
          },
          traitIds: [] as string[],
          importedAt: new Date(),
          lastModified: new Date(),
        };

        // Create traits for this layer
        const traitIds: string[] = [];
        const rarityPerTrait = layerFiles.length > 0 ? 100 / layerFiles.length : 0;

        for (let i = 0; i < layerFiles.length; i++) {
          const file = layerFiles[i];
          const traitId = `trait-${Date.now()}-${layerIndex}-${i}`;
          
          // Create trait
          const trait = {
            id: traitId,
            layerId: layer.id,
            projectId: currentProject.id,
            metadata: {
              name: cleanFileName(file.name),
              description: `Imported from: ${file.name}`,
              category: cleanName,
              tags: [],
              attributes: {},
            },
            file: {
              name: file.name,
              path: file.webkitRelativePath || file.name,
              size: file.size,
              type: file.type,
              lastModified: new Date(file.lastModified),
              blob: file,
              dataUrl: await fileToDataUrl(file),
            },
            rarity: {
              value: rarityPerTrait,
              isLocked: false,
              weight: 1,
            },
            isEnabled: true,
            importedAt: new Date(),
            lastModified: new Date(),
          };

          traitIds.push(traitId);
          addTrait(trait);
        }

        layer.traitIds = traitIds;
        addLayer(layer);
        layerIndex++;
      }

    } catch (err) {
      if (err instanceof Error) {
        setError(`Import failed: ${err.message}`);
      } else {
        setError('Import failed: Unknown error');
      }
    } finally {
      setIsImporting(false);
    }
  }, [currentProject, addLayer, addTrait]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    isImporting,
    error,
    importFromFiles,
    clearError,
  };
};

// Helper functions
const isImageFile = (file: File): boolean => {
  const supportedTypes = [
    'image/png',
    'image/jpeg',
    'image/jpg',
    'image/webp',
    'image/gif',
    'image/svg+xml'
  ];
  return supportedTypes.includes(file.type.toLowerCase());
};

const cleanFileName = (fileName: string): string => {
  // Remove extension and clean up the name
  const nameWithoutExt = fileName.replace(/\.[^/.]+$/, '');
  return nameWithoutExt
    .replace(/[-_]/g, ' ')
    .replace(/\b\w/g, l => l.toUpperCase())
    .trim();
};

const fileToDataUrl = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
};

export default useSimpleLayerImport;
