import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { Project } from '../../domain/entities/Project';
import { Layer } from '../../domain/entities/Layer';
import { Trait } from '../../domain/entities/Trait';

export interface ProjectState {
  // Current project
  currentProject: Project | null;
  
  // Collections
  projects: Project[];
  layers: Layer[];
  traits: Trait[];
  
  // UI State
  isLoading: boolean;
  error: string | null;
  
  // Selection state
  selectedLayerId: string | null;
  selectedTraitIds: string[];
  
  // View state
  viewMode: 'grid' | 'list';
  previewMode: 'single' | 'batch';
}

export interface ProjectActions {
  // Project actions
  setCurrentProject: (project: Project | null) => void;
  addProject: (project: Project) => void;
  updateProject: (project: Project) => void;
  removeProject: (projectId: string) => void;
  
  // Layer actions
  addLayer: (layer: Layer) => void;
  updateLayer: (layer: Layer) => void;
  removeLayer: (layerId: string) => void;
  reorderLayers: (layerIds: string[]) => void;
  
  // Trait actions
  addTrait: (trait: Trait) => void;
  updateTrait: (trait: Trait) => void;
  removeTrait: (traitId: string) => void;
  batchUpdateTraits: (traits: Trait[]) => void;
  
  // Selection actions
  selectLayer: (layerId: string | null) => void;
  selectTrait: (traitId: string) => void;
  selectMultipleTraits: (traitIds: string[]) => void;
  clearSelection: () => void;
  
  // UI actions
  setLoading: (isLoading: boolean) => void;
  setError: (error: string | null) => void;
  setViewMode: (mode: 'grid' | 'list') => void;
  setPreviewMode: (mode: 'single' | 'batch') => void;
  
  // Utility actions
  reset: () => void;
  loadProjectData: (projectId: string) => void;
  loadLayerTraits: (layerId: string) => Promise<void>;
}

export type ProjectStore = ProjectState & ProjectActions;

const initialState: ProjectState = {
  currentProject: null,
  projects: [],
  layers: [],
  traits: [],
  isLoading: false,
  error: null,
  selectedLayerId: null,
  selectedTraitIds: [],
  viewMode: 'grid',
  previewMode: 'single',
};

export const useProjectStore = create<ProjectStore>()(
  devtools(
    (set, get) => ({
      ...initialState,

      // Project actions
      setCurrentProject: (project) => {
        set({ currentProject: project }, false, 'setCurrentProject');
        if (project) {
          get().loadProjectData(project.id);
        } else {
          set({ layers: [], traits: [], selectedLayerId: null, selectedTraitIds: [] });
        }
      },

      addProject: (project) => {
        set(
          (state) => ({
            projects: [...state.projects, project],
          }),
          false,
          'addProject'
        );
      },

      updateProject: (project) => {
        set(
          (state) => ({
            projects: state.projects.map((p) => (p.id === project.id ? project : p)),
            currentProject: state.currentProject?.id === project.id ? project : state.currentProject,
          }),
          false,
          'updateProject'
        );
      },

      removeProject: (projectId) => {
        set(
          (state) => ({
            projects: state.projects.filter((p) => p.id !== projectId),
            currentProject: state.currentProject?.id === projectId ? null : state.currentProject,
            layers: state.currentProject?.id === projectId ? [] : state.layers,
            traits: state.currentProject?.id === projectId ? [] : state.traits,
          }),
          false,
          'removeProject'
        );
      },

      // Layer actions
      addLayer: (layer) => {
        set(
          (state) => ({
            layers: [...state.layers, layer],
          }),
          false,
          'addLayer'
        );
      },

      updateLayer: (layer) => {
        set(
          (state) => ({
            layers: state.layers.map((l) => (l.id === layer.id ? layer : l)),
          }),
          false,
          'updateLayer'
        );
      },

      removeLayer: (layerId) => {
        set(
          (state) => ({
            layers: state.layers.filter((l) => l.id !== layerId),
            traits: state.traits.filter((t) => t.layerId !== layerId),
            selectedLayerId: state.selectedLayerId === layerId ? null : state.selectedLayerId,
          }),
          false,
          'removeLayer'
        );
      },

      reorderLayers: (layerIds) => {
        set(
          (state) => {
            const reorderedLayers = layerIds
              .map((id) => state.layers.find((l) => l.id === id))
              .filter(Boolean) as Layer[];
            
            return { layers: reorderedLayers };
          },
          false,
          'reorderLayers'
        );
      },

      // Trait actions
      addTrait: (trait) => {
        set(
          (state) => ({
            traits: [...state.traits, trait],
          }),
          false,
          'addTrait'
        );
      },

      updateTrait: (trait) => {
        set(
          (state) => ({
            traits: state.traits.map((t) => (t.id === trait.id ? trait : t)),
          }),
          false,
          'updateTrait'
        );
      },

      removeTrait: (traitId) => {
        set(
          (state) => ({
            traits: state.traits.filter((t) => t.id !== traitId),
            selectedTraitIds: state.selectedTraitIds.filter((id) => id !== traitId),
          }),
          false,
          'removeTrait'
        );
      },

      batchUpdateTraits: (traits) => {
        set(
          (state) => {
            const updatedTraits = [...state.traits];
            traits.forEach((trait) => {
              const index = updatedTraits.findIndex((t) => t.id === trait.id);
              if (index !== -1) {
                updatedTraits[index] = trait;
              }
            });
            return { traits: updatedTraits };
          },
          false,
          'batchUpdateTraits'
        );
      },

      // Selection actions
      selectLayer: (layerId) => {
        set({ selectedLayerId: layerId, selectedTraitIds: [] }, false, 'selectLayer');
      },

      selectTrait: (traitId) => {
        set({ selectedTraitIds: [traitId] }, false, 'selectTrait');
      },

      selectMultipleTraits: (traitIds) => {
        set({ selectedTraitIds: traitIds }, false, 'selectMultipleTraits');
      },

      clearSelection: () => {
        set({ selectedLayerId: null, selectedTraitIds: [] }, false, 'clearSelection');
      },

      // UI actions
      setLoading: (isLoading) => {
        set({ isLoading }, false, 'setLoading');
      },

      setError: (error) => {
        set({ error }, false, 'setError');
      },

      setViewMode: (viewMode) => {
        set({ viewMode }, false, 'setViewMode');
      },

      setPreviewMode: (previewMode) => {
        set({ previewMode }, false, 'setPreviewMode');
      },

      // Utility actions
      reset: () => {
        set(initialState, false, 'reset');
      },

      loadProjectData: (projectId) => {
        // This will be implemented with repository integration
        console.log('Loading project data for:', projectId);
      },

      loadLayerTraits: async (layerId) => {
        try {
          // This will be implemented with repository integration
          console.log('Loading traits for layer:', layerId);
          // For now, just update the selected layer
          set({ selectedLayerId: layerId });
        } catch (error) {
          console.error('Failed to load layer traits:', error);
          set({ error: 'Failed to load layer traits' });
        }
      },
    }),
    {
      name: 'project-store',
    }
  )
);

export default useProjectStore;
