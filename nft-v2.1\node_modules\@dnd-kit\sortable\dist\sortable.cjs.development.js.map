{"version": 3, "file": "sortable.cjs.development.js", "sources": ["../src/utilities/arrayMove.ts", "../src/utilities/arraySwap.ts", "../src/utilities/getSortedRects.ts", "../src/utilities/isValidIndex.ts", "../src/utilities/itemsEqual.ts", "../src/utilities/normalizeDisabled.ts", "../src/strategies/horizontalListSorting.ts", "../src/strategies/rectSorting.ts", "../src/strategies/rectSwapping.ts", "../src/strategies/verticalListSorting.ts", "../src/components/SortableContext.tsx", "../src/hooks/defaults.ts", "../src/hooks/utilities/useDerivedTransform.ts", "../src/hooks/useSortable.ts", "../src/types/type-guard.ts", "../src/sensors/keyboard/sortableKeyboardCoordinates.ts"], "sourcesContent": ["/**\n * Move an array item to a different position. Returns a new array with the item moved to the new position.\n */\nexport function arrayMove<T>(array: T[], from: number, to: number): T[] {\n  const newArray = array.slice();\n  newArray.splice(\n    to < 0 ? newArray.length + to : to,\n    0,\n    newArray.splice(from, 1)[0]\n  );\n\n  return newArray;\n}\n", "/**\n * Swap an array item to a different position. Returns a new array with the item swapped to the new position.\n */\nexport function arraySwap<T>(array: T[], from: number, to: number): T[] {\n  const newArray = array.slice();\n\n  newArray[from] = array[to];\n  newArray[to] = array[from];\n\n  return newArray;\n}\n", "import type {\n  ClientRect,\n  UniqueIdentifier,\n  UseDndContextReturnValue,\n} from '@dnd-kit/core';\n\nexport function getSortedRects(\n  items: UniqueIdentifier[],\n  rects: UseDndContextReturnValue['droppableRects']\n) {\n  return items.reduce<ClientRect[]>((accumulator, id, index) => {\n    const rect = rects.get(id);\n\n    if (rect) {\n      accumulator[index] = rect;\n    }\n\n    return accumulator;\n  }, Array(items.length));\n}\n", "export function isValidIndex(index: number | null): index is number {\n  return index !== null && index >= 0;\n}\n", "import type {UniqueIdentifier} from '@dnd-kit/core';\n\nexport function itemsEqual(a: UniqueIdentifier[], b: UniqueIdentifier[]) {\n  if (a === b) {\n    return true;\n  }\n\n  if (a.length !== b.length) {\n    return false;\n  }\n\n  for (let i = 0; i < a.length; i++) {\n    if (a[i] !== b[i]) {\n      return false;\n    }\n  }\n\n  return true;\n}\n", "import type {Disabled} from '../types';\n\nexport function normalizeDisabled(disabled: boolean | Disabled): Disabled {\n  if (typeof disabled === 'boolean') {\n    return {\n      draggable: disabled,\n      droppable: disabled,\n    };\n  }\n\n  return disabled;\n}\n", "import type {ClientRect} from '@dnd-kit/core';\nimport type {SortingStrategy} from '../types';\n\n// To-do: We should be calculating scale transformation\nconst defaultScale = {\n  scaleX: 1,\n  scaleY: 1,\n};\n\nexport const horizontalListSortingStrategy: SortingStrategy = ({\n  rects,\n  activeNodeRect: fallbackActiveRect,\n  activeIndex,\n  overIndex,\n  index,\n}) => {\n  const activeNodeRect = rects[activeIndex] ?? fallbackActiveRect;\n\n  if (!activeNodeRect) {\n    return null;\n  }\n\n  const itemGap = getItemGap(rects, index, activeIndex);\n\n  if (index === activeIndex) {\n    const newIndexRect = rects[overIndex];\n\n    if (!newIndexRect) {\n      return null;\n    }\n\n    return {\n      x:\n        activeIndex < overIndex\n          ? newIndexRect.left +\n            newIndexRect.width -\n            (activeNodeRect.left + activeNodeRect.width)\n          : newIndexRect.left - activeNodeRect.left,\n      y: 0,\n      ...defaultScale,\n    };\n  }\n\n  if (index > activeIndex && index <= overIndex) {\n    return {\n      x: -activeNodeRect.width - itemGap,\n      y: 0,\n      ...defaultScale,\n    };\n  }\n\n  if (index < activeIndex && index >= overIndex) {\n    return {\n      x: activeNodeRect.width + itemGap,\n      y: 0,\n      ...defaultScale,\n    };\n  }\n\n  return {\n    x: 0,\n    y: 0,\n    ...defaultScale,\n  };\n};\n\nfunction getItemGap(rects: ClientRect[], index: number, activeIndex: number) {\n  const currentRect: ClientRect | undefined = rects[index];\n  const previousRect: ClientRect | undefined = rects[index - 1];\n  const nextRect: ClientRect | undefined = rects[index + 1];\n\n  if (!currentRect || (!previousRect && !nextRect)) {\n    return 0;\n  }\n\n  if (activeIndex < index) {\n    return previousRect\n      ? currentRect.left - (previousRect.left + previousRect.width)\n      : nextRect.left - (currentRect.left + currentRect.width);\n  }\n\n  return nextRect\n    ? nextRect.left - (currentRect.left + currentRect.width)\n    : currentRect.left - (previousRect.left + previousRect.width);\n}\n", "import {arrayMove} from '../utilities';\nimport type {SortingStrategy} from '../types';\n\nexport const rectSortingStrategy: SortingStrategy = ({\n  rects,\n  activeIndex,\n  overIndex,\n  index,\n}) => {\n  const newRects = arrayMove(rects, overIndex, activeIndex);\n\n  const oldRect = rects[index];\n  const newRect = newRects[index];\n\n  if (!newRect || !oldRect) {\n    return null;\n  }\n\n  return {\n    x: newRect.left - oldRect.left,\n    y: newRect.top - oldRect.top,\n    scaleX: newRect.width / oldRect.width,\n    scaleY: newRect.height / oldRect.height,\n  };\n};\n", "import type {SortingStrategy} from '../types';\n\nexport const rectSwappingStrategy: SortingStrategy = ({\n  activeIndex,\n  index,\n  rects,\n  overIndex,\n}) => {\n  let oldRect;\n  let newRect;\n\n  if (index === activeIndex) {\n    oldRect = rects[index];\n    newRect = rects[overIndex];\n  }\n\n  if (index === overIndex) {\n    oldRect = rects[index];\n    newRect = rects[activeIndex];\n  }\n\n  if (!newRect || !oldRect) {\n    return null;\n  }\n\n  return {\n    x: newRect.left - oldRect.left,\n    y: newRect.top - oldRect.top,\n    scaleX: newRect.width / oldRect.width,\n    scaleY: newRect.height / oldRect.height,\n  };\n};\n", "import type {ClientRect} from '@dnd-kit/core';\nimport type {SortingStrategy} from '../types';\n\n// To-do: We should be calculating scale transformation\nconst defaultScale = {\n  scaleX: 1,\n  scaleY: 1,\n};\n\nexport const verticalListSortingStrategy: SortingStrategy = ({\n  activeIndex,\n  activeNodeRect: fallbackActiveRect,\n  index,\n  rects,\n  overIndex,\n}) => {\n  const activeNodeRect = rects[activeIndex] ?? fallbackActiveRect;\n\n  if (!activeNodeRect) {\n    return null;\n  }\n\n  if (index === activeIndex) {\n    const overIndexRect = rects[overIndex];\n\n    if (!overIndexRect) {\n      return null;\n    }\n\n    return {\n      x: 0,\n      y:\n        activeIndex < overIndex\n          ? overIndexRect.top +\n            overIndexRect.height -\n            (activeNodeRect.top + activeNodeRect.height)\n          : overIndexRect.top - activeNodeRect.top,\n      ...defaultScale,\n    };\n  }\n\n  const itemGap = getItemGap(rects, index, activeIndex);\n\n  if (index > activeIndex && index <= overIndex) {\n    return {\n      x: 0,\n      y: -activeNodeRect.height - itemGap,\n      ...defaultScale,\n    };\n  }\n\n  if (index < activeIndex && index >= overIndex) {\n    return {\n      x: 0,\n      y: activeNodeRect.height + itemGap,\n      ...defaultScale,\n    };\n  }\n\n  return {\n    x: 0,\n    y: 0,\n    ...defaultScale,\n  };\n};\n\nfunction getItemGap(\n  clientRects: ClientRect[],\n  index: number,\n  activeIndex: number\n) {\n  const currentRect: ClientRect | undefined = clientRects[index];\n  const previousRect: ClientRect | undefined = clientRects[index - 1];\n  const nextRect: ClientRect | undefined = clientRects[index + 1];\n\n  if (!currentRect) {\n    return 0;\n  }\n\n  if (activeIndex < index) {\n    return previousRect\n      ? currentRect.top - (previousRect.top + previousRect.height)\n      : nextRect\n      ? nextRect.top - (currentRect.top + currentRect.height)\n      : 0;\n  }\n\n  return nextRect\n    ? nextRect.top - (currentRect.top + currentRect.height)\n    : previousRect\n    ? currentRect.top - (previousRect.top + previousRect.height)\n    : 0;\n}\n", "import React, {useEffect, useMemo, useRef} from 'react';\nimport {useDndContext, ClientRect, UniqueIdentifier} from '@dnd-kit/core';\nimport {useIsomorphicLayoutEffect, useUniqueId} from '@dnd-kit/utilities';\n\nimport type {Disabled, SortingStrategy} from '../types';\nimport {getSortedRects, itemsEqual, normalizeDisabled} from '../utilities';\nimport {rectSortingStrategy} from '../strategies';\n\nexport interface Props {\n  children: React.ReactNode;\n  items: (UniqueIdentifier | {id: UniqueIdentifier})[];\n  strategy?: SortingStrategy;\n  id?: string;\n  disabled?: boolean | Disabled;\n}\n\nconst ID_PREFIX = 'Sortable';\n\ninterface ContextDescriptor {\n  activeIndex: number;\n  containerId: string;\n  disabled: Disabled;\n  disableTransforms: boolean;\n  items: UniqueIdentifier[];\n  overIndex: number;\n  useDragOverlay: boolean;\n  sortedRects: ClientRect[];\n  strategy: SortingStrategy;\n}\n\nexport const Context = React.createContext<ContextDescriptor>({\n  activeIndex: -1,\n  containerId: ID_PREFIX,\n  disableTransforms: false,\n  items: [],\n  overIndex: -1,\n  useDragOverlay: false,\n  sortedRects: [],\n  strategy: rectSortingStrategy,\n  disabled: {\n    draggable: false,\n    droppable: false,\n  },\n});\n\nexport function SortableContext({\n  children,\n  id,\n  items: userDefinedItems,\n  strategy = rectSortingStrategy,\n  disabled: disabledProp = false,\n}: Props) {\n  const {\n    active,\n    dragOverlay,\n    droppableRects,\n    over,\n    measureDroppableContainers,\n  } = useDndContext();\n  const containerId = useUniqueId(ID_PREFIX, id);\n  const useDragOverlay = Boolean(dragOverlay.rect !== null);\n  const items = useMemo<UniqueIdentifier[]>(\n    () =>\n      userDefinedItems.map((item) =>\n        typeof item === 'object' && 'id' in item ? item.id : item\n      ),\n    [userDefinedItems]\n  );\n  const isDragging = active != null;\n  const activeIndex = active ? items.indexOf(active.id) : -1;\n  const overIndex = over ? items.indexOf(over.id) : -1;\n  const previousItemsRef = useRef(items);\n  const itemsHaveChanged = !itemsEqual(items, previousItemsRef.current);\n  const disableTransforms =\n    (overIndex !== -1 && activeIndex === -1) || itemsHaveChanged;\n  const disabled = normalizeDisabled(disabledProp);\n\n  useIsomorphicLayoutEffect(() => {\n    if (itemsHaveChanged && isDragging) {\n      measureDroppableContainers(items);\n    }\n  }, [itemsHaveChanged, items, isDragging, measureDroppableContainers]);\n\n  useEffect(() => {\n    previousItemsRef.current = items;\n  }, [items]);\n\n  const contextValue = useMemo(\n    (): ContextDescriptor => ({\n      activeIndex,\n      containerId,\n      disabled,\n      disableTransforms,\n      items,\n      overIndex,\n      useDragOverlay,\n      sortedRects: getSortedRects(items, droppableRects),\n      strategy,\n    }),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n      activeIndex,\n      containerId,\n      disabled.draggable,\n      disabled.droppable,\n      disableTransforms,\n      items,\n      overIndex,\n      droppableRects,\n      useDragOverlay,\n      strategy,\n    ]\n  );\n\n  return <Context.Provider value={contextValue}>{children}</Context.Provider>;\n}\n", "import {CSS} from '@dnd-kit/utilities';\n\nimport {arrayMove} from '../utilities';\n\nimport type {\n  AnimateLayoutChanges,\n  NewIndexGetter,\n  SortableTransition,\n} from './types';\n\nexport const defaultNewIndexGetter: NewIndexGetter = ({\n  id,\n  items,\n  activeIndex,\n  overIndex,\n}) => arrayMove(items, activeIndex, overIndex).indexOf(id);\n\nexport const defaultAnimateLayoutChanges: AnimateLayoutChanges = ({\n  containerId,\n  isSorting,\n  wasDragging,\n  index,\n  items,\n  newIndex,\n  previousItems,\n  previousContainerId,\n  transition,\n}) => {\n  if (!transition || !wasDragging) {\n    return false;\n  }\n\n  if (previousItems !== items && index === newIndex) {\n    return false;\n  }\n\n  if (isSorting) {\n    return true;\n  }\n\n  return newIndex !== index && containerId === previousContainerId;\n};\n\nexport const defaultTransition: SortableTransition = {\n  duration: 200,\n  easing: 'ease',\n};\n\nexport const transitionProperty = 'transform';\n\nexport const disabledTransition = CSS.Transition.toString({\n  property: transitionProperty,\n  duration: 0,\n  easing: 'linear',\n});\n\nexport const defaultAttributes = {\n  roleDescription: 'sortable',\n};\n", "import {useEffect, useRef, useState} from 'react';\nimport {getClientRect, ClientRect} from '@dnd-kit/core';\nimport {Transform, useIsomorphicLayoutEffect} from '@dnd-kit/utilities';\n\ninterface Arguments {\n  rect: React.MutableRefObject<ClientRect | null>;\n  disabled: boolean;\n  index: number;\n  node: React.MutableRefObject<HTMLElement | null>;\n}\n\n/*\n * When the index of an item changes while sorting,\n * we need to temporarily disable the transforms\n */\nexport function useDerivedTransform({disabled, index, node, rect}: Arguments) {\n  const [derivedTransform, setDerivedtransform] = useState<Transform | null>(\n    null\n  );\n  const previousIndex = useRef(index);\n\n  useIsomorphicLayoutEffect(() => {\n    if (!disabled && index !== previousIndex.current && node.current) {\n      const initial = rect.current;\n\n      if (initial) {\n        const current = getClientRect(node.current, {\n          ignoreTransform: true,\n        });\n\n        const delta = {\n          x: initial.left - current.left,\n          y: initial.top - current.top,\n          scaleX: initial.width / current.width,\n          scaleY: initial.height / current.height,\n        };\n\n        if (delta.x || delta.y) {\n          setDerivedtransform(delta);\n        }\n      }\n    }\n\n    if (index !== previousIndex.current) {\n      previousIndex.current = index;\n    }\n  }, [disabled, index, node, rect]);\n\n  useEffect(() => {\n    if (derivedTransform) {\n      setDerivedtransform(null);\n    }\n  }, [derivedTransform]);\n\n  return derivedTransform;\n}\n", "import {useContext, useEffect, useMemo, useRef} from 'react';\nimport {\n  useDraggable,\n  useDroppable,\n  UseDraggableArguments,\n  UseDroppableArguments,\n} from '@dnd-kit/core';\nimport type {Data} from '@dnd-kit/core';\nimport {CSS, isKeyboardEvent, useCombinedRefs} from '@dnd-kit/utilities';\n\nimport {Context} from '../components';\nimport type {Disabled, SortableData, SortingStrategy} from '../types';\nimport {isValidIndex} from '../utilities';\nimport {\n  defaultAnimateLayoutChanges,\n  defaultAttributes,\n  defaultNewIndexGetter,\n  defaultTransition,\n  disabledTransition,\n  transitionProperty,\n} from './defaults';\nimport type {\n  AnimateLayoutChanges,\n  NewIndexGetter,\n  SortableTransition,\n} from './types';\nimport {useDerivedTransform} from './utilities';\n\nexport interface Arguments\n  extends Omit<UseDraggableArguments, 'disabled'>,\n    Pick<UseDroppableArguments, 'resizeObserverConfig'> {\n  animateLayoutChanges?: AnimateLayoutChanges;\n  disabled?: boolean | Disabled;\n  getNewIndex?: NewIndexGetter;\n  strategy?: SortingStrategy;\n  transition?: SortableTransition | null;\n}\n\nexport function useSortable({\n  animateLayoutChanges = defaultAnimateLayoutChanges,\n  attributes: userDefinedAttributes,\n  disabled: localDisabled,\n  data: customData,\n  getNewIndex = defaultNewIndexGetter,\n  id,\n  strategy: localStrategy,\n  resizeObserverConfig,\n  transition = defaultTransition,\n}: Arguments) {\n  const {\n    items,\n    containerId,\n    activeIndex,\n    disabled: globalDisabled,\n    disableTransforms,\n    sortedRects,\n    overIndex,\n    useDragOverlay,\n    strategy: globalStrategy,\n  } = useContext(Context);\n  const disabled: Disabled = normalizeLocalDisabled(\n    localDisabled,\n    globalDisabled\n  );\n  const index = items.indexOf(id);\n  const data = useMemo<SortableData & Data>(\n    () => ({sortable: {containerId, index, items}, ...customData}),\n    [containerId, customData, index, items]\n  );\n  const itemsAfterCurrentSortable = useMemo(\n    () => items.slice(items.indexOf(id)),\n    [items, id]\n  );\n  const {\n    rect,\n    node,\n    isOver,\n    setNodeRef: setDroppableNodeRef,\n  } = useDroppable({\n    id,\n    data,\n    disabled: disabled.droppable,\n    resizeObserverConfig: {\n      updateMeasurementsFor: itemsAfterCurrentSortable,\n      ...resizeObserverConfig,\n    },\n  });\n  const {\n    active,\n    activatorEvent,\n    activeNodeRect,\n    attributes,\n    setNodeRef: setDraggableNodeRef,\n    listeners,\n    isDragging,\n    over,\n    setActivatorNodeRef,\n    transform,\n  } = useDraggable({\n    id,\n    data,\n    attributes: {\n      ...defaultAttributes,\n      ...userDefinedAttributes,\n    },\n    disabled: disabled.draggable,\n  });\n  const setNodeRef = useCombinedRefs(setDroppableNodeRef, setDraggableNodeRef);\n  const isSorting = Boolean(active);\n  const displaceItem =\n    isSorting &&\n    !disableTransforms &&\n    isValidIndex(activeIndex) &&\n    isValidIndex(overIndex);\n  const shouldDisplaceDragSource = !useDragOverlay && isDragging;\n  const dragSourceDisplacement =\n    shouldDisplaceDragSource && displaceItem ? transform : null;\n  const strategy = localStrategy ?? globalStrategy;\n  const finalTransform = displaceItem\n    ? dragSourceDisplacement ??\n      strategy({\n        rects: sortedRects,\n        activeNodeRect,\n        activeIndex,\n        overIndex,\n        index,\n      })\n    : null;\n  const newIndex =\n    isValidIndex(activeIndex) && isValidIndex(overIndex)\n      ? getNewIndex({id, items, activeIndex, overIndex})\n      : index;\n  const activeId = active?.id;\n  const previous = useRef({\n    activeId,\n    items,\n    newIndex,\n    containerId,\n  });\n  const itemsHaveChanged = items !== previous.current.items;\n  const shouldAnimateLayoutChanges = animateLayoutChanges({\n    active,\n    containerId,\n    isDragging,\n    isSorting,\n    id,\n    index,\n    items,\n    newIndex: previous.current.newIndex,\n    previousItems: previous.current.items,\n    previousContainerId: previous.current.containerId,\n    transition,\n    wasDragging: previous.current.activeId != null,\n  });\n\n  const derivedTransform = useDerivedTransform({\n    disabled: !shouldAnimateLayoutChanges,\n    index,\n    node,\n    rect,\n  });\n\n  useEffect(() => {\n    if (isSorting && previous.current.newIndex !== newIndex) {\n      previous.current.newIndex = newIndex;\n    }\n\n    if (containerId !== previous.current.containerId) {\n      previous.current.containerId = containerId;\n    }\n\n    if (items !== previous.current.items) {\n      previous.current.items = items;\n    }\n  }, [isSorting, newIndex, containerId, items]);\n\n  useEffect(() => {\n    if (activeId === previous.current.activeId) {\n      return;\n    }\n\n    if (activeId != null && previous.current.activeId == null) {\n      previous.current.activeId = activeId;\n      return;\n    }\n\n    const timeoutId = setTimeout(() => {\n      previous.current.activeId = activeId;\n    }, 50);\n\n    return () => clearTimeout(timeoutId);\n  }, [activeId]);\n\n  return {\n    active,\n    activeIndex,\n    attributes,\n    data,\n    rect,\n    index,\n    newIndex,\n    items,\n    isOver,\n    isSorting,\n    isDragging,\n    listeners,\n    node,\n    overIndex,\n    over,\n    setNodeRef,\n    setActivatorNodeRef,\n    setDroppableNodeRef,\n    setDraggableNodeRef,\n    transform: derivedTransform ?? finalTransform,\n    transition: getTransition(),\n  };\n\n  function getTransition() {\n    if (\n      // Temporarily disable transitions for a single frame to set up derived transforms\n      derivedTransform ||\n      // Or to prevent items jumping to back to their \"new\" position when items change\n      (itemsHaveChanged && previous.current.newIndex === index)\n    ) {\n      return disabledTransition;\n    }\n\n    if (\n      (shouldDisplaceDragSource && !isKeyboardEvent(activatorEvent)) ||\n      !transition\n    ) {\n      return undefined;\n    }\n\n    if (isSorting || shouldAnimateLayoutChanges) {\n      return CSS.Transition.toString({\n        ...transition,\n        property: transitionProperty,\n      });\n    }\n\n    return undefined;\n  }\n}\n\nfunction normalizeLocalDisabled(\n  localDisabled: Arguments['disabled'],\n  globalDisabled: Disabled\n) {\n  if (typeof localDisabled === 'boolean') {\n    return {\n      draggable: localDisabled,\n      // Backwards compatibility\n      droppable: false,\n    };\n  }\n\n  return {\n    draggable: localDisabled?.draggable ?? globalDisabled.draggable,\n    droppable: localDisabled?.droppable ?? globalDisabled.droppable,\n  };\n}\n", "import type {\n  Active,\n  Data,\n  DroppableContainer,\n  DraggableNode,\n  Over,\n} from '@dnd-kit/core';\n\nimport type {SortableData} from './data';\n\nexport function hasSortableData<\n  T extends Active | Over | DraggableNode | DroppableContainer\n>(\n  entry: T | null | undefined\n): entry is T & {data: {current: Data<SortableData>}} {\n  if (!entry) {\n    return false;\n  }\n\n  const data = entry.data.current;\n\n  if (\n    data &&\n    'sortable' in data &&\n    typeof data.sortable === 'object' &&\n    'containerId' in data.sortable &&\n    'items' in data.sortable &&\n    'index' in data.sortable\n  ) {\n    return true;\n  }\n\n  return false;\n}\n", "import {\n  closestCorners,\n  getScrollableAncestors,\n  getFirstCollision,\n  KeyboardCode,\n  DroppableContainer,\n  KeyboardCoordinateGetter,\n} from '@dnd-kit/core';\nimport {subtract} from '@dnd-kit/utilities';\n\nimport {hasSortableData} from '../../types';\n\nconst directions: string[] = [\n  KeyboardCode.Down,\n  KeyboardCode.Right,\n  KeyboardCode.Up,\n  KeyboardCode.Left,\n];\n\nexport const sortableKeyboardCoordinates: KeyboardCoordinateGetter = (\n  event,\n  {\n    context: {\n      active,\n      collisionRect,\n      droppableRects,\n      droppableContainers,\n      over,\n      scrollableAncestors,\n    },\n  }\n) => {\n  if (directions.includes(event.code)) {\n    event.preventDefault();\n\n    if (!active || !collisionRect) {\n      return;\n    }\n\n    const filteredContainers: DroppableContainer[] = [];\n\n    droppableContainers.getEnabled().forEach((entry) => {\n      if (!entry || entry?.disabled) {\n        return;\n      }\n\n      const rect = droppableRects.get(entry.id);\n\n      if (!rect) {\n        return;\n      }\n\n      switch (event.code) {\n        case KeyboardCode.Down:\n          if (collisionRect.top < rect.top) {\n            filteredContainers.push(entry);\n          }\n          break;\n        case KeyboardCode.Up:\n          if (collisionRect.top > rect.top) {\n            filteredContainers.push(entry);\n          }\n          break;\n        case KeyboardCode.Left:\n          if (collisionRect.left > rect.left) {\n            filteredContainers.push(entry);\n          }\n          break;\n        case KeyboardCode.Right:\n          if (collisionRect.left < rect.left) {\n            filteredContainers.push(entry);\n          }\n          break;\n      }\n    });\n\n    const collisions = closestCorners({\n      active,\n      collisionRect: collisionRect,\n      droppableRects,\n      droppableContainers: filteredContainers,\n      pointerCoordinates: null,\n    });\n    let closestId = getFirstCollision(collisions, 'id');\n\n    if (closestId === over?.id && collisions.length > 1) {\n      closestId = collisions[1].id;\n    }\n\n    if (closestId != null) {\n      const activeDroppable = droppableContainers.get(active.id);\n      const newDroppable = droppableContainers.get(closestId);\n      const newRect = newDroppable ? droppableRects.get(newDroppable.id) : null;\n      const newNode = newDroppable?.node.current;\n\n      if (newNode && newRect && activeDroppable && newDroppable) {\n        const newScrollAncestors = getScrollableAncestors(newNode);\n        const hasDifferentScrollAncestors = newScrollAncestors.some(\n          (element, index) => scrollableAncestors[index] !== element\n        );\n        const hasSameContainer = isSameContainer(activeDroppable, newDroppable);\n        const isAfterActive = isAfter(activeDroppable, newDroppable);\n        const offset =\n          hasDifferentScrollAncestors || !hasSameContainer\n            ? {\n                x: 0,\n                y: 0,\n              }\n            : {\n                x: isAfterActive ? collisionRect.width - newRect.width : 0,\n                y: isAfterActive ? collisionRect.height - newRect.height : 0,\n              };\n        const rectCoordinates = {\n          x: newRect.left,\n          y: newRect.top,\n        };\n\n        const newCoordinates =\n          offset.x && offset.y\n            ? rectCoordinates\n            : subtract(rectCoordinates, offset);\n\n        return newCoordinates;\n      }\n    }\n  }\n\n  return undefined;\n};\n\nfunction isSameContainer(a: DroppableContainer, b: DroppableContainer) {\n  if (!hasSortableData(a) || !hasSortableData(b)) {\n    return false;\n  }\n\n  return (\n    a.data.current.sortable.containerId === b.data.current.sortable.containerId\n  );\n}\n\nfunction isAfter(a: DroppableContainer, b: DroppableContainer) {\n  if (!hasSortableData(a) || !hasSortableData(b)) {\n    return false;\n  }\n\n  if (!isSameContainer(a, b)) {\n    return false;\n  }\n\n  return a.data.current.sortable.index < b.data.current.sortable.index;\n}\n"], "names": ["arrayMove", "array", "from", "to", "newArray", "slice", "splice", "length", "arraySwap", "getSortedRects", "items", "rects", "reduce", "accumulator", "id", "index", "rect", "get", "Array", "isValidIndex", "itemsEqual", "a", "b", "i", "normalizeDisabled", "disabled", "draggable", "droppable", "defaultScale", "scaleX", "scaleY", "horizontalListSortingStrategy", "activeNodeRect", "fallbackActiveRect", "activeIndex", "overIndex", "itemGap", "getItemGap", "newIndexRect", "x", "left", "width", "y", "currentRect", "previousRect", "nextRect", "rectSortingStrategy", "newRects", "oldRect", "newRect", "top", "height", "rectSwappingStrategy", "verticalListSortingStrategy", "overIndexRect", "clientRects", "ID_PREFIX", "Context", "React", "createContext", "containerId", "disableTransforms", "useDragOverlay", "sortedRects", "strategy", "SortableContext", "children", "userDefinedItems", "disabledProp", "active", "dragOverlay", "droppableRects", "over", "measureDroppableContainers", "useDndContext", "useUniqueId", "Boolean", "useMemo", "map", "item", "isDragging", "indexOf", "previousItemsRef", "useRef", "itemsHaveChanged", "current", "useIsomorphicLayoutEffect", "useEffect", "contextValue", "Provider", "value", "defaultNewIndexGetter", "defaultAnimateLayoutChanges", "isSorting", "wasDragging", "newIndex", "previousItems", "previousContainerId", "transition", "defaultTransition", "duration", "easing", "transitionProperty", "disabledTransition", "CSS", "Transition", "toString", "property", "defaultAttributes", "roleDescription", "useDerivedTransform", "node", "derivedTransform", "setDerivedtransform", "useState", "previousIndex", "initial", "getClientRect", "ignoreTransform", "delta", "useSortable", "animateLayoutChanges", "attributes", "userDefinedAttributes", "localDisabled", "data", "customData", "getNewIndex", "localStrategy", "resizeObserverConfig", "globalDisabled", "globalStrategy", "useContext", "normalizeLocalDisabled", "sortable", "itemsAfterCurrentSortable", "isOver", "setNodeRef", "setDroppableNodeRef", "useDroppable", "updateMeasurementsFor", "activatorEvent", "setDraggableNodeRef", "listeners", "setActivatorNodeRef", "transform", "useDraggable", "useCombinedRefs", "displaceItem", "shouldDisplaceDragSource", "dragSourceDisplacement", "finalTransform", "activeId", "previous", "shouldAnimateLayoutChanges", "timeoutId", "setTimeout", "clearTimeout", "getTransition", "isKeyboardEvent", "undefined", "hasSortableData", "entry", "directions", "KeyboardCode", "Down", "Right", "Up", "Left", "sortableKeyboardCoordinates", "event", "context", "collisionRect", "droppableContainers", "scrollableAncestors", "includes", "code", "preventDefault", "filteredContainers", "getEnabled", "for<PERSON>ach", "push", "collisions", "closestCorners", "pointerCoordinates", "closestId", "getFirstCollision", "activeDroppable", "newDroppable", "newNode", "newScrollAncestors", "getScrollableAncestors", "hasDifferentScrollAncestors", "some", "element", "hasSameContainer", "isSameContainer", "isAfterActive", "isAfter", "offset", "rectCoordinates", "newCoordinates", "subtract"], "mappings": ";;;;;;;;;;;AAAA;;;SAGgBA,UAAaC,OAAYC,MAAcC;EACrD,MAAMC,QAAQ,GAAGH,KAAK,CAACI,KAAN,EAAjB;EACAD,QAAQ,CAACE,MAAT,CACEH,EAAE,GAAG,CAAL,GAASC,QAAQ,CAACG,MAAT,GAAkBJ,EAA3B,GAAgCA,EADlC,EAEE,CAFF,EAGEC,QAAQ,CAACE,MAAT,CAAgBJ,IAAhB,EAAsB,CAAtB,EAAyB,CAAzB,CAHF;EAMA,OAAOE,QAAP;AACD;;ACZD;;;AAGA,SAAgBI,UAAaP,OAAYC,MAAcC;EACrD,MAAMC,QAAQ,GAAGH,KAAK,CAACI,KAAN,EAAjB;EAEAD,QAAQ,CAACF,IAAD,CAAR,GAAiBD,KAAK,CAACE,EAAD,CAAtB;EACAC,QAAQ,CAACD,EAAD,CAAR,GAAeF,KAAK,CAACC,IAAD,CAApB;EAEA,OAAOE,QAAP;AACD;;SCJeK,eACdC,OACAC;EAEA,OAAOD,KAAK,CAACE,MAAN,CAA2B,CAACC,WAAD,EAAcC,EAAd,EAAkBC,KAAlB;IAChC,MAAMC,IAAI,GAAGL,KAAK,CAACM,GAAN,CAAUH,EAAV,CAAb;;IAEA,IAAIE,IAAJ,EAAU;MACRH,WAAW,CAACE,KAAD,CAAX,GAAqBC,IAArB;;;IAGF,OAAOH,WAAP;GAPK,EAQJK,KAAK,CAACR,KAAK,CAACH,MAAP,CARD,CAAP;AASD;;SCnBeY,aAAaJ;EAC3B,OAAOA,KAAK,KAAK,IAAV,IAAkBA,KAAK,IAAI,CAAlC;AACD;;SCAeK,WAAWC,GAAuBC;EAChD,IAAID,CAAC,KAAKC,CAAV,EAAa;IACX,OAAO,IAAP;;;EAGF,IAAID,CAAC,CAACd,MAAF,KAAae,CAAC,CAACf,MAAnB,EAA2B;IACzB,OAAO,KAAP;;;EAGF,KAAK,IAAIgB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,CAAC,CAACd,MAAtB,EAA8BgB,CAAC,EAA/B,EAAmC;IACjC,IAAIF,CAAC,CAACE,CAAD,CAAD,KAASD,CAAC,CAACC,CAAD,CAAd,EAAmB;MACjB,OAAO,KAAP;;;;EAIJ,OAAO,IAAP;AACD;;SChBeC,kBAAkBC;EAChC,IAAI,OAAOA,QAAP,KAAoB,SAAxB,EAAmC;IACjC,OAAO;MACLC,SAAS,EAAED,QADN;MAELE,SAAS,EAAEF;KAFb;;;EAMF,OAAOA,QAAP;AACD;;ACRD;AACA,MAAMG,YAAY,GAAG;EACnBC,MAAM,EAAE,CADW;EAEnBC,MAAM,EAAE;AAFW,CAArB;AAKA,MAAaC,6BAA6B,GAAoB;;;MAAC;IAC7DpB,KAD6D;IAE7DqB,cAAc,EAAEC,kBAF6C;IAG7DC,WAH6D;IAI7DC,SAJ6D;IAK7DpB;;EAEA,MAAMiB,cAAc,yBAAGrB,KAAK,CAACuB,WAAD,CAAR,iCAAyBD,kBAA7C;;EAEA,IAAI,CAACD,cAAL,EAAqB;IACnB,OAAO,IAAP;;;EAGF,MAAMI,OAAO,GAAGC,UAAU,CAAC1B,KAAD,EAAQI,KAAR,EAAemB,WAAf,CAA1B;;EAEA,IAAInB,KAAK,KAAKmB,WAAd,EAA2B;IACzB,MAAMI,YAAY,GAAG3B,KAAK,CAACwB,SAAD,CAA1B;;IAEA,IAAI,CAACG,YAAL,EAAmB;MACjB,OAAO,IAAP;;;IAGF,OAAO;MACLC,CAAC,EACCL,WAAW,GAAGC,SAAd,GACIG,YAAY,CAACE,IAAb,GACAF,YAAY,CAACG,KADb,IAECT,cAAc,CAACQ,IAAf,GAAsBR,cAAc,CAACS,KAFtC,CADJ,GAIIH,YAAY,CAACE,IAAb,GAAoBR,cAAc,CAACQ,IANpC;MAOLE,CAAC,EAAE,CAPE;MAQL,GAAGd;KARL;;;EAYF,IAAIb,KAAK,GAAGmB,WAAR,IAAuBnB,KAAK,IAAIoB,SAApC,EAA+C;IAC7C,OAAO;MACLI,CAAC,EAAE,CAACP,cAAc,CAACS,KAAhB,GAAwBL,OADtB;MAELM,CAAC,EAAE,CAFE;MAGL,GAAGd;KAHL;;;EAOF,IAAIb,KAAK,GAAGmB,WAAR,IAAuBnB,KAAK,IAAIoB,SAApC,EAA+C;IAC7C,OAAO;MACLI,CAAC,EAAEP,cAAc,CAACS,KAAf,GAAuBL,OADrB;MAELM,CAAC,EAAE,CAFE;MAGL,GAAGd;KAHL;;;EAOF,OAAO;IACLW,CAAC,EAAE,CADE;IAELG,CAAC,EAAE,CAFE;IAGL,GAAGd;GAHL;AAKD,CAvDM;;AAyDP,SAASS,UAAT,CAAoB1B,KAApB,EAAyCI,KAAzC,EAAwDmB,WAAxD;EACE,MAAMS,WAAW,GAA2BhC,KAAK,CAACI,KAAD,CAAjD;EACA,MAAM6B,YAAY,GAA2BjC,KAAK,CAACI,KAAK,GAAG,CAAT,CAAlD;EACA,MAAM8B,QAAQ,GAA2BlC,KAAK,CAACI,KAAK,GAAG,CAAT,CAA9C;;EAEA,IAAI,CAAC4B,WAAD,IAAiB,CAACC,YAAD,IAAiB,CAACC,QAAvC,EAAkD;IAChD,OAAO,CAAP;;;EAGF,IAAIX,WAAW,GAAGnB,KAAlB,EAAyB;IACvB,OAAO6B,YAAY,GACfD,WAAW,CAACH,IAAZ,IAAoBI,YAAY,CAACJ,IAAb,GAAoBI,YAAY,CAACH,KAArD,CADe,GAEfI,QAAQ,CAACL,IAAT,IAAiBG,WAAW,CAACH,IAAZ,GAAmBG,WAAW,CAACF,KAAhD,CAFJ;;;EAKF,OAAOI,QAAQ,GACXA,QAAQ,CAACL,IAAT,IAAiBG,WAAW,CAACH,IAAZ,GAAmBG,WAAW,CAACF,KAAhD,CADW,GAEXE,WAAW,CAACH,IAAZ,IAAoBI,YAAY,CAACJ,IAAb,GAAoBI,YAAY,CAACH,KAArD,CAFJ;AAGD;;MCjFYK,mBAAmB,GAAoB;MAAC;IACnDnC,KADmD;IAEnDuB,WAFmD;IAGnDC,SAHmD;IAInDpB;;EAEA,MAAMgC,QAAQ,GAAG/C,SAAS,CAACW,KAAD,EAAQwB,SAAR,EAAmBD,WAAnB,CAA1B;EAEA,MAAMc,OAAO,GAAGrC,KAAK,CAACI,KAAD,CAArB;EACA,MAAMkC,OAAO,GAAGF,QAAQ,CAAChC,KAAD,CAAxB;;EAEA,IAAI,CAACkC,OAAD,IAAY,CAACD,OAAjB,EAA0B;IACxB,OAAO,IAAP;;;EAGF,OAAO;IACLT,CAAC,EAAEU,OAAO,CAACT,IAAR,GAAeQ,OAAO,CAACR,IADrB;IAELE,CAAC,EAAEO,OAAO,CAACC,GAAR,GAAcF,OAAO,CAACE,GAFpB;IAGLrB,MAAM,EAAEoB,OAAO,CAACR,KAAR,GAAgBO,OAAO,CAACP,KAH3B;IAILX,MAAM,EAAEmB,OAAO,CAACE,MAAR,GAAiBH,OAAO,CAACG;GAJnC;AAMD,CArBM;;MCDMC,oBAAoB,GAAoB;MAAC;IACpDlB,WADoD;IAEpDnB,KAFoD;IAGpDJ,KAHoD;IAIpDwB;;EAEA,IAAIa,OAAJ;EACA,IAAIC,OAAJ;;EAEA,IAAIlC,KAAK,KAAKmB,WAAd,EAA2B;IACzBc,OAAO,GAAGrC,KAAK,CAACI,KAAD,CAAf;IACAkC,OAAO,GAAGtC,KAAK,CAACwB,SAAD,CAAf;;;EAGF,IAAIpB,KAAK,KAAKoB,SAAd,EAAyB;IACvBa,OAAO,GAAGrC,KAAK,CAACI,KAAD,CAAf;IACAkC,OAAO,GAAGtC,KAAK,CAACuB,WAAD,CAAf;;;EAGF,IAAI,CAACe,OAAD,IAAY,CAACD,OAAjB,EAA0B;IACxB,OAAO,IAAP;;;EAGF,OAAO;IACLT,CAAC,EAAEU,OAAO,CAACT,IAAR,GAAeQ,OAAO,CAACR,IADrB;IAELE,CAAC,EAAEO,OAAO,CAACC,GAAR,GAAcF,OAAO,CAACE,GAFpB;IAGLrB,MAAM,EAAEoB,OAAO,CAACR,KAAR,GAAgBO,OAAO,CAACP,KAH3B;IAILX,MAAM,EAAEmB,OAAO,CAACE,MAAR,GAAiBH,OAAO,CAACG;GAJnC;AAMD,CA7BM;;ACCP;AACA,MAAMvB,cAAY,GAAG;EACnBC,MAAM,EAAE,CADW;EAEnBC,MAAM,EAAE;AAFW,CAArB;AAKA,MAAauB,2BAA2B,GAAoB;;;MAAC;IAC3DnB,WAD2D;IAE3DF,cAAc,EAAEC,kBAF2C;IAG3DlB,KAH2D;IAI3DJ,KAJ2D;IAK3DwB;;EAEA,MAAMH,cAAc,yBAAGrB,KAAK,CAACuB,WAAD,CAAR,iCAAyBD,kBAA7C;;EAEA,IAAI,CAACD,cAAL,EAAqB;IACnB,OAAO,IAAP;;;EAGF,IAAIjB,KAAK,KAAKmB,WAAd,EAA2B;IACzB,MAAMoB,aAAa,GAAG3C,KAAK,CAACwB,SAAD,CAA3B;;IAEA,IAAI,CAACmB,aAAL,EAAoB;MAClB,OAAO,IAAP;;;IAGF,OAAO;MACLf,CAAC,EAAE,CADE;MAELG,CAAC,EACCR,WAAW,GAAGC,SAAd,GACImB,aAAa,CAACJ,GAAd,GACAI,aAAa,CAACH,MADd,IAECnB,cAAc,CAACkB,GAAf,GAAqBlB,cAAc,CAACmB,MAFrC,CADJ,GAIIG,aAAa,CAACJ,GAAd,GAAoBlB,cAAc,CAACkB,GAPpC;MAQL,GAAGtB;KARL;;;EAYF,MAAMQ,OAAO,GAAGC,YAAU,CAAC1B,KAAD,EAAQI,KAAR,EAAemB,WAAf,CAA1B;;EAEA,IAAInB,KAAK,GAAGmB,WAAR,IAAuBnB,KAAK,IAAIoB,SAApC,EAA+C;IAC7C,OAAO;MACLI,CAAC,EAAE,CADE;MAELG,CAAC,EAAE,CAACV,cAAc,CAACmB,MAAhB,GAAyBf,OAFvB;MAGL,GAAGR;KAHL;;;EAOF,IAAIb,KAAK,GAAGmB,WAAR,IAAuBnB,KAAK,IAAIoB,SAApC,EAA+C;IAC7C,OAAO;MACLI,CAAC,EAAE,CADE;MAELG,CAAC,EAAEV,cAAc,CAACmB,MAAf,GAAwBf,OAFtB;MAGL,GAAGR;KAHL;;;EAOF,OAAO;IACLW,CAAC,EAAE,CADE;IAELG,CAAC,EAAE,CAFE;IAGL,GAAGd;GAHL;AAKD,CAvDM;;AAyDP,SAASS,YAAT,CACEkB,WADF,EAEExC,KAFF,EAGEmB,WAHF;EAKE,MAAMS,WAAW,GAA2BY,WAAW,CAACxC,KAAD,CAAvD;EACA,MAAM6B,YAAY,GAA2BW,WAAW,CAACxC,KAAK,GAAG,CAAT,CAAxD;EACA,MAAM8B,QAAQ,GAA2BU,WAAW,CAACxC,KAAK,GAAG,CAAT,CAApD;;EAEA,IAAI,CAAC4B,WAAL,EAAkB;IAChB,OAAO,CAAP;;;EAGF,IAAIT,WAAW,GAAGnB,KAAlB,EAAyB;IACvB,OAAO6B,YAAY,GACfD,WAAW,CAACO,GAAZ,IAAmBN,YAAY,CAACM,GAAb,GAAmBN,YAAY,CAACO,MAAnD,CADe,GAEfN,QAAQ,GACRA,QAAQ,CAACK,GAAT,IAAgBP,WAAW,CAACO,GAAZ,GAAkBP,WAAW,CAACQ,MAA9C,CADQ,GAER,CAJJ;;;EAOF,OAAON,QAAQ,GACXA,QAAQ,CAACK,GAAT,IAAgBP,WAAW,CAACO,GAAZ,GAAkBP,WAAW,CAACQ,MAA9C,CADW,GAEXP,YAAY,GACZD,WAAW,CAACO,GAAZ,IAAmBN,YAAY,CAACM,GAAb,GAAmBN,YAAY,CAACO,MAAnD,CADY,GAEZ,CAJJ;AAKD;;AC5ED,MAAMK,SAAS,GAAG,UAAlB;AAcA,AAAO,MAAMC,OAAO,gBAAGC,cAAK,CAACC,aAAN,CAAuC;EAC5DzB,WAAW,EAAE,CAAC,CAD8C;EAE5D0B,WAAW,EAAEJ,SAF+C;EAG5DK,iBAAiB,EAAE,KAHyC;EAI5DnD,KAAK,EAAE,EAJqD;EAK5DyB,SAAS,EAAE,CAAC,CALgD;EAM5D2B,cAAc,EAAE,KAN4C;EAO5DC,WAAW,EAAE,EAP+C;EAQ5DC,QAAQ,EAAElB,mBARkD;EAS5DrB,QAAQ,EAAE;IACRC,SAAS,EAAE,KADH;IAERC,SAAS,EAAE;;AAX+C,CAAvC,CAAhB;AAeP,SAAgBsC;MAAgB;IAC9BC,QAD8B;IAE9BpD,EAF8B;IAG9BJ,KAAK,EAAEyD,gBAHuB;IAI9BH,QAAQ,GAAGlB,mBAJmB;IAK9BrB,QAAQ,EAAE2C,YAAY,GAAG;;EAEzB,MAAM;IACJC,MADI;IAEJC,WAFI;IAGJC,cAHI;IAIJC,IAJI;IAKJC;MACEC,kBAAa,EANjB;EAOA,MAAMd,WAAW,GAAGe,qBAAW,CAACnB,SAAD,EAAY1C,EAAZ,CAA/B;EACA,MAAMgD,cAAc,GAAGc,OAAO,CAACN,WAAW,CAACtD,IAAZ,KAAqB,IAAtB,CAA9B;EACA,MAAMN,KAAK,GAAGmE,aAAO,CACnB,MACEV,gBAAgB,CAACW,GAAjB,CAAsBC,IAAD,IACnB,OAAOA,IAAP,KAAgB,QAAhB,IAA4B,QAAQA,IAApC,GAA2CA,IAAI,CAACjE,EAAhD,GAAqDiE,IADvD,CAFiB,EAKnB,CAACZ,gBAAD,CALmB,CAArB;EAOA,MAAMa,UAAU,GAAGX,MAAM,IAAI,IAA7B;EACA,MAAMnC,WAAW,GAAGmC,MAAM,GAAG3D,KAAK,CAACuE,OAAN,CAAcZ,MAAM,CAACvD,EAArB,CAAH,GAA8B,CAAC,CAAzD;EACA,MAAMqB,SAAS,GAAGqC,IAAI,GAAG9D,KAAK,CAACuE,OAAN,CAAcT,IAAI,CAAC1D,EAAnB,CAAH,GAA4B,CAAC,CAAnD;EACA,MAAMoE,gBAAgB,GAAGC,YAAM,CAACzE,KAAD,CAA/B;EACA,MAAM0E,gBAAgB,GAAG,CAAChE,UAAU,CAACV,KAAD,EAAQwE,gBAAgB,CAACG,OAAzB,CAApC;EACA,MAAMxB,iBAAiB,GACpB1B,SAAS,KAAK,CAAC,CAAf,IAAoBD,WAAW,KAAK,CAAC,CAAtC,IAA4CkD,gBAD9C;EAEA,MAAM3D,QAAQ,GAAGD,iBAAiB,CAAC4C,YAAD,CAAlC;EAEAkB,mCAAyB,CAAC;IACxB,IAAIF,gBAAgB,IAAIJ,UAAxB,EAAoC;MAClCP,0BAA0B,CAAC/D,KAAD,CAA1B;;GAFqB,EAItB,CAAC0E,gBAAD,EAAmB1E,KAAnB,EAA0BsE,UAA1B,EAAsCP,0BAAtC,CAJsB,CAAzB;EAMAc,eAAS,CAAC;IACRL,gBAAgB,CAACG,OAAjB,GAA2B3E,KAA3B;GADO,EAEN,CAACA,KAAD,CAFM,CAAT;EAIA,MAAM8E,YAAY,GAAGX,aAAO,CAC1B,OAA0B;IACxB3C,WADwB;IAExB0B,WAFwB;IAGxBnC,QAHwB;IAIxBoC,iBAJwB;IAKxBnD,KALwB;IAMxByB,SANwB;IAOxB2B,cAPwB;IAQxBC,WAAW,EAAEtD,cAAc,CAACC,KAAD,EAAQ6D,cAAR,CARH;IASxBP;GATF,CAD0B;EAa1B,CACE9B,WADF,EAEE0B,WAFF,EAGEnC,QAAQ,CAACC,SAHX,EAIED,QAAQ,CAACE,SAJX,EAKEkC,iBALF,EAMEnD,KANF,EAOEyB,SAPF,EAQEoC,cARF,EASET,cATF,EAUEE,QAVF,CAb0B,CAA5B;EA2BA,OAAON,4BAAA,CAACD,OAAO,CAACgC,QAAT;IAAkBC,KAAK,EAAEF;GAAzB,EAAwCtB,QAAxC,CAAP;AACD;;MCzGYyB,qBAAqB,GAAmB;EAAA,IAAC;IACpD7E,EADoD;IAEpDJ,KAFoD;IAGpDwB,WAHoD;IAIpDC;GAJmD;EAAA,OAK/CnC,SAAS,CAACU,KAAD,EAAQwB,WAAR,EAAqBC,SAArB,CAAT,CAAyC8C,OAAzC,CAAiDnE,EAAjD,CAL+C;AAAA,CAA9C;AAOP,MAAa8E,2BAA2B,GAAyB;MAAC;IAChEhC,WADgE;IAEhEiC,SAFgE;IAGhEC,WAHgE;IAIhE/E,KAJgE;IAKhEL,KALgE;IAMhEqF,QANgE;IAOhEC,aAPgE;IAQhEC,mBARgE;IAShEC;;;EAEA,IAAI,CAACA,UAAD,IAAe,CAACJ,WAApB,EAAiC;IAC/B,OAAO,KAAP;;;EAGF,IAAIE,aAAa,KAAKtF,KAAlB,IAA2BK,KAAK,KAAKgF,QAAzC,EAAmD;IACjD,OAAO,KAAP;;;EAGF,IAAIF,SAAJ,EAAe;IACb,OAAO,IAAP;;;EAGF,OAAOE,QAAQ,KAAKhF,KAAb,IAAsB6C,WAAW,KAAKqC,mBAA7C;AACD,CAxBM;AA0BP,AAAO,MAAME,iBAAiB,GAAuB;EACnDC,QAAQ,EAAE,GADyC;EAEnDC,MAAM,EAAE;AAF2C,CAA9C;AAKP,AAAO,MAAMC,kBAAkB,GAAG,WAA3B;AAEP,AAAO,MAAMC,kBAAkB,gBAAGC,aAAG,CAACC,UAAJ,CAAeC,QAAf,CAAwB;EACxDC,QAAQ,EAAEL,kBAD8C;EAExDF,QAAQ,EAAE,CAF8C;EAGxDC,MAAM,EAAE;AAHgD,CAAxB,CAA3B;AAMP,AAAO,MAAMO,iBAAiB,GAAG;EAC/BC,eAAe,EAAE;AADc,CAA1B;;AC7CP;;;;;AAIA,SAAgBC;MAAoB;IAACrF,QAAD;IAAWV,KAAX;IAAkBgG,IAAlB;IAAwB/F;;EAC1D,MAAM,CAACgG,gBAAD,EAAmBC,mBAAnB,IAA0CC,cAAQ,CACtD,IADsD,CAAxD;EAGA,MAAMC,aAAa,GAAGhC,YAAM,CAACpE,KAAD,CAA5B;EAEAuE,mCAAyB,CAAC;IACxB,IAAI,CAAC7D,QAAD,IAAaV,KAAK,KAAKoG,aAAa,CAAC9B,OAArC,IAAgD0B,IAAI,CAAC1B,OAAzD,EAAkE;MAChE,MAAM+B,OAAO,GAAGpG,IAAI,CAACqE,OAArB;;MAEA,IAAI+B,OAAJ,EAAa;QACX,MAAM/B,OAAO,GAAGgC,kBAAa,CAACN,IAAI,CAAC1B,OAAN,EAAe;UAC1CiC,eAAe,EAAE;SADU,CAA7B;QAIA,MAAMC,KAAK,GAAG;UACZhF,CAAC,EAAE6E,OAAO,CAAC5E,IAAR,GAAe6C,OAAO,CAAC7C,IADd;UAEZE,CAAC,EAAE0E,OAAO,CAAClE,GAAR,GAAcmC,OAAO,CAACnC,GAFb;UAGZrB,MAAM,EAAEuF,OAAO,CAAC3E,KAAR,GAAgB4C,OAAO,CAAC5C,KAHpB;UAIZX,MAAM,EAAEsF,OAAO,CAACjE,MAAR,GAAiBkC,OAAO,CAAClC;SAJnC;;QAOA,IAAIoE,KAAK,CAAChF,CAAN,IAAWgF,KAAK,CAAC7E,CAArB,EAAwB;UACtBuE,mBAAmB,CAACM,KAAD,CAAnB;;;;;IAKN,IAAIxG,KAAK,KAAKoG,aAAa,CAAC9B,OAA5B,EAAqC;MACnC8B,aAAa,CAAC9B,OAAd,GAAwBtE,KAAxB;;GAvBqB,EAyBtB,CAACU,QAAD,EAAWV,KAAX,EAAkBgG,IAAlB,EAAwB/F,IAAxB,CAzBsB,CAAzB;EA2BAuE,eAAS,CAAC;IACR,IAAIyB,gBAAJ,EAAsB;MACpBC,mBAAmB,CAAC,IAAD,CAAnB;;GAFK,EAIN,CAACD,gBAAD,CAJM,CAAT;EAMA,OAAOA,gBAAP;AACD;;SCjBeQ;MAAY;IAC1BC,oBAAoB,GAAG7B,2BADG;IAE1B8B,UAAU,EAAEC,qBAFc;IAG1BlG,QAAQ,EAAEmG,aAHgB;IAI1BC,IAAI,EAAEC,UAJoB;IAK1BC,WAAW,GAAGpC,qBALY;IAM1B7E,EAN0B;IAO1BkD,QAAQ,EAAEgE,aAPgB;IAQ1BC,oBAR0B;IAS1B/B,UAAU,GAAGC;;EAEb,MAAM;IACJzF,KADI;IAEJkD,WAFI;IAGJ1B,WAHI;IAIJT,QAAQ,EAAEyG,cAJN;IAKJrE,iBALI;IAMJE,WANI;IAOJ5B,SAPI;IAQJ2B,cARI;IASJE,QAAQ,EAAEmE;MACRC,gBAAU,CAAC3E,OAAD,CAVd;EAWA,MAAMhC,QAAQ,GAAa4G,sBAAsB,CAC/CT,aAD+C,EAE/CM,cAF+C,CAAjD;EAIA,MAAMnH,KAAK,GAAGL,KAAK,CAACuE,OAAN,CAAcnE,EAAd,CAAd;EACA,MAAM+G,IAAI,GAAGhD,aAAO,CAClB,OAAO;IAACyD,QAAQ,EAAE;MAAC1E,WAAD;MAAc7C,KAAd;MAAqBL;KAAhC;IAAwC,GAAGoH;GAAlD,CADkB,EAElB,CAAClE,WAAD,EAAckE,UAAd,EAA0B/G,KAA1B,EAAiCL,KAAjC,CAFkB,CAApB;EAIA,MAAM6H,yBAAyB,GAAG1D,aAAO,CACvC,MAAMnE,KAAK,CAACL,KAAN,CAAYK,KAAK,CAACuE,OAAN,CAAcnE,EAAd,CAAZ,CADiC,EAEvC,CAACJ,KAAD,EAAQI,EAAR,CAFuC,CAAzC;EAIA,MAAM;IACJE,IADI;IAEJ+F,IAFI;IAGJyB,MAHI;IAIJC,UAAU,EAAEC;MACVC,iBAAY,CAAC;IACf7H,EADe;IAEf+G,IAFe;IAGfpG,QAAQ,EAAEA,QAAQ,CAACE,SAHJ;IAIfsG,oBAAoB,EAAE;MACpBW,qBAAqB,EAAEL,yBADH;MAEpB,GAAGN;;GANS,CALhB;EAcA,MAAM;IACJ5D,MADI;IAEJwE,cAFI;IAGJ7G,cAHI;IAIJ0F,UAJI;IAKJe,UAAU,EAAEK,mBALR;IAMJC,SANI;IAOJ/D,UAPI;IAQJR,IARI;IASJwE,mBATI;IAUJC;MACEC,iBAAY,CAAC;IACfpI,EADe;IAEf+G,IAFe;IAGfH,UAAU,EAAE,EACV,GAAGd,iBADO;MAEV,GAAGe;KALU;IAOflG,QAAQ,EAAEA,QAAQ,CAACC;GAPL,CAXhB;EAoBA,MAAM+G,UAAU,GAAGU,yBAAe,CAACT,mBAAD,EAAsBI,mBAAtB,CAAlC;EACA,MAAMjD,SAAS,GAAGjB,OAAO,CAACP,MAAD,CAAzB;EACA,MAAM+E,YAAY,GAChBvD,SAAS,IACT,CAAChC,iBADD,IAEA1C,YAAY,CAACe,WAAD,CAFZ,IAGAf,YAAY,CAACgB,SAAD,CAJd;EAKA,MAAMkH,wBAAwB,GAAG,CAACvF,cAAD,IAAmBkB,UAApD;EACA,MAAMsE,sBAAsB,GAC1BD,wBAAwB,IAAID,YAA5B,GAA2CH,SAA3C,GAAuD,IADzD;EAEA,MAAMjF,QAAQ,GAAGgE,aAAH,WAAGA,aAAH,GAAoBG,cAAlC;EACA,MAAMoB,cAAc,GAAGH,YAAY,GAC/BE,sBAD+B,WAC/BA,sBAD+B,GAE/BtF,QAAQ,CAAC;IACPrD,KAAK,EAAEoD,WADA;IAEP/B,cAFO;IAGPE,WAHO;IAIPC,SAJO;IAKPpB;GALM,CAFuB,GAS/B,IATJ;EAUA,MAAMgF,QAAQ,GACZ5E,YAAY,CAACe,WAAD,CAAZ,IAA6Bf,YAAY,CAACgB,SAAD,CAAzC,GACI4F,WAAW,CAAC;IAACjH,EAAD;IAAKJ,KAAL;IAAYwB,WAAZ;IAAyBC;GAA1B,CADf,GAEIpB,KAHN;EAIA,MAAMyI,QAAQ,GAAGnF,MAAH,oBAAGA,MAAM,CAAEvD,EAAzB;EACA,MAAM2I,QAAQ,GAAGtE,YAAM,CAAC;IACtBqE,QADsB;IAEtB9I,KAFsB;IAGtBqF,QAHsB;IAItBnC;GAJqB,CAAvB;EAMA,MAAMwB,gBAAgB,GAAG1E,KAAK,KAAK+I,QAAQ,CAACpE,OAAT,CAAiB3E,KAApD;EACA,MAAMgJ,0BAA0B,GAAGjC,oBAAoB,CAAC;IACtDpD,MADsD;IAEtDT,WAFsD;IAGtDoB,UAHsD;IAItDa,SAJsD;IAKtD/E,EALsD;IAMtDC,KANsD;IAOtDL,KAPsD;IAQtDqF,QAAQ,EAAE0D,QAAQ,CAACpE,OAAT,CAAiBU,QAR2B;IAStDC,aAAa,EAAEyD,QAAQ,CAACpE,OAAT,CAAiB3E,KATsB;IAUtDuF,mBAAmB,EAAEwD,QAAQ,CAACpE,OAAT,CAAiBzB,WAVgB;IAWtDsC,UAXsD;IAYtDJ,WAAW,EAAE2D,QAAQ,CAACpE,OAAT,CAAiBmE,QAAjB,IAA6B;GAZW,CAAvD;EAeA,MAAMxC,gBAAgB,GAAGF,mBAAmB,CAAC;IAC3CrF,QAAQ,EAAE,CAACiI,0BADgC;IAE3C3I,KAF2C;IAG3CgG,IAH2C;IAI3C/F;GAJ0C,CAA5C;EAOAuE,eAAS,CAAC;IACR,IAAIM,SAAS,IAAI4D,QAAQ,CAACpE,OAAT,CAAiBU,QAAjB,KAA8BA,QAA/C,EAAyD;MACvD0D,QAAQ,CAACpE,OAAT,CAAiBU,QAAjB,GAA4BA,QAA5B;;;IAGF,IAAInC,WAAW,KAAK6F,QAAQ,CAACpE,OAAT,CAAiBzB,WAArC,EAAkD;MAChD6F,QAAQ,CAACpE,OAAT,CAAiBzB,WAAjB,GAA+BA,WAA/B;;;IAGF,IAAIlD,KAAK,KAAK+I,QAAQ,CAACpE,OAAT,CAAiB3E,KAA/B,EAAsC;MACpC+I,QAAQ,CAACpE,OAAT,CAAiB3E,KAAjB,GAAyBA,KAAzB;;GAVK,EAYN,CAACmF,SAAD,EAAYE,QAAZ,EAAsBnC,WAAtB,EAAmClD,KAAnC,CAZM,CAAT;EAcA6E,eAAS,CAAC;IACR,IAAIiE,QAAQ,KAAKC,QAAQ,CAACpE,OAAT,CAAiBmE,QAAlC,EAA4C;MAC1C;;;IAGF,IAAIA,QAAQ,IAAI,IAAZ,IAAoBC,QAAQ,CAACpE,OAAT,CAAiBmE,QAAjB,IAA6B,IAArD,EAA2D;MACzDC,QAAQ,CAACpE,OAAT,CAAiBmE,QAAjB,GAA4BA,QAA5B;MACA;;;IAGF,MAAMG,SAAS,GAAGC,UAAU,CAAC;MAC3BH,QAAQ,CAACpE,OAAT,CAAiBmE,QAAjB,GAA4BA,QAA5B;KAD0B,EAEzB,EAFyB,CAA5B;IAIA,OAAO,MAAMK,YAAY,CAACF,SAAD,CAAzB;GAdO,EAeN,CAACH,QAAD,CAfM,CAAT;EAiBA,OAAO;IACLnF,MADK;IAELnC,WAFK;IAGLwF,UAHK;IAILG,IAJK;IAKL7G,IALK;IAMLD,KANK;IAOLgF,QAPK;IAQLrF,KARK;IASL8H,MATK;IAUL3C,SAVK;IAWLb,UAXK;IAYL+D,SAZK;IAaLhC,IAbK;IAcL5E,SAdK;IAeLqC,IAfK;IAgBLiE,UAhBK;IAiBLO,mBAjBK;IAkBLN,mBAlBK;IAmBLI,mBAnBK;IAoBLG,SAAS,EAAEjC,gBAAF,WAAEA,gBAAF,GAAsBuC,cApB1B;IAqBLrD,UAAU,EAAE4D,aAAa;GArB3B;;EAwBA,SAASA,aAAT;IACE;IAEE9C,gBAAgB;IAEf5B,gBAAgB,IAAIqE,QAAQ,CAACpE,OAAT,CAAiBU,QAAjB,KAA8BhF,KAJrD,EAKE;MACA,OAAOwF,kBAAP;;;IAGF,IACG8C,wBAAwB,IAAI,CAACU,yBAAe,CAAClB,cAAD,CAA7C,IACA,CAAC3C,UAFH,EAGE;MACA,OAAO8D,SAAP;;;IAGF,IAAInE,SAAS,IAAI6D,0BAAjB,EAA6C;MAC3C,OAAOlD,aAAG,CAACC,UAAJ,CAAeC,QAAf,CAAwB,EAC7B,GAAGR,UAD0B;QAE7BS,QAAQ,EAAEL;OAFL,CAAP;;;IAMF,OAAO0D,SAAP;;AAEH;;AAED,SAAS3B,sBAAT,CACET,aADF,EAEEM,cAFF;;;EAIE,IAAI,OAAON,aAAP,KAAyB,SAA7B,EAAwC;IACtC,OAAO;MACLlG,SAAS,EAAEkG,aADN;;MAGLjG,SAAS,EAAE;KAHb;;;EAOF,OAAO;IACLD,SAAS,2BAAEkG,aAAF,oBAAEA,aAAa,CAAElG,SAAjB,oCAA8BwG,cAAc,CAACxG,SADjD;IAELC,SAAS,2BAAEiG,aAAF,oBAAEA,aAAa,CAAEjG,SAAjB,oCAA8BuG,cAAc,CAACvG;GAFxD;AAID;;SC3PesI,gBAGdC;EAEA,IAAI,CAACA,KAAL,EAAY;IACV,OAAO,KAAP;;;EAGF,MAAMrC,IAAI,GAAGqC,KAAK,CAACrC,IAAN,CAAWxC,OAAxB;;EAEA,IACEwC,IAAI,IACJ,cAAcA,IADd,IAEA,OAAOA,IAAI,CAACS,QAAZ,KAAyB,QAFzB,IAGA,iBAAiBT,IAAI,CAACS,QAHtB,IAIA,WAAWT,IAAI,CAACS,QAJhB,IAKA,WAAWT,IAAI,CAACS,QANlB,EAOE;IACA,OAAO,IAAP;;;EAGF,OAAO,KAAP;AACD;;ACrBD,MAAM6B,UAAU,GAAa,CAC3BC,iBAAY,CAACC,IADc,EAE3BD,iBAAY,CAACE,KAFc,EAG3BF,iBAAY,CAACG,EAHc,EAI3BH,iBAAY,CAACI,IAJc,CAA7B;AAOA,MAAaC,2BAA2B,GAA6B,CACnEC,KADmE;MAEnE;IACEC,OAAO,EAAE;MACPtG,MADO;MAEPuG,aAFO;MAGPrG,cAHO;MAIPsG,mBAJO;MAKPrG,IALO;MAMPsG;;;;EAIJ,IAAIX,UAAU,CAACY,QAAX,CAAoBL,KAAK,CAACM,IAA1B,CAAJ,EAAqC;IACnCN,KAAK,CAACO,cAAN;;IAEA,IAAI,CAAC5G,MAAD,IAAW,CAACuG,aAAhB,EAA+B;MAC7B;;;IAGF,MAAMM,kBAAkB,GAAyB,EAAjD;IAEAL,mBAAmB,CAACM,UAApB,GAAiCC,OAAjC,CAA0ClB,KAAD;MACvC,IAAI,CAACA,KAAD,IAAUA,KAAV,YAAUA,KAAK,CAAEzI,QAArB,EAA+B;QAC7B;;;MAGF,MAAMT,IAAI,GAAGuD,cAAc,CAACtD,GAAf,CAAmBiJ,KAAK,CAACpJ,EAAzB,CAAb;;MAEA,IAAI,CAACE,IAAL,EAAW;QACT;;;MAGF,QAAQ0J,KAAK,CAACM,IAAd;QACE,KAAKZ,iBAAY,CAACC,IAAlB;UACE,IAAIO,aAAa,CAAC1H,GAAd,GAAoBlC,IAAI,CAACkC,GAA7B,EAAkC;YAChCgI,kBAAkB,CAACG,IAAnB,CAAwBnB,KAAxB;;;UAEF;;QACF,KAAKE,iBAAY,CAACG,EAAlB;UACE,IAAIK,aAAa,CAAC1H,GAAd,GAAoBlC,IAAI,CAACkC,GAA7B,EAAkC;YAChCgI,kBAAkB,CAACG,IAAnB,CAAwBnB,KAAxB;;;UAEF;;QACF,KAAKE,iBAAY,CAACI,IAAlB;UACE,IAAII,aAAa,CAACpI,IAAd,GAAqBxB,IAAI,CAACwB,IAA9B,EAAoC;YAClC0I,kBAAkB,CAACG,IAAnB,CAAwBnB,KAAxB;;;UAEF;;QACF,KAAKE,iBAAY,CAACE,KAAlB;UACE,IAAIM,aAAa,CAACpI,IAAd,GAAqBxB,IAAI,CAACwB,IAA9B,EAAoC;YAClC0I,kBAAkB,CAACG,IAAnB,CAAwBnB,KAAxB;;;UAEF;;KA/BN;IAmCA,MAAMoB,UAAU,GAAGC,mBAAc,CAAC;MAChClH,MADgC;MAEhCuG,aAAa,EAAEA,aAFiB;MAGhCrG,cAHgC;MAIhCsG,mBAAmB,EAAEK,kBAJW;MAKhCM,kBAAkB,EAAE;KALW,CAAjC;IAOA,IAAIC,SAAS,GAAGC,sBAAiB,CAACJ,UAAD,EAAa,IAAb,CAAjC;;IAEA,IAAIG,SAAS,MAAKjH,IAAL,oBAAKA,IAAI,CAAE1D,EAAX,CAAT,IAA0BwK,UAAU,CAAC/K,MAAX,GAAoB,CAAlD,EAAqD;MACnDkL,SAAS,GAAGH,UAAU,CAAC,CAAD,CAAV,CAAcxK,EAA1B;;;IAGF,IAAI2K,SAAS,IAAI,IAAjB,EAAuB;MACrB,MAAME,eAAe,GAAGd,mBAAmB,CAAC5J,GAApB,CAAwBoD,MAAM,CAACvD,EAA/B,CAAxB;MACA,MAAM8K,YAAY,GAAGf,mBAAmB,CAAC5J,GAApB,CAAwBwK,SAAxB,CAArB;MACA,MAAMxI,OAAO,GAAG2I,YAAY,GAAGrH,cAAc,CAACtD,GAAf,CAAmB2K,YAAY,CAAC9K,EAAhC,CAAH,GAAyC,IAArE;MACA,MAAM+K,OAAO,GAAGD,YAAH,oBAAGA,YAAY,CAAE7E,IAAd,CAAmB1B,OAAnC;;MAEA,IAAIwG,OAAO,IAAI5I,OAAX,IAAsB0I,eAAtB,IAAyCC,YAA7C,EAA2D;QACzD,MAAME,kBAAkB,GAAGC,2BAAsB,CAACF,OAAD,CAAjD;QACA,MAAMG,2BAA2B,GAAGF,kBAAkB,CAACG,IAAnB,CAClC,CAACC,OAAD,EAAUnL,KAAV,KAAoB+J,mBAAmB,CAAC/J,KAAD,CAAnB,KAA+BmL,OADjB,CAApC;QAGA,MAAMC,gBAAgB,GAAGC,eAAe,CAACT,eAAD,EAAkBC,YAAlB,CAAxC;QACA,MAAMS,aAAa,GAAGC,OAAO,CAACX,eAAD,EAAkBC,YAAlB,CAA7B;QACA,MAAMW,MAAM,GACVP,2BAA2B,IAAI,CAACG,gBAAhC,GACI;UACE5J,CAAC,EAAE,CADL;UAEEG,CAAC,EAAE;SAHT,GAKI;UACEH,CAAC,EAAE8J,aAAa,GAAGzB,aAAa,CAACnI,KAAd,GAAsBQ,OAAO,CAACR,KAAjC,GAAyC,CAD3D;UAEEC,CAAC,EAAE2J,aAAa,GAAGzB,aAAa,CAACzH,MAAd,GAAuBF,OAAO,CAACE,MAAlC,GAA2C;SARnE;QAUA,MAAMqJ,eAAe,GAAG;UACtBjK,CAAC,EAAEU,OAAO,CAACT,IADW;UAEtBE,CAAC,EAAEO,OAAO,CAACC;SAFb;QAKA,MAAMuJ,cAAc,GAClBF,MAAM,CAAChK,CAAP,IAAYgK,MAAM,CAAC7J,CAAnB,GACI8J,eADJ,GAEIE,kBAAQ,CAACF,eAAD,EAAkBD,MAAlB,CAHd;QAKA,OAAOE,cAAP;;;;;EAKN,OAAOzC,SAAP;AACD,CA7GM;;AA+GP,SAASoC,eAAT,CAAyB/K,CAAzB,EAAgDC,CAAhD;EACE,IAAI,CAAC2I,eAAe,CAAC5I,CAAD,CAAhB,IAAuB,CAAC4I,eAAe,CAAC3I,CAAD,CAA3C,EAAgD;IAC9C,OAAO,KAAP;;;EAGF,OACED,CAAC,CAACwG,IAAF,CAAOxC,OAAP,CAAeiD,QAAf,CAAwB1E,WAAxB,KAAwCtC,CAAC,CAACuG,IAAF,CAAOxC,OAAP,CAAeiD,QAAf,CAAwB1E,WADlE;AAGD;;AAED,SAAS0I,OAAT,CAAiBjL,CAAjB,EAAwCC,CAAxC;EACE,IAAI,CAAC2I,eAAe,CAAC5I,CAAD,CAAhB,IAAuB,CAAC4I,eAAe,CAAC3I,CAAD,CAA3C,EAAgD;IAC9C,OAAO,KAAP;;;EAGF,IAAI,CAAC8K,eAAe,CAAC/K,CAAD,EAAIC,CAAJ,CAApB,EAA4B;IAC1B,OAAO,KAAP;;;EAGF,OAAOD,CAAC,CAACwG,IAAF,CAAOxC,OAAP,CAAeiD,QAAf,CAAwBvH,KAAxB,GAAgCO,CAAC,CAACuG,IAAF,CAAOxC,OAAP,CAAeiD,QAAf,CAAwBvH,KAA/D;AACD;;;;;;;;;;;;;;;"}