import React, { useState } from 'react';
import {
  Box,
  Ty<PERSON>graphy,
  Button,
  Grid,
  Card,
  CardMedia,
  CardContent,
  CardActions,
  IconButton,
  Chip,
  TextField,
  ToggleButton,
  ToggleButtonGroup,
  Divider,
} from '@mui/material';
import {
  GridView as GridViewIcon,
  ViewList as ListViewIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Shuffle as ShuffleIcon,
  Category as CategoryIcon,
} from '@mui/icons-material';
import { useProjectStore } from '@/app/stores/useProjectStore';

export const TraitsPanel: React.FC = () => {
  const {
    traits,
    selectedLayerId,
    layers,
    viewMode,
    setViewMode,
    selectTrait,
    selectedTraitIds,
    updateTrait,
    removeTrait
  } = useProjectStore();

  const [editingRarity, setEditingRarity] = useState<string | null>(null);

  // Get traits for selected layer
  const selectedLayer = layers.find(l => l.id === selectedLayerId);
  const layerTraits = traits.filter(t => t.layerId === selectedLayerId);

  const handleViewModeChange = (
    event: React.MouseEvent<HTMLElement>,
    newViewMode: 'grid' | 'list' | null,
  ) => {
    if (newViewMode !== null) {
      setViewMode(newViewMode);
    }
  };

  const handleTraitSelect = (traitId: string) => {
    selectTrait(traitId);
  };

  const handleRarityEdit = (traitId: string, newRarity: number) => {
    const trait = traits.find(t => t.id === traitId);
    if (trait && newRarity >= 0 && newRarity <= 100) {
      const updatedTrait = {
        ...trait,
        rarity: {
          ...trait.rarity,
          value: newRarity,
        },
        lastModified: new Date(),
      };
      updateTrait(updatedTrait);
    }
    setEditingRarity(null);
  };

  const handleToggleTraitVisibility = (traitId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    const trait = traits.find(t => t.id === traitId);
    if (trait) {
      const updatedTrait = {
        ...trait,
        isEnabled: !trait.isEnabled,
        lastModified: new Date(),
      };
      updateTrait(updatedTrait);
    }
  };

  const handleDeleteTrait = (traitId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    if (window.confirm('Are you sure you want to delete this trait?')) {
      removeTrait(traitId);
    }
  };

  const handleDistributeRarity = () => {
    if (!selectedLayerId) return;

    const layerTraitsToUpdate = layerTraits.filter(t => t.isEnabled && !t.rarity.isLocked);
    if (layerTraitsToUpdate.length === 0) return;

    const rarityPerTrait = 100 / layerTraitsToUpdate.length;

    layerTraitsToUpdate.forEach(trait => {
      const updatedTrait = {
        ...trait,
        rarity: {
          ...trait.rarity,
          value: rarityPerTrait,
        },
        lastModified: new Date(),
      };
      updateTrait(updatedTrait);
    });
  };

  const renderTraitCard = (trait: any) => (
    <Card
      key={trait.id}
      sx={{
        cursor: 'pointer',
        border: selectedTraitIds.includes(trait.id) ? 2 : 1,
        borderColor: selectedTraitIds.includes(trait.id) ? 'primary.main' : 'divider',
        '&:hover': {
          boxShadow: 2,
        },
      }}
      onClick={() => handleTraitSelect(trait.id)}
    >
      <CardMedia
        component="div"
        sx={{
          height: viewMode === 'grid' ? 120 : 60,
          backgroundColor: 'grey.100',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          position: 'relative',
        }}
      >
        {trait.file?.dataUrl ? (
          <img
            src={trait.file.dataUrl}
            alt={trait.metadata.name}
            style={{
              maxWidth: '100%',
              maxHeight: '100%',
              objectFit: 'contain',
            }}
          />
        ) : (
          <CategoryIcon sx={{ fontSize: 40, color: 'text.secondary' }} />
        )}
        
        <IconButton
          size="small"
          sx={{
            position: 'absolute',
            top: 4,
            right: 4,
            backgroundColor: 'background.paper',
            '&:hover': { backgroundColor: 'background.paper' },
          }}
          onClick={(e) => handleToggleTraitVisibility(trait.id, e)}
        >
          {trait.isEnabled ? <VisibilityIcon /> : <VisibilityOffIcon />}
        </IconButton>
      </CardMedia>

      <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
        <Typography variant="body2" fontWeight={600} gutterBottom>
          {trait.metadata.name}
        </Typography>
        
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {editingRarity === trait.id ? (
            <TextField
              size="small"
              type="number"
              defaultValue={trait.rarity.value}
              onBlur={(e) => handleRarityEdit(trait.id, parseFloat(e.target.value))}
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  handleRarityEdit(trait.id, parseFloat((e.target as HTMLInputElement).value));
                }
              }}
              sx={{ width: 80 }}
              autoFocus
            />
          ) : (
            <Chip
              label={`${trait.rarity.value.toFixed(2)}%`}
              size="small"
              color="primary"
              variant="outlined"
              onClick={(e) => {
                e.stopPropagation();
                setEditingRarity(trait.id);
              }}
            />
          )}
          
          <Typography variant="caption" color="text.secondary">
            Weight: {trait.rarity.weight}
          </Typography>
        </Box>
      </CardContent>

      <CardActions sx={{ p: 1, pt: 0 }}>
        <IconButton
          size="small"
          onClick={(e) => {
            e.stopPropagation();
            setEditingRarity(trait.id);
          }}
        >
          <EditIcon />
        </IconButton>
        
        <IconButton
          size="small"
          color="error"
          onClick={(e) => handleDeleteTrait(trait.id, e)}
        >
          <DeleteIcon />
        </IconButton>
      </CardActions>
    </Card>
  );

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Panel Header */}
      <Box
        sx={{
          p: 2,
          borderBottom: 1,
          borderColor: 'divider',
          backgroundColor: 'background.paper',
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Typography variant="h6" component="h2">
            Traits
          </Typography>
          <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
            <Chip
              label={layerTraits.length}
              size="small"
              color="primary"
              variant="outlined"
            />
            <ToggleButtonGroup
              value={viewMode}
              exclusive
              onChange={handleViewModeChange}
              size="small"
            >
              <ToggleButton value="grid">
                <GridViewIcon />
              </ToggleButton>
              <ToggleButton value="list">
                <ListViewIcon />
              </ToggleButton>
            </ToggleButtonGroup>
          </Box>
        </Box>

        {selectedLayer && (
          <Box sx={{ mb: 2 }}>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Layer: {selectedLayer.settings.name}
            </Typography>
            <Button
              variant="outlined"
              startIcon={<ShuffleIcon />}
              onClick={handleDistributeRarity}
              fullWidth
              size="small"
            >
              Auto-Distribute Rarity
            </Button>
          </Box>
        )}
      </Box>

      {/* Traits Content */}
      <Box sx={{ flex: 1, overflow: 'auto', p: 2 }}>
        {!selectedLayerId ? (
          <Box
            sx={{
              textAlign: 'center',
              color: 'text.secondary',
              py: 4,
            }}
          >
            <CategoryIcon sx={{ fontSize: 48, mb: 2, opacity: 0.5 }} />
            <Typography variant="body2" gutterBottom>
              No layer selected
            </Typography>
            <Typography variant="caption">
              Select a layer from the Layers panel to view its traits
            </Typography>
          </Box>
        ) : layerTraits.length === 0 ? (
          <Box
            sx={{
              textAlign: 'center',
              color: 'text.secondary',
              py: 4,
            }}
          >
            <CategoryIcon sx={{ fontSize: 48, mb: 2, opacity: 0.5 }} />
            <Typography variant="body2" gutterBottom>
              No traits in this layer
            </Typography>
            <Typography variant="caption">
              Import layers to see traits here
            </Typography>
          </Box>
        ) : (
          <Grid container spacing={viewMode === 'grid' ? 2 : 1}>
            {layerTraits.map((trait) => (
              <Grid
                item
                xs={viewMode === 'grid' ? 6 : 12}
                sm={viewMode === 'grid' ? 4 : 12}
                md={viewMode === 'grid' ? 3 : 12}
                key={trait.id}
              >
                {renderTraitCard(trait)}
              </Grid>
            ))}
          </Grid>
        )}
      </Box>

      {/* Panel Footer */}
      {layerTraits.length > 0 && (
        <Box
          sx={{
            p: 2,
            borderTop: 1,
            borderColor: 'divider',
            backgroundColor: 'background.paper',
          }}
        >
          <Typography variant="caption" color="text.secondary">
            Total Rarity: {layerTraits.reduce((sum, trait) => sum + trait.rarity.value, 0).toFixed(2)}%
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default TraitsPanel;
