import React, { useState, useRef, useCallback, useEffect } from 'react';
import {
  Box,
  Paper,
  Tabs,
  Tab,
  useMediaQuery,
  useTheme,
  IconButton,
} from '@mui/material';
import {
  DragHandle as DragHandleIcon,
  Layers as LayersIcon,
  Category as CategoryIcon,
  Visibility as VisibilityIcon,
} from '@mui/icons-material';

// Panel components (we'll create these next)
import LayersPanel from '../panels/LayersPanel';
import TraitsPanel from '../panels/TraitsPanel';
import PreviewPanel from '../panels/PreviewPanel';

interface PanelSizes {
  layers: number;
  traits: number;
  preview: number;
}

const DEFAULT_PANEL_SIZES: PanelSizes = {
  layers: 25,
  traits: 35,
  preview: 40,
};

const MIN_WIDTH_PERCENT = 15;
const MAX_WIDTH_PERCENT = 60;
const STORAGE_KEY = 'nft-generator-panel-layout';

export const PanelContainer: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  // Panel sizes state
  const [panelSizes, setPanelSizes] = useState<PanelSizes>(() => {
    const saved = localStorage.getItem(STORAGE_KEY);
    if (saved) {
      try {
        return JSON.parse(saved);
      } catch {
        return DEFAULT_PANEL_SIZES;
      }
    }
    return DEFAULT_PANEL_SIZES;
  });

  // Mobile tab state
  const [activeTab, setActiveTab] = useState(0);
  
  // Resize state
  const [isResizing, setIsResizing] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  // Save panel sizes to localStorage
  useEffect(() => {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(panelSizes));
  }, [panelSizes]);

  // Handle tab change for mobile
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  // Resize handlers
  const handleLeftDragStart = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    setIsResizing(true);

    const handleMouseMove = (e: MouseEvent) => {
      if (!containerRef.current) return;

      const containerRect = containerRef.current.getBoundingClientRect();
      const newLayersWidth = ((e.clientX - containerRect.left) / containerRect.width) * 100;
      
      if (newLayersWidth >= MIN_WIDTH_PERCENT && newLayersWidth <= MAX_WIDTH_PERCENT) {
        const remainingWidth = 100 - newLayersWidth;
        const traitsRatio = panelSizes.traits / (panelSizes.traits + panelSizes.preview);
        
        setPanelSizes({
          layers: newLayersWidth,
          traits: remainingWidth * traitsRatio,
          preview: remainingWidth * (1 - traitsRatio),
        });
      }
    };

    const handleMouseUp = () => {
      setIsResizing(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  }, [panelSizes]);

  const handleRightDragStart = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    setIsResizing(true);

    const handleMouseMove = (e: MouseEvent) => {
      if (!containerRef.current) return;

      const containerRect = containerRef.current.getBoundingClientRect();
      const mouseX = e.clientX - containerRect.left;
      const newTraitsWidth = ((mouseX - (panelSizes.layers / 100) * containerRect.width) / containerRect.width) * 100;
      
      if (newTraitsWidth >= MIN_WIDTH_PERCENT && newTraitsWidth <= MAX_WIDTH_PERCENT) {
        const newPreviewWidth = 100 - panelSizes.layers - newTraitsWidth;
        
        if (newPreviewWidth >= MIN_WIDTH_PERCENT) {
          setPanelSizes({
            layers: panelSizes.layers,
            traits: newTraitsWidth,
            preview: newPreviewWidth,
          });
        }
      }
    };

    const handleMouseUp = () => {
      setIsResizing(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  }, [panelSizes]);

  // Common paper styles
  const paperStyle = {
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
    borderRadius: 2,
    overflow: 'hidden',
    backgroundColor: 'background.paper',
  };

  // Resize handle styles
  const resizeHandleStyle = {
    position: 'absolute' as const,
    top: 0,
    bottom: 0,
    width: '8px',
    cursor: 'col-resize',
    zIndex: 10,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent',
    '&:hover': {
      backgroundColor: 'action.hover',
    },
    '&:hover .drag-icon': {
      opacity: 1,
    },
  };

  // Mobile layout
  if (isMobile) {
    return (
      <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
        {/* Mobile tabs */}
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            variant="fullWidth"
            sx={{
              minHeight: 48,
              '& .MuiTab-root': {
                minHeight: 48,
                fontSize: '0.875rem',
              },
            }}
          >
            <Tab icon={<LayersIcon />} label="Layers" />
            <Tab icon={<CategoryIcon />} label="Traits" />
            <Tab icon={<VisibilityIcon />} label="Preview" />
          </Tabs>
        </Box>

        {/* Mobile panel content */}
        <Box sx={{ flex: 1, overflow: 'hidden' }}>
          <Paper
            sx={{
              ...paperStyle,
              display: activeTab === 0 ? 'flex' : 'none',
              borderRadius: 0,
            }}
          >
            <LayersPanel />
          </Paper>

          <Paper
            sx={{
              ...paperStyle,
              display: activeTab === 1 ? 'flex' : 'none',
              borderRadius: 0,
            }}
          >
            <TraitsPanel />
          </Paper>

          <Paper
            sx={{
              ...paperStyle,
              display: activeTab === 2 ? 'flex' : 'none',
              borderRadius: 0,
            }}
          >
            <PreviewPanel />
          </Paper>
        </Box>
      </Box>
    );
  }

  // Desktop layout with resizable panels
  return (
    <Box
      ref={containerRef}
      sx={{
        height: '100%',
        display: 'flex',
        position: 'relative',
        p: 3,
        gap: 2,
        cursor: isResizing ? 'col-resize' : 'default',
      }}
    >
      {/* Layers Panel */}
      <Box sx={{ width: `${panelSizes.layers}%`, height: '100%' }}>
        <Paper sx={paperStyle}>
          <LayersPanel />
        </Paper>
      </Box>

      {/* Left Resize Handle */}
      <Box
        sx={{
          ...resizeHandleStyle,
          left: `${panelSizes.layers}%`,
          transform: 'translateX(-50%)',
        }}
        onMouseDown={handleLeftDragStart}
      >
        <DragHandleIcon
          className="drag-icon"
          sx={{
            color: 'text.secondary',
            opacity: 0.3,
            fontSize: '16px',
            transform: 'rotate(90deg)',
            transition: 'opacity 0.2s',
          }}
        />
      </Box>

      {/* Traits Panel */}
      <Box sx={{ width: `${panelSizes.traits}%`, height: '100%' }}>
        <Paper sx={paperStyle}>
          <TraitsPanel />
        </Paper>
      </Box>

      {/* Right Resize Handle */}
      <Box
        sx={{
          ...resizeHandleStyle,
          left: `${panelSizes.layers + panelSizes.traits}%`,
          transform: 'translateX(-50%)',
        }}
        onMouseDown={handleRightDragStart}
      >
        <DragHandleIcon
          className="drag-icon"
          sx={{
            color: 'text.secondary',
            opacity: 0.3,
            fontSize: '16px',
            transform: 'rotate(90deg)',
            transition: 'opacity 0.2s',
          }}
        />
      </Box>

      {/* Preview Panel */}
      <Box sx={{ width: `${panelSizes.preview}%`, height: '100%' }}>
        <Paper sx={paperStyle}>
          <PreviewPanel />
        </Paper>
      </Box>
    </Box>
  );
};

export default PanelContainer;
